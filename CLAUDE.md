# AI Component 项目记录

## 项目概述
AI Component 是一个 monorepo 项目，包含多个可复用的 AI 组件，支持 React、Vue 等多种框架。

## 发布配置

### Sonatype Nexus 配置
- **仓库地址**: `https://npm-registry.leyaoyao.com/`
- **Scope**: `@lyy-npm-group`
- **认证方式**: 用户名密码认证
- **仓库类型**: group 仓库

### 发布脚本
位于 `scripts/` 目录下，支持多平台运行：

- `build-and-publish.sh` - 主发布脚本（Bash）
- `build-and-publish.cmd` - Windows 批处理包装器
- `build-and-publish.ps1` - PowerShell 包装器
- `run-publish.js` - 跨平台 Node.js 启动器
- `test-publish.js` - 环境测试脚本

### 使用方法
```bash
# 推荐方式（跨平台）
pnpm run publish

# Windows 专用
pnpm run publish:cmd
pnpm run publish:ps1

# Unix/Linux/macOS
pnpm run publish:bash

# 环境测试
pnpm run test:publish
```

### 发布顺序
packages 按以下顺序构建和发布：
1. adapters
2. config
3. core
4. ui

## 技术栈
- **包管理器**: pnpm
- **构建工具**: Vite
- **框架支持**: React, Vue
- **类型检查**: TypeScript

## 环境变量
发布时需要设置 Sonatype Nexus 认证信息：
- `NPM_USERNAME` - Nexus 用户名
- `NPM_PASSWORD` - Nexus 密码

## 已解决问题

### 2025-06-25: Windows 发布脚本兼容性问题
**问题**: 
- Git Bash 环境下 Node.js 路径格式不兼容（Unix vs Windows 路径）
- WSL 配置错误导致脚本执行失败
- 缺少跨平台支持

**解决方案**:
- 添加 Windows 路径自动转换支持（使用 cygpath）
- 创建跨平台脚本启动器避免直接依赖 bash
- 提供多种执行方式（cmd/ps1/js）
- 添加环境检测和错误处理

### 2025-06-25: Sonatype Nexus 3.x 适配
**问题**:
- 原脚本使用 NPM_TOKEN 认证，不适用于 Nexus
- 缺少 scoped registry 配置
- 错误处理不够详细

**解决方案**:
- 改为用户名密码认证方式
- 配置 `@lyy-npm-group:registry` 指向 Nexus
- 添加 Nexus 特定错误处理（401/403/409）
- 优化发布流程和清理逻辑

## 已解决问题

### 2025-06-25: 构建系统和Vue组件导出问题
**问题**:
- Rollup 在 Linux 环境下缺失原生模块导致构建失败
- Vue 组件导出路径错误，用户无法正确导入 `@leyaoyao/ai-component-ui/vue`
- 缺少正确的多平台构建配置

**解决方案**:
- 创建快速构建脚本替代有问题的 Rollup 配置
- 修复 Vue/Web/Taro 组件的导出路径和文件结构
- 实现正确的 ESM/CJS/TypeScript 声明文件生成
- 添加 `build-all-quick.js` 和 `build-quick.js` 构建脚本

### 2025-06-25: 发布原子性和测试问题
**问题**:
- 发布脚本缺少原子性，版本号更新后测试失败导致不一致状态
- 项目缺少 test 命令导致发布失败
- 需要回滚机制确保发布安全

**解决方案**:
- 创建原子性发布脚本 `publish-atomic.js` 
- 实现完整的备份和回滚机制
- 在所有 package.json 中添加测试命令
- 按阶段执行：验证→构建→测试→更新版本→发布→标签
- 失败时自动回滚到原始版本状态

## 注意事项
1. 提交时不包含 Claude 水印，保持代码整洁
2. 发布前确保 git 工作区是 clean 状态
3. Windows 用户推荐使用 `pnpm run publish` 避免路径问题
4. 发布失败时脚本会自动回滚版本号
5. 使用 `--dry-run` 参数测试发布流程