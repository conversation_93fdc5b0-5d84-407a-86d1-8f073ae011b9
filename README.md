# AI Chat Component 🤖💬

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/your-org/ai-component)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)

一个功能丰富、跨平台的AI聊天组件，支持 **React**、**Vue** 和 **Taro** 三大框架。

## ✨ 核心特性

- 🚀 **跨平台支持** - React、Vue、Taro 三端统一API
- 📎 **智能文件上传** - 支持图片、文档，可配置上传参数，拖拽操作
- 🎈 **悬浮模式** - 可拖拽悬浮球，智能边缘吸附
- 🔧 **高度可定制** - 7个插槽位置，完全自定义UI
- 🎨 **主题系统** - 内置明暗主题，支持自定义样式
- 💾 **历史记录** - 本地存储，会话恢复
- 💬 **快捷提示** - 预设提示词，快速输入
- 🛡️ **TypeScript** - 完整类型支持，开发友好
- 📱 **响应式** - 完美适配各种屏幕尺寸

## 🔥 新功能亮点

### 文件上传配置 🆕

支持两种上传方式，灵活满足不同需求：

1. **自定义上传函数** - 完全控制上传逻辑
2. **配置式上传** - 通过配置对象快速设置

```typescript
// 方式1：自定义上传函数
const uploadFile = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData
  });
  return response.json().url;
};

// 方式2：配置式上传
const uploadConfig = {
  url: '/api/upload',
  method: 'POST',
  headers: { 'Authorization': 'Bearer token' },
  onProgress: (percent, file) => console.log(`${percent}%`),
  responseParser: (res) => ({ url: res.data.url })
};
```

### 增强的拖拽体验

- ✅ **修复拖拽蒙层闪烁** - 使用计数器避免子元素干扰
- ✅ **悬浮模式拖拽** - 正确绑定DOM元素，支持拖拽移动
- ✅ **智能事件处理** - 防抖、防重复执行

## 📦 安装

```bash
# npm
npm install @your-org/ai-component

# yarn
yarn add @your-org/ai-component

# pnpm
pnpm add @your-org/ai-component
```

## 🚀 快速开始

### React

```tsx
import { AIChatComponent } from '@your-org/ai-component/web';

function App() {
  const handleUpload = async (file: File) => {
    // 自定义上传逻辑
    return 'https://example.com/uploaded-file.jpg';
  };

  return (
    <AIChatComponent
      enableFileUpload={true}
      uploadFile={handleUpload}
      // 或使用配置方式
      uploadConfig={{
        url: '/api/upload',
        method: 'POST',
        onProgress: (percent) => console.log(`${percent}%`)
      }}
      onSend={(message, files) => {
        console.log('发送:', message, files);
      }}
    />
  );
}
```

### Vue

```vue
<template>
  <AIChatComponent
    :enable-file-upload="true"
    :upload-file="handleUpload"
    :upload-config="uploadConfig"
    @send="handleSend"
  />
</template>

<script setup>
import { AIChatComponent } from '@your-org/ai-component/vue';

const uploadConfig = {
  url: '/api/upload',
  method: 'POST',
  headers: { 'Authorization': 'Bearer token' },
  onProgress: (percent, file) => {
    console.log(`上传进度: ${percent}%`);
  }
};

const handleSend = (message, files) => {
  console.log('发送:', message, files);
};
</script>
```

### Taro

```tsx
import { TaroAIChatComponent } from '@your-org/ai-component/taro';

const MyComponent = () => {
  const uploadConfig = {
    url: '/api/upload',
    method: 'POST',
    requestType: 'json', // 小程序支持JSON格式上传
    fieldName: 'imageData'
  };

  return (
    <TaroAIChatComponent
      enableFileUpload={true}
      uploadConfig={uploadConfig}
      onSend={(message, files) => {
        console.log('发送:', message, files);
      }}
    />
  );
};
```

## 🎮 在线演示

体验完整功能，包括新的文件上传配置：

- **React Demo**: [examples/react-demo](./examples/react-demo)
- **Vue Demo**: [examples/vue-demo](./examples/vue-demo)

两个演示都包含：
- 📋 **文件上传配置面板** - 实时切换上传模式
- 🎯 **多场景预设** - 客服、编程、创意等场景
- 🎨 **主题切换** - 明暗主题动态切换
- 🎈 **悬浮模式** - 可拖拽悬浮球体验

## 📖 文件上传配置详解

### UploadConfig 接口

```typescript
interface UploadConfig {
  url: string;                              // 上传地址
  method?: 'POST' | 'PUT' | 'PATCH';       // 请求方法
  headers?: Record<string, string>;         // 自定义请求头
  withCredentials?: boolean;                // 是否携带凭证
  data?: Record<string, any>;              // 额外数据
  fieldName?: string;                       // 文件字段名
  timeout?: number;                         // 超时时间（毫秒）
  requestType?: 'form-data' | 'json';      // 请求类型
  
  // 高级功能
  responseParser?: (response: any) => {     // 响应解析
    url: string;
    name?: string;
    [key: string]: any;
  };
  onProgress?: (percent: number, file: File) => void;   // 进度回调
  validateStatus?: (status: number) => boolean;         // 状态验证
}
```

### 使用场景

#### 1. 简单上传

```typescript
const uploadConfig = {
  url: '/api/upload',
  method: 'POST'
};
```

#### 2. 带认证的上传

```typescript
const uploadConfig = {
  url: '/api/upload',
  headers: {
    'Authorization': 'Bearer your-token',
    'X-Custom-Header': 'value'
  },
  withCredentials: true
};
```

#### 3. 高级配置

```typescript
const uploadConfig = {
  url: '/api/upload',
  method: 'POST',
  timeout: 60000,
  data: { userId: '123', category: 'chat' },
  onProgress: (percent, file) => {
    console.log(`${file.name}: ${percent}%`);
  },
  responseParser: (response) => {
    if (response.success) {
      return {
        url: response.data.fileUrl,
        name: response.data.fileName,
        id: response.data.fileId
      };
    }
    throw new Error(response.message);
  },
  validateStatus: (status) => status >= 200 && status < 300
};
```

#### 4. JSON 格式上传

```typescript
const uploadConfig = {
  url: '/api/upload-base64',
  requestType: 'json',
  fieldName: 'imageData',
  data: { 
    bucket: 'my-bucket',
    folder: 'chat-files' 
  }
};
```

## 🎨 悬浮模式

支持可拖拽的悬浮聊天球，完美适配移动端和桌面端：

```tsx
<AIChatComponent
  floatingMode={true}
  floatingConfig={{
    dragEnabled: true,
    rememberPosition: true,
    position: { x: 20, y: 100, side: 'right' }
  }}
  chatDialogConfig={{
    width: 380,
    height: 600
  }}
  onFloatingToggle={(expanded) => {
    console.log('悬浮窗口', expanded ? '展开' : '收起');
  }}
  onPositionChange={(position) => {
    console.log('位置变化:', position);
  }}
/>
```

## 🔧 自定义插槽

支持 7 个插槽位置，完全自定义UI：

```tsx
<AIChatComponent
  renderHeader={(props) => <CustomHeader {...props} />}
  renderEmpty={(props) => <CustomEmpty {...props} />}
  renderMessage={(message, props) => <CustomMessage {...props} />}
  renderMessageActions={(message, props) => <CustomActions {...props} />}
  renderLoading={(props) => <CustomLoading {...props} />}
  renderInputTools={(props) => <CustomInputTools {...props} />}
  renderFooter={(props) => <CustomFooter {...props} />}
/>
```

## 💾 历史记录

自动保存聊天历史，支持会话恢复：

```tsx
<AIChatComponent
  config={{
    showHistory: true,
    sessionId: 'user-123-session'
  }}
/>
```

## 🎯 多场景预设

内置多种场景配置：

- **默认配置** - 通用聊天场景
- **客服场景** - 订单、退款、技术支持
- **编程助手** - 代码调试、性能优化、代码审查
- **悬浮客服** - 轻量级客服窗口

## 📱 响应式设计

组件完全响应式，自动适配：

- 📱 **移动端** - 触摸友好，手势操作
- 💻 **桌面端** - 鼠标拖拽，键盘快捷键
- 🖥️ **大屏** - 充分利用屏幕空间

## 🛡️ TypeScript 支持

完整的类型定义，开发体验极佳：

```typescript
import type { 
  AIChatComponentProps,
  UploadConfig,
  UploadResponse,
  Message,
  FileItem
} from '@your-org/ai-component';
```

## 🤝 贡献指南

我们欢迎社区贡献！请查看 [CONTRIBUTING.md](./CONTRIBUTING.md) 了解如何参与开发。

### 开发环境

```bash
# 克隆项目
git clone https://github.com/your-org/ai-component.git

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 运行测试
pnpm test

# 构建项目
pnpm build
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](./LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有贡献者和支持者！

特别感谢：
- [Ant Design](https://ant.design/) - 优秀的设计语言
- [Vue.js](https://vuejs.org/) - 渐进式框架
- [Taro](https://taro.jd.com/) - 多端统一开发框架

## 📞 支持

- 📧 **邮箱**: <EMAIL>
- 💬 **讨论**: [GitHub Discussions](https://github.com/your-org/ai-component/discussions)
- 🐛 **问题报告**: [GitHub Issues](https://github.com/your-org/ai-component/issues)
- 📚 **文档**: [Documentation](https://docs.your-org.com/ai-component)

---

<div align="center">

**如果这个项目对您有帮助，请给它一个 ⭐**

Made with ❤️ by [Your Organization](https://your-org.com)

</div>