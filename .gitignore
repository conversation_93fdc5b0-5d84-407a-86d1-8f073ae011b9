# Dependencies
/node_modules
node_modules/
**/node_modules/
/.pnp
.pnp.js

# Production builds
/dist
/build
**/dist/
**/build/
/.next/
/out/

# Development
/.env.local
/.env.development.local  
/.env.test.local
/.env.production.local
/.umirc.local.ts
/config/config.local.ts
/src/.umi
/src/.umi-production
/src/.umi-test
.swc

# Cache directories
.cache/
.parcel-cache/
.vite/
.turbo/
.eslintcache
.stylelintcache

# IDE and editors
.idea/
.vscode/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
storybook-static

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Lock files (consider keeping pnpm-lock.yaml for consistency)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Project specific
.promptx
custom-memory-bank/*
.cursor
.claude

# Taro specific
.temp/
.rn_temp/

# Mobile development
*.apk
*.ipa
*.aab

# Testing
/coverage
.nyc_output

# TypeScript
*.tsbuildinfo

# Rollup
.rollup.cache

# Vite
.vite/
vite.config.*.timestamp-*

# Local environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
