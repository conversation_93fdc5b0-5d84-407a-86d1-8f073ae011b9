{"name": "@leyaoyao/ai-component", "version": "0.0.6", "author": "chenweijian <<EMAIL>>", "browserslist": ["last 2 versions", ">1%", "not ie <= 11"], "scripts": {"dev": "pnpm run demo:react & pnpm run demo:vue", "build": "node build.js", "build:dev": "node build.js --mode development", "build:watch": "node build.js --watch", "build:analyze": "node build.js --analyze", "preview": "vite preview", "test": "echo \"Warning: no tests specified\" && exit 0", "test:packages": "pnpm --filter=@leyaoyao/ai-component-* test", "optimize": "node scripts/optimize-bundle.js", "optimize:treeshaking": "node scripts/optimize-treeshaking.js", "validate": "node scripts/validate-build.js", "validate:strict": "node scripts/validate-build.js --strict --verbose", "clean": "pnpm --filter=@leyaoyao/ai-component-* run clean", "demo:react": "cd examples/react-demo && pnpm dev", "demo:vue": "cd examples/vue-demo && pnpm dev", "demo:install": "cd examples/react-demo && pnpm install && cd ../vue-demo && pnpm install", "publish": "node scripts/publish-atomic.js", "publish:test": "node scripts/test-publish.js", "version:check": "node scripts/version-manager.js check", "version:update": "node scripts/version-manager.js update", "pre-publish": "node scripts/pre-publish-check.js"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/icons-vue": "^7.0.1", "@ant-design/x": "^1.0.6", "@leyaoyao/prettier-config": "^1.0.7", "@leyaoyao/qt-hook": "^0.0.30", "@nutui/nutui-react-taro": "^3.0.14", "@tarojs/components": "^4.1.3-alpha.0", "@tarojs/taro": "^4.1.3-alpha.0", "@traptitech/markdown-it-katex": "^3.6.0", "ant-design-vue": "^4.2.6", "antd": "^5.24.3", "antd-mobile": "^5.39.0", "antd-style": "^3.7.1", "echarts": "^5.6.0", "highlight.js": "^11.10.0", "katex": "^0.16.11", "markdown-it-anchor": "^9.0.0", "markdown-it-highlightjs": "^4.2.0", "markdown-it-toc-done-right": "^4.2.0", "normalize.css": "^8.0.1"}, "devDependencies": {"@eslint/js": "^9.28.0", "@rollup/plugin-babel": "^6.0.0", "@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.0.0", "@rollup/rollup-linux-x64-gnu": "^4.44.0", "@types/markdown-it": "^14.1.2", "@types/node": "^24.0.0", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.21", "axios": "^1.8.1", "brotli-size": "^4.0.0", "chokidar": "^3.5.3", "cross-env": "^7.0.3", "cssnano": "^6.0.0", "dayjs": "^1.11.13", "gzip-size": "^7.0.0", "html-react-parser": "^5.2.2", "less": "^4.3.0", "markdown-it": "^14.1.0", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "rimraf": "^5.0.0", "rollup": "^4.0.0", "rollup-plugin-bundle-analyzer": "^1.0.0", "rollup-plugin-dts": "^6.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^5.0.0", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "semver": "^7.7.2", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.5.3", "vite": "^6.3.5", "vite-plugin-style-import": "^2.0.0", "vue": "^3.4.0", "vue-tsc": "^2.2.10"}, "publishConfig": {"@leyaoyao:registry": "https://npm-registry.leyaoyao.com/repository/lyy-npm-hosted/"}}