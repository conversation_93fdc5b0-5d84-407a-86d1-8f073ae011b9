#!/usr/bin/env node

/**
 * Atomic publish script with rollback mechanism
 * This script ensures all-or-nothing publishing with proper cleanup
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const COMPANY_REGISTRY = 'https://npm-registry.leyaoyao.com/repository/lyy-npm-hosted/';
const COMPANY_SCOPE = '@leyaoyao';
const PACKAGES_BUILD_ORDER = ['adapters', 'config', 'core', 'ui'];

// Colors
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) { log(`[ERROR] ${message}`, 'red'); }
function logSuccess(message) { log(`[SUCCESS] ${message}`, 'green'); }
function logInfo(message) { log(`[INFO] ${message}`, 'blue'); }
function logWarning(message) { log(`[WARNING] ${message}`, 'yellow'); }
function logStep(message) { log(`\n[STEP] ${message}`, 'cyan'); }

function execCommand(command, options = {}) {
    try {
        return execSync(command, { encoding: 'utf8', stdio: 'inherit', ...options });
    } catch (error) {
        throw new Error(`Command failed: ${command}\n${error.message}`);
    }
}

// State management for rollback
let originalVersions = {};
let backupCreated = false;

function createBackup() {
    logStep('Creating backup of current versions...');
    
    // Backup root package.json
    const rootPath = path.join(process.cwd(), 'package.json');
    originalVersions.root = JSON.parse(fs.readFileSync(rootPath, 'utf8'));
    
    // Backup all package versions
    const packagesDir = path.join(process.cwd(), 'packages');
    originalVersions.packages = {};
    
    for (const pkg of PACKAGES_BUILD_ORDER) {
        const pkgPath = path.join(packagesDir, pkg, 'package.json');
        if (fs.existsSync(pkgPath)) {
            originalVersions.packages[pkg] = JSON.parse(fs.readFileSync(pkgPath, 'utf8'));
        }
    }
    
    backupCreated = true;
    logSuccess('Backup created successfully');
}

function rollback() {
    if (!backupCreated) {
        logWarning('No backup available for rollback');
        return;
    }
    
    logStep('Rolling back to original versions...');
    
    try {
        // Restore root package.json
        const rootPath = path.join(process.cwd(), 'package.json');
        fs.writeFileSync(rootPath, JSON.stringify(originalVersions.root, null, 2) + '\n');
        
        // Restore all packages
        const packagesDir = path.join(process.cwd(), 'packages');
        for (const pkg of PACKAGES_BUILD_ORDER) {
            if (originalVersions.packages[pkg]) {
                const pkgPath = path.join(packagesDir, pkg, 'package.json');
                fs.writeFileSync(pkgPath, JSON.stringify(originalVersions.packages[pkg], null, 2) + '\n');
            }
        }
        
        logSuccess('Successfully rolled back to original versions');
    } catch (error) {
        logError(`Rollback failed: ${error.message}`);
    }
}

function readPackageJson(filePath) {
    return JSON.parse(fs.readFileSync(filePath, 'utf8'));
}

function writePackageJson(filePath, data) {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2) + '\n');
}

function getCurrentVersion() {
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    return readPackageJson(packageJsonPath).version;
}

function incrementVersion(version, type = 'patch') {
    const parts = version.split('.');
    const major = parseInt(parts[0]) || 0;
    const minor = parseInt(parts[1]) || 0;
    const patch = parseInt(parts[2]) || 0;

    switch (type) {
        case 'major': return `${major + 1}.0.0`;
        case 'minor': return `${major}.${minor + 1}.0`;
        default: return `${major}.${minor}.${patch + 1}`;
    }
}

function updateVersions(newVersion) {
    logStep(`Updating versions to ${newVersion}...`);
    
    // Update root package.json
    const rootPath = path.join(process.cwd(), 'package.json');
    const rootPkg = readPackageJson(rootPath);
    rootPkg.version = newVersion;
    writePackageJson(rootPath, rootPkg);
    
    // Update packages
    const packagesDir = path.join(process.cwd(), 'packages');
    for (const pkg of PACKAGES_BUILD_ORDER) {
        const pkgPath = path.join(packagesDir, pkg, 'package.json');
        if (fs.existsSync(pkgPath)) {
            const packageData = readPackageJson(pkgPath);
            packageData.version = newVersion;
            
            // Update workspace dependencies
            ['dependencies', 'devDependencies', 'peerDependencies'].forEach(depType => {
                if (packageData[depType]) {
                    Object.keys(packageData[depType]).forEach(dep => {
                        if (dep.startsWith(`${COMPANY_SCOPE}/ai-component-`) && 
                            packageData[depType][dep].startsWith('^0.0.')) {
                            packageData[depType][dep] = `^${newVersion}`;
                        }
                    });
                }
            });
            
            writePackageJson(pkgPath, packageData);
            logInfo(`Updated ${COMPANY_SCOPE}/ai-component-${pkg}`);
        }
    }
    
    logSuccess('Version update completed');
}

async function main() {
    log('🚀 AI Component Atomic Build & Publish Script', 'cyan');
    log('===============================================', 'cyan');
    
    const args = process.argv.slice(2);
    const isDryRun = args.includes('--dry-run');
    const skipTests = args.includes('--skip-tests');
    const customVersion = args.find(arg => !arg.startsWith('--') && !['daizhitao', 'daizhitao123'].includes(arg));
    
    // Extract credentials from args
    const credentialArgs = args.filter(arg => ['daizhitao', 'daizhitao123'].includes(arg));
    const username = credentialArgs[0] || process.env.NPM_USERNAME;
    const password = credentialArgs[1] || process.env.NPM_PASSWORD;
    
    try {
        // Check credentials
        if (!username || !password) {
            logError('Missing NPM credentials.');
            console.log('Usage: node scripts/publish-atomic.js <username> <password> [options]');
            console.log('Example: node scripts/publish-atomic.js daizhitao daizhitao123 --dry-run');
            process.exit(1);
        }
        
        logInfo(`Using credentials for user: ${username}`);
        
        // Create backup before any changes
        createBackup();
        
        // Version management
        const currentVersion = getCurrentVersion();
        const newVersion = customVersion || incrementVersion(currentVersion);
        logInfo(`Version: ${currentVersion} → ${newVersion}`);
        
        // Phase 1: Pre-flight checks (no modifications yet)
        logStep('Phase 1: Pre-flight validation...');
        
        // Check git status
        try {
            const gitStatus = execSync('git status --porcelain', { encoding: 'utf8' });
            if (gitStatus.trim() && !isDryRun) {
                logWarning('Git working directory is not clean');
                logInfo('Use --dry-run to test or commit changes first');
            }
        } catch (error) {
            logWarning('Git status check failed (non-fatal)');
        }
        
        // Validate npm credentials
        if (!isDryRun) {
            const registryHost = COMPANY_REGISTRY.replace(/^https?:\/\//, '').replace(/\/$/, '');
            const authString = Buffer.from(`${username}:${password}`).toString('base64');
            execCommand(`npm config set //${registryHost}/:_auth ${authString}`);
            logInfo('NPM authentication configured');
        }
        
        // Phase 2: Dependencies and build (reversible operations)
        logStep('Phase 2: Install dependencies...');
        execCommand('pnpm install');
        
        logStep('Phase 3: Build packages...');
        execCommand('pnpm run build:packages:clean');
        
        // Phase 4: Run tests (before version changes)
        if (!skipTests) {
            logStep('Phase 4: Running tests...');
            try {
                execCommand('pnpm test');
                logSuccess('All tests passed');
            } catch (error) {
                logError('Tests failed! Aborting publish to maintain consistency.');
                logInfo('Use --skip-tests to bypass tests (not recommended)');
                throw new Error('Test failure');
            }
        }
        
        // Phase 5: Version update (first irreversible operation)
        if (!isDryRun) {
            logStep('Phase 5: Updating versions...');
            updateVersions(newVersion);
        }
        
        // Phase 6: Publish packages
        logStep(`Phase 6: ${isDryRun ? '[DRY RUN] ' : ''}Publishing packages...`);
        
        const packagesDir = path.join(process.cwd(), 'packages');
        
        for (const pkg of PACKAGES_BUILD_ORDER) {
            const pkgDir = path.join(packagesDir, pkg);
            const pkgJsonPath = path.join(pkgDir, 'package.json');
            
            if (fs.existsSync(pkgJsonPath)) {
                const packageData = readPackageJson(pkgJsonPath);
                const packageName = `${COMPANY_SCOPE}/ai-component-${pkg}`;
                
                logInfo(`${isDryRun ? '[DRY RUN] ' : ''}Publishing ${packageName}@${packageData.version}...`);
                
                if (!isDryRun) {
                    try {
                        execCommand(`npm publish --registry="${COMPANY_REGISTRY}"`, { cwd: pkgDir });
                        logSuccess(`Published ${packageName}@${packageData.version}`);
                    } catch (error) {
                        logError(`Failed to publish ${packageName}`);
                        logError('CRITICAL: Partial publish detected! Manual intervention may be required.');
                        throw error;
                    }
                }
            }
        }
        
        // Phase 7: Git operations (final step)
        if (!isDryRun) {
            logStep('Phase 7: Creating git tag...');
            try {
                execCommand('git add -A');
                execCommand(`git commit -m "chore: release v${newVersion}"`);
                execCommand(`git tag -a v${newVersion} -m "Release v${newVersion}"`);
                logSuccess(`Created tag v${newVersion}`);
            } catch (error) {
                logWarning('Git tag creation failed (non-fatal)');
            }
        }
        
        // Success
        log('\n✅ Publish completed successfully!', 'green');
        log('\n📦 Published packages:', 'blue');
        for (const pkg of PACKAGES_BUILD_ORDER) {
            console.log(`  - ${COMPANY_SCOPE}/ai-component-${pkg}@${newVersion}`);
        }
        
        log('\n💡 Installation:', 'blue');
        console.log(`  npm config set ${COMPANY_SCOPE}:registry ${COMPANY_REGISTRY}`);
        console.log(`  npm install ${COMPANY_SCOPE}/ai-component-ui@${newVersion}`);
        
    } catch (error) {
        logError(`Publish failed: ${error.message}`);
        
        if (!isDryRun && backupCreated) {
            rollback();
        }
        
        process.exit(1);
    } finally {
        // Cleanup
        try {
            const registryHost = COMPANY_REGISTRY.replace(/^https?:\/\//, '').replace(/\/$/, '');
            execCommand(`npm config delete //${registryHost}/:_auth`);
        } catch {
            // Ignore cleanup errors
        }
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main };