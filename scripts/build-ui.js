#!/usr/bin/env node

/**
 * UI包构建脚本 - 支持多平台 (Web/Vue/Taro)
 * 包含样式处理、代码分割、性能优化
 */

const path = require('path')
const fs = require('fs')
const { rollup } = require('rollup')
const typescript = require('@rollup/plugin-typescript')
const resolve = require('@rollup/plugin-node-resolve')
const commonjs = require('@rollup/plugin-commonjs')
const terser = require('@rollup/plugin-terser')
const postcss = require('rollup-plugin-postcss')
const { dts } = require('rollup-plugin-dts')
const vue = require('@vitejs/plugin-vue')
const { getBabelOutputPlugin } = require('@rollup/plugin-babel')

const {
  BUILD_CONFIG,
  getPackageInfo,
  generateExternals,
  generateGlobals
} = require('../build.config')

class UIPackageBuilder {
  constructor(packagePath, options = {}) {
    this.packagePath = packagePath
    this.options = {
      mode: 'production',
      platform: 'web',
      watch: false,
      verbose: false,
      ...options
    }
    
    this.packageInfo = getPackageInfo('ui')
    this.distPath = path.join(packagePath, 'dist')
    this.platform = this.options.platform
  }

  async build() {
    console.log(`      🎨 构建UI包 - ${this.platform}平台`)
    
    try {
      // 确保输出目录存在
      const platformDistPath = path.join(this.distPath, this.platform)
      if (!fs.existsSync(platformDistPath)) {
        fs.mkdirSync(platformDistPath, { recursive: true })
      }
      
      // 根据平台选择构建策略
      switch (this.platform) {
        case 'web':
          await this.buildWebPlatform()
          break
        case 'vue':
          await this.buildVuePlatform()
          break
        case 'taro':
          await this.buildTaroPlatform()
          break
        default:
          throw new Error(`不支持的平台: ${this.platform}`)
      }
      
      // 构建样式文件
      await this.buildStyles()
      
      // 构建类型声明
      await this.buildTypeDeclarations()
      
      console.log(`      ✅ UI包 ${this.platform}平台构建完成`)
      
    } catch (error) {
      console.error(`      ❌ UI包 ${this.platform}平台构建失败:`, error.message)
      throw error
    }
  }

  // 构建Web平台
  async buildWebPlatform() {
    const inputPath = path.join(this.packagePath, 'web/index.ts')
    const outputDir = path.join(this.distPath, 'web')
    
    await this.buildPlatformBundle(inputPath, outputDir, 'web')
    
    // 构建单个组件（支持按需引入）
    await this.buildWebComponents()
  }

  // 构建Vue平台
  async buildVuePlatform() {
    const inputPath = path.join(this.packagePath, 'vue/index.ts')
    const outputDir = path.join(this.distPath, 'vue')
    
    await this.buildPlatformBundle(inputPath, outputDir, 'vue')
    
    // 构建Vue组件
    await this.buildVueComponents()
  }

  // 构建Taro平台
  async buildTaroPlatform() {
    const inputPath = path.join(this.packagePath, 'taro/index.ts')
    const outputDir = path.join(this.distPath, 'taro')
    
    await this.buildPlatformBundle(inputPath, outputDir, 'taro')
    
    // 构建Taro组件
    await this.buildTaroComponents()
  }

  // 构建平台Bundle
  async buildPlatformBundle(inputPath, outputDir, platform) {
    const externals = generateExternals(this.packageInfo, platform)
    const globals = generateGlobals(externals, platform)
    
    // 调试信息
    if (this.options.verbose) {
      console.log(`        🔍 Externals for ${platform}:`, externals)
      console.log(`        🌐 Globals for ${platform}:`, globals)
    }
    
    const inputOptions = {
      input: inputPath,
      external: externals,
      plugins: this.getPlatformPlugins(platform)
    }
    
    const formats = [
      { 
        format: 'es', 
        file: `${outputDir}/index.esm.js`,
        inlineDynamicImports: true,
        sourcemap: this.options.mode === 'development'
      },
      { 
        format: 'cjs', 
        file: `${outputDir}/index.cjs.js`,
        inlineDynamicImports: true,
        sourcemap: this.options.mode === 'development'
      }
    ]
    
    // 生产环境添加UMD格式
    if (this.options.mode === 'production') {
      formats.push({
        format: 'umd',
        file: `${outputDir}/index.umd.js`,
        name: `AIComponentUI${platform.charAt(0).toUpperCase() + platform.slice(1)}`,
        globals,
        inlineDynamicImports: true,
        sourcemap: false
      })
    }
    
    const bundle = await rollup(inputOptions)
    
    for (const outputOptions of formats) {
      await bundle.write(outputOptions)
      if (this.options.verbose) {
        console.log(`        📦 Generated ${path.relative(this.distPath, outputOptions.file)}`)
      }
    }
    
    await bundle.close()
  }

  // 获取平台特定插件
  getPlatformPlugins(platform) {
    const externals = generateExternals(this.packageInfo, platform)
    
    const commonPlugins = [
      resolve({
        preferBuiltins: false,
        browser: true,
        extensions: ['.ts', '.tsx', '.js', '.jsx'],
        // 不解析 node_modules 中的外部依赖
        skip: externals
      }),
      commonjs(),
      postcss({
        extract: true, // 提取 CSS 到单独文件
        minimize: this.options.mode === 'production',
        sourceMap: this.options.mode === 'development'
      }),
      typescript({
        tsconfig: this.getTsConfigPath(platform),
        declaration: false, // 让 Rollup 处理输出，不让 TypeScript 直接输出
        declarationMap: false,
        noEmitOnError: false // 允许有错误时继续构建
      })
    ]
    
    // Vue平台特定插件
    if (platform === 'vue') {
      commonPlugins.push(vue())
    }
    
    // Taro平台特定处理
    if (platform === 'taro') {
      commonPlugins.push(
        getBabelOutputPlugin({
          presets: [
            ['@babel/preset-env', { targets: { node: '10' } }]
          ]
        })
      )
    }
    
    // 生产环境优化
    if (this.options.mode === 'production') {
      commonPlugins.push(
        terser({
          ...BUILD_CONFIG.OPTIMIZATION.terser,
          compress: {
            ...BUILD_CONFIG.OPTIMIZATION.terser.compress,
            // 平台特定优化
            ...(platform === 'taro' && {
              drop_console: false // Taro需要保留console
            })
          }
        })
      )
    }
    
    return commonPlugins
  }

  // 构建Web组件
  async buildWebComponents() {
    const componentsDir = path.join(this.packagePath, 'web/components')
    
    if (!fs.existsSync(componentsDir)) return
    
    const components = this.getComponentFiles(componentsDir)
    
    for (const component of components) {
      await this.buildSingleComponent(component, 'web')
    }
  }

  // 构建Vue组件
  async buildVueComponents() {
    const componentsDir = path.join(this.packagePath, 'vue/components')
    
    if (!fs.existsSync(componentsDir)) return
    
    const components = this.getComponentFiles(componentsDir)
    
    for (const component of components) {
      await this.buildSingleComponent(component, 'vue')
    }
  }

  // 构建Taro组件
  async buildTaroComponents() {
    const componentsDir = path.join(this.packagePath, 'taro/components')
    
    if (!fs.existsSync(componentsDir)) return
    
    const components = this.getComponentFiles(componentsDir)
    
    for (const component of components) {
      await this.buildSingleComponent(component, 'taro')
    }
  }

  // 构建单个组件（支持按需引入）
  async buildSingleComponent(componentPath, platform) {
    const componentName = path.basename(componentPath, path.extname(componentPath))
    const outputDir = path.join(this.distPath, platform, 'components', componentName)
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    const externals = generateExternals(this.packageInfo, platform)
    
    const inputOptions = {
      input: componentPath,
      external: externals,
      plugins: this.getPlatformPlugins(platform)
    }
    
    const formats = [
      { 
        format: 'es', 
        file: `${outputDir}/index.esm.js`,
        inlineDynamicImports: true,
        sourcemap: this.options.mode === 'development'
      },
      { 
        format: 'cjs', 
        file: `${outputDir}/index.cjs.js`,
        inlineDynamicImports: true,
        sourcemap: this.options.mode === 'development'
      }
    ]
    
    const bundle = await rollup(inputOptions)
    
    for (const outputOptions of formats) {
      await bundle.write(outputOptions)
    }
    
    await bundle.close()
    
    if (this.options.verbose) {
      console.log(`        🧩 Generated component: ${componentName}`)
    }
  }

  // 构建样式文件
  async buildStyles() {
    const stylesDir = path.join(this.packagePath, 'shared/ui/style')
    const outputDir = path.join(this.distPath, 'style')
    
    if (!fs.existsSync(stylesDir)) return
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    // 构建主样式文件
    const mainStylePath = path.join(stylesDir, 'index.scss')
    if (fs.existsSync(mainStylePath)) {
      await this.buildStyleBundle(mainStylePath, outputDir)
    }
    
    // 构建平台特定样式
    for (const platform of ['web', 'vue', 'taro']) {
      const platformStylePath = path.join(this.packagePath, platform)
      if (fs.existsSync(platformStylePath)) {
        await this.buildPlatformStyles(platformStylePath, platform)
      }
    }
  }

  // 构建样式Bundle
  async buildStyleBundle(inputPath, outputDir) {
    const bundle = await rollup({
      input: inputPath,
      plugins: [
        postcss({
          extract: path.join(outputDir, 'index.css'),
          minimize: this.options.mode === 'production',
          plugins: [
            require('autoprefixer'),
            ...(this.options.mode === 'production' ? [
              require('cssnano')({
                preset: 'default'
              })
            ] : [])
          ]
        })
      ]
    })
    
    await bundle.generate({ format: 'es' })
    await bundle.close()
    
    if (this.options.verbose) {
      console.log(`        🎨 Generated styles: index.css`)
    }
  }

  // 构建平台特定样式
  async buildPlatformStyles(platformPath, platform) {
    const styleFiles = this.getStyleFiles(platformPath)
    const outputDir = path.join(this.distPath, platform, 'styles')
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    for (const styleFile of styleFiles) {
      const outputName = path.basename(styleFile, path.extname(styleFile)) + '.css'
      
      const bundle = await rollup({
        input: styleFile,
        plugins: [
          postcss({
            extract: path.join(outputDir, outputName),
            minimize: this.options.mode === 'production'
          })
        ]
      })
      
      await bundle.generate({ format: 'es' })
      await bundle.close()
    }
  }

  // 构建类型声明
  async buildTypeDeclarations() {
    const inputPath = path.join(this.packagePath, this.platform, 'index.ts')
    const outputPath = path.join(this.distPath, this.platform, 'index.d.ts')
    
    if (!fs.existsSync(inputPath)) {
      if (this.options.verbose) {
        console.log(`        ⚠️  输入文件不存在: ${inputPath}`)
      }
      return
    }
    
    const bundle = await rollup({
      input: inputPath,
      external: generateExternals(this.packageInfo, this.platform),
      plugins: [dts()]
    })
    
    await bundle.write({
      file: outputPath,
      format: 'es',
      inlineDynamicImports: true
    })
    
    await bundle.close()
    
    if (this.options.verbose) {
      console.log(`        📝 Generated type declarations for ${this.platform}`)
    }
  }

  // 获取组件文件列表
  getComponentFiles(dir) {
    if (!fs.existsSync(dir)) return []
    
    const files = []
    const entries = fs.readdirSync(dir, { withFileTypes: true })
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name)
      
      if (entry.isFile() && /\.(tsx?|vue)$/.test(entry.name)) {
        files.push(fullPath)
      } else if (entry.isDirectory()) {
        const indexFile = path.join(fullPath, 'index.ts')
        if (fs.existsSync(indexFile)) {
          files.push(indexFile)
        }
      }
    }
    
    return files
  }

  // 获取样式文件列表
  getStyleFiles(dir) {
    if (!fs.existsSync(dir)) return []
    
    const files = []
    const walk = (currentDir) => {
      const entries = fs.readdirSync(currentDir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = path.join(currentDir, entry.name)
        
        if (entry.isFile() && /\.(scss|sass|css)$/.test(entry.name)) {
          files.push(fullPath)
        } else if (entry.isDirectory()) {
          walk(fullPath)
        }
      }
    }
    
    walk(dir)
    return files
  }

  // 获取TypeScript配置路径
  getTsConfigPath(platform) {
    const platformConfig = path.join(this.packagePath, `tsconfig.${platform}.json`)
    if (fs.existsSync(platformConfig)) {
      return platformConfig
    }
    
    return path.join(this.packagePath, 'tsconfig.json')
  }
}

// CLI 入口
async function main() {
  const [packagePath, optionsJson] = process.argv.slice(2)
  
  if (!packagePath) {
    console.error('❌ 请提供包路径')
    process.exit(1)
  }
  
  const options = optionsJson ? JSON.parse(optionsJson) : {}
  const builder = new UIPackageBuilder(packagePath, options)
  
  await builder.build()
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ UI包构建失败:', error.message)
    process.exit(1)
  })
}

module.exports = UIPackageBuilder