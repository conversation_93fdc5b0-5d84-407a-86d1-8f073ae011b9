#!/usr/bin/env node

/**
 * Simplified publish script for AI Component
 * Uses environment variables for non-interactive execution
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const COMPANY_REGISTRY = 'https://npm-registry.leyaoyao.com/repository/lyy-npm-hosted/';
const COMPANY_SCOPE = '@leyaoyao';
const PACKAGES_BUILD_ORDER = ['adapters', 'config', 'core', 'ui'];

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) { log(`[ERROR] ${message}`, 'red'); }
function logSuccess(message) { log(`[SUCCESS] ${message}`, 'green'); }
function logInfo(message) { log(`[INFO] ${message}`, 'blue'); }
function logStep(message) { log(`\n[STEP] ${message}`, 'cyan'); }

function execCommand(command, options = {}) {
    try {
        return execSync(command, { encoding: 'utf8', stdio: 'inherit', ...options });
    } catch (error) {
        throw new Error(`Command failed: ${command}`);
    }
}

function readPackageJson(filePath) {
    return JSON.parse(fs.readFileSync(filePath, 'utf8'));
}

function writePackageJson(filePath, data) {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2) + '\n');
}

function getCurrentVersion() {
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    return readPackageJson(packageJsonPath).version;
}

function incrementVersion(version, type = 'patch') {
    const parts = version.split('.');
    const major = parseInt(parts[0]) || 0;
    const minor = parseInt(parts[1]) || 0;
    const patch = parseInt(parts[2]) || 0;

    switch (type) {
        case 'major': return `${major + 1}.0.0`;
        case 'minor': return `${major}.${minor + 1}.0`;
        default: return `${major}.${minor}.${patch + 1}`;
    }
}

function updateVersions(newVersion) {
    logStep(`Updating versions to ${newVersion}...`);
    
    // Update root package.json
    const rootPath = path.join(process.cwd(), 'package.json');
    const rootPkg = readPackageJson(rootPath);
    rootPkg.version = newVersion;
    writePackageJson(rootPath, rootPkg);
    
    // Update packages
    const packagesDir = path.join(process.cwd(), 'packages');
    for (const pkg of PACKAGES_BUILD_ORDER) {
        const pkgPath = path.join(packagesDir, pkg, 'package.json');
        if (fs.existsSync(pkgPath)) {
            const packageData = readPackageJson(pkgPath);
            packageData.version = newVersion;
            
            // Update workspace dependencies
            ['dependencies', 'devDependencies', 'peerDependencies'].forEach(depType => {
                if (packageData[depType]) {
                    Object.keys(packageData[depType]).forEach(dep => {
                        if (dep.startsWith(`${COMPANY_SCOPE}/ai-component-`) && 
                            packageData[depType][dep] === 'workspace:*') {
                            packageData[depType][dep] = `^${newVersion}`;
                        }
                    });
                }
            });
            
            writePackageJson(pkgPath, packageData);
            logInfo(`Updated ${COMPANY_SCOPE}/ai-component-${pkg}`);
        }
    }
    
    logSuccess('Version update completed');
}

async function main() {
    log('🚀 AI Component Build & Publish Script', 'cyan');
    log('=====================================', 'cyan');
    
    const args = process.argv.slice(2);
    const isDryRun = args.includes('--dry-run');
    const skipTests = args.includes('--skip-tests');
    const customVersion = args.find(arg => !arg.startsWith('--'));
    
    try {
        // Check credentials
        let username = process.env.NPM_USERNAME;
        let password = process.env.NPM_PASSWORD;
        
        // Debug: Print all environment variables that start with NPM
        console.log('Debug - Environment variables:');
        Object.keys(process.env).filter(key => key.startsWith('NPM')).forEach(key => {
            console.log(`  ${key}: ${process.env[key] ? '[SET]' : '[NOT SET]'}`);
        });
        
        if (!username || !password) {
            logError('Missing NPM credentials. Please set environment variables:');
            console.log('');
            console.log('Windows Command Prompt:');
            console.log('  set NPM_USERNAME=your-username && set NPM_PASSWORD=your-password && pnpm run publish');
            console.log('');
            console.log('PowerShell:');
            console.log('  $env:NPM_USERNAME="your-username"; $env:NPM_PASSWORD="your-password"; pnpm run publish');
            console.log('');
            console.log('Or use cross-env:');
            console.log('  npx cross-env NPM_USERNAME=your-username NPM_PASSWORD=your-password pnpm run publish');
            process.exit(1);
        }
        
        logInfo(`Using credentials for user: ${username}`);
        
        // Version management
        const currentVersion = getCurrentVersion();
        const newVersion = customVersion || incrementVersion(currentVersion);
        logInfo(`Version: ${currentVersion} → ${newVersion}`);
        
        if (!isDryRun) {
            updateVersions(newVersion);
        }
        
        // Install dependencies
        logStep('Installing dependencies...');
        execCommand('pnpm install');
        
        // Build packages
        logStep('Building packages...');
        execCommand('pnpm run build:packages:clean');
        
        // Run tests
        if (!skipTests) {
            logStep('Running tests...');
            try {
                execCommand('pnpm test');
            } catch (error) {
                logError('Tests failed. Use --skip-tests to skip tests.');
                process.exit(1);
            }
        }
        
        // Publish packages
        logStep(`${isDryRun ? '[DRY RUN] ' : ''}Publishing packages...`);
        
        // Configure npm auth
        if (!isDryRun) {
            const registryHost = COMPANY_REGISTRY.replace(/^https?:\/\//, '').replace(/\/$/, '');
            const authString = Buffer.from(`${username}:${password}`).toString('base64');
            
            execCommand(`npm config set //${registryHost}/:_auth ${authString}`);
            logInfo('NPM authentication configured');
        }
        
        const packagesDir = path.join(process.cwd(), 'packages');
        
        for (const pkg of PACKAGES_BUILD_ORDER) {
            const pkgDir = path.join(packagesDir, pkg);
            const pkgJsonPath = path.join(pkgDir, 'package.json');
            
            if (fs.existsSync(pkgJsonPath)) {
                const packageData = readPackageJson(pkgJsonPath);
                const packageName = `${COMPANY_SCOPE}/ai-component-${pkg}`;
                
                logInfo(`${isDryRun ? '[DRY RUN] ' : ''}Publishing ${packageName}@${packageData.version}...`);
                
                if (!isDryRun) {
                    try {
                        execCommand(`npm publish --registry="${COMPANY_REGISTRY}"`, { cwd: pkgDir });
                        logSuccess(`Published ${packageName}@${packageData.version}`);
                    } catch (error) {
                        logError(`Failed to publish ${packageName}`);
                        throw error;
                    }
                }
            }
        }
        
        // Create git tag
        if (!isDryRun) {
            logStep('Creating git tag...');
            try {
                execCommand('git add -A');
                execCommand(`git commit -m "chore: release v${newVersion}"`);
                execCommand(`git tag -a v${newVersion} -m "Release v${newVersion}"`);
                logSuccess(`Created tag v${newVersion}`);
            } catch (error) {
                logError('Failed to create git tag (this is non-fatal)');
            }
        }
        
        // Success message
        log('\n✅ Publish completed successfully!', 'green');
        log('\n📦 Published packages:', 'blue');
        for (const pkg of PACKAGES_BUILD_ORDER) {
            console.log(`  - ${COMPANY_SCOPE}/ai-component-${pkg}@${newVersion}`);
        }
        
        log('\n💡 Installation:', 'blue');
        console.log(`  npm config set ${COMPANY_SCOPE}:registry ${COMPANY_REGISTRY}`);
        console.log(`  npm install ${COMPANY_SCOPE}/ai-component-ui@${newVersion}`);
        
    } catch (error) {
        logError(`Publish failed: ${error.message}`);
        process.exit(1);
    } finally {
        // Cleanup
        try {
            const registryHost = COMPANY_REGISTRY.replace(/^https?:\/\//, '').replace(/\/$/, '');
            execCommand(`npm config delete //${registryHost}/:_auth`);
        } catch {
            // Ignore cleanup errors
        }
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { main };