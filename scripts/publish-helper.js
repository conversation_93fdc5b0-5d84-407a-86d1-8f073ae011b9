#!/usr/bin/env node

/**
 * Publish Helper - 发布辅助工具
 * 
 * 提供简化的发布命令和工具函数
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

class PublishHelper {
    constructor() {
        this.rootDir = process.cwd();
        this.scriptsDir = path.join(this.rootDir, 'scripts');
    }

    /**
     * 执行命令并返回结果
     */
    exec(command, options = {}) {
        try {
            return execSync(command, {
                cwd: this.rootDir,
                encoding: 'utf8',
                stdio: 'pipe',
                ...options
            });
        } catch (error) {
            throw new Error(`Command failed: ${command}\n${error.message}`);
        }
    }

    /**
     * 快速发布 - patch版本
     */
    async quickPublish() {
        console.log('🚀 快速发布 (patch版本)...');
        
        try {
            // 运行预检查
            console.log('1. 运行预发布检查...');
            this.exec('node scripts/pre-publish-check.js');
            
            // 执行发布
            console.log('2. 执行发布...');
            this.exec('bash scripts/build-and-publish.sh');
            
            console.log('✅ 快速发布完成！');
        } catch (error) {
            console.error('❌ 快速发布失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 测试发布 - dry run
     */
    async testPublish() {
        console.log('🧪 测试发布 (dry run)...');
        
        try {
            this.exec('bash scripts/build-and-publish.sh --dry-run');
            console.log('✅ 测试发布完成！');
        } catch (error) {
            console.error('❌ 测试发布失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 版本发布
     */
    async versionPublish(version) {
        console.log(`🎯 发布版本 ${version}...`);
        
        try {
            // 运行预检查
            console.log('1. 运行预发布检查...');
            this.exec('node scripts/pre-publish-check.js');
            
            // 执行发布
            console.log('2. 执行发布...');
            this.exec(`bash scripts/build-and-publish.sh ${version}`);
            
            console.log('✅ 版本发布完成！');
        } catch (error) {
            console.error('❌ 版本发布失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * Beta发布
     */
    async betaPublish() {
        console.log('🔬 Beta版本发布...');
        
        try {
            // 获取当前版本并生成beta版本
            const currentVersion = this.exec('node scripts/version-manager.js current').trim();
            const betaVersion = this.exec(`node scripts/version-manager.js generate prerelease`).trim();
            
            console.log(`当前版本: ${currentVersion}`);
            console.log(`Beta版本: ${betaVersion}`);
            
            // 执行发布
            this.exec(`bash scripts/build-and-publish.sh ${betaVersion}`);
            
            console.log('✅ Beta版本发布完成！');
        } catch (error) {
            console.error('❌ Beta版本发布失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 检查工作区状态
     */
    checkWorkspace() {
        console.log('🔍 检查工作区状态...');
        
        try {
            // 版本一致性检查
            this.exec('node scripts/version-manager.js check');
            
            // workspace依赖检查
            this.exec('node scripts/version-manager.js workspace-check');
            
            // 预发布检查
            this.exec('node scripts/pre-publish-check.js');
            
            console.log('✅ 工作区状态正常！');
        } catch (error) {
            console.error('❌ 工作区检查失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 修复工作区问题
     */
    fixWorkspace() {
        console.log('🔧 修复工作区问题...');
        
        try {
            // 修复workspace依赖
            this.exec('node scripts/version-manager.js workspace-fix');
            
            // 重新安装依赖
            this.exec('pnpm install');
            
            console.log('✅ 工作区修复完成！');
        } catch (error) {
            console.error('❌ 工作区修复失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 清理构建文件
     */
    cleanBuild() {
        console.log('🧹 清理构建文件...');
        
        try {
            this.exec('pnpm run clean:packages');
            console.log('✅ 构建文件清理完成！');
        } catch (error) {
            console.error('❌ 清理失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 重新构建
     */
    rebuild() {
        console.log('🔨 重新构建...');
        
        try {
            this.cleanBuild();
            this.exec('pnpm run build:packages');
            console.log('✅ 重新构建完成！');
        } catch (error) {
            console.error('❌ 重新构建失败:', error.message);
            process.exit(1);
        }
    }

    /**
     * 显示包信息
     */
    showPackageInfo() {
        console.log('📦 包信息:');
        
        try {
            const versions = this.exec('node scripts/version-manager.js list');
            console.log(versions);
        } catch (error) {
            console.error('❌ 获取包信息失败:', error.message);
        }
    }

    /**
     * 生成发布说明
     */
    generateReleaseNotes(version) {
        console.log(`📝 生成版本 ${version} 发布说明...`);
        
        try {
            // 获取git日志
            const gitLog = this.exec(`git log --oneline --pretty=format:"%h %s" --since="1 week ago"`);
            
            const releaseNotes = `
# Release ${version}

## 🚀 发布时间
${new Date().toISOString()}

## 📦 包含的包
- @leyaoyao/ai-component-adapters@${version}
- @leyaoyao/ai-component-config@${version}
- @leyaoyao/ai-component-core@${version}
- @leyaoyao/ai-component-ui@${version}

## 🔄 最近更改
${gitLog.split('\n').map(line => `- ${line}`).join('\n')}

## 📥 安装
\`\`\`bash
npm install @leyaoyao/ai-component-ui@${version} --registry=https://npm.leyaoyao.com
\`\`\`

## 🔗 相关链接
- [文档](README.md)
- [更新日志](CHANGELOG.md)
- [示例](examples/)
`;

            const notesFile = path.join(this.rootDir, `RELEASE_NOTES_${version}.md`);
            fs.writeFileSync(notesFile, releaseNotes);
            
            console.log(`✅ 发布说明已生成: ${notesFile}`);
        } catch (error) {
            console.error('❌ 生成发布说明失败:', error.message);
        }
    }
}

// CLI接口
if (require.main === module) {
    const helper = new PublishHelper();
    const command = process.argv[2];
    const args = process.argv.slice(3);
    
    const commands = {
        'quick': () => helper.quickPublish(),
        'test': () => helper.testPublish(),
        'version': () => helper.versionPublish(args[0]),
        'beta': () => helper.betaPublish(),
        'check': () => helper.checkWorkspace(),
        'fix': () => helper.fixWorkspace(),
        'clean': () => helper.cleanBuild(),
        'rebuild': () => helper.rebuild(),
        'info': () => helper.showPackageInfo(),
        'notes': () => helper.generateReleaseNotes(args[0] || 'latest'),
    };
    
    if (commands[command]) {
        commands[command]().catch(error => {
            console.error('执行失败:', error.message);
            process.exit(1);
        });
    } else {
        console.log(`
Publish Helper - AI Component发布辅助工具

用法: node scripts/publish-helper.js <command> [args]

命令:
  quick              快速发布 (patch版本)
  test               测试发布 (dry run)
  version <version>  发布指定版本
  beta               发布beta版本
  check              检查工作区状态
  fix                修复工作区问题
  clean              清理构建文件
  rebuild            重新构建
  info               显示包信息
  notes [version]    生成发布说明

示例:
  node scripts/publish-helper.js quick
  node scripts/publish-helper.js version 1.2.3
  node scripts/publish-helper.js test
  node scripts/publish-helper.js beta
  node scripts/publish-helper.js check
`);
    }
}

module.exports = PublishHelper;