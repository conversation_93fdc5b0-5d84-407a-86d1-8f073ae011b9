#!/usr/bin/env node

/**
 * 标准包构建脚本 (adapters, core, config)
 * 支持 ESM, CJS, UMD 多格式输出 + Tree-shaking优化
 */

const path = require('path')
const fs = require('fs')
const { rollup } = require('rollup')
const typescript = require('@rollup/plugin-typescript')
const resolve = require('@rollup/plugin-node-resolve')
const commonjs = require('@rollup/plugin-commonjs')
const terser = require('@rollup/plugin-terser')
const { dts } = require('rollup-plugin-dts')

const {
  BUILD_CONFIG,
  getPackageInfo,
  generateExternals,
  generateGlobals
} = require('../build.config')

class StandardPackageBuilder {
  constructor(packagePath, options = {}) {
    this.packagePath = packagePath
    this.options = {
      mode: 'production',
      watch: false,
      verbose: false,
      ...options
    }
    
    this.packageName = path.basename(packagePath)
    this.packageInfo = getPackageInfo(this.packageName)
    this.distPath = path.join(packagePath, 'dist')
  }

  async build() {
    console.log(`    🔧 构建标准包: ${this.packageInfo.fullName}`)
    
    try {
      // 确保输出目录存在
      if (!fs.existsSync(this.distPath)) {
        fs.mkdirSync(this.distPath, { recursive: true })
      }
      
      // 构建 JavaScript 模块
      await this.buildJavaScript()
      
      // 构建类型声明文件
      await this.buildTypeDeclarations()
      
      // 生成子模块导出
      await this.generateSubmoduleExports()
      
      console.log(`    ✅ ${this.packageName} 标准构建完成`)
      
    } catch (error) {
      console.error(`    ❌ ${this.packageName} 构建失败:`, error.message)
      throw error
    }
  }

  // 构建 JavaScript 模块
  async buildJavaScript() {
    const inputPath = path.join(this.packagePath, 'index.ts')
    const externals = generateExternals(this.packageInfo)
    const globals = generateGlobals(externals)
    
    const inputOptions = {
      input: inputPath,
      external: externals,
      plugins: [
        resolve({
          preferBuiltins: false,
          browser: true
        }),
        commonjs(),
        typescript({
          tsconfig: path.join(this.packagePath, 'tsconfig.json'),
          declaration: false,
          declarationMap: false,
          outDir: this.distPath
        }),
        ...(this.options.mode === 'production' ? [
          terser(BUILD_CONFIG.OPTIMIZATION.terser)
        ] : [])
      ]
    }
    
    // 构建多种格式
    const formats = [
      { 
        format: 'es', 
        file: `${this.distPath}/index.esm.js`,
        sourcemap: this.options.mode === 'development',
        inlineDynamicImports: true // 内联动态导入避免多 chunk 问题
      },
      { 
        format: 'cjs', 
        file: `${this.distPath}/index.cjs.js`,
        sourcemap: this.options.mode === 'development',
        inlineDynamicImports: true
      },
      { 
        format: 'umd', 
        file: `${this.distPath}/index.umd.js`,
        name: this.getUMDName(),
        globals,
        sourcemap: this.options.mode === 'development',
        inlineDynamicImports: true
      }
    ]
    
    const bundle = await rollup(inputOptions)
    
    for (const outputOptions of formats) {
      await bundle.write(outputOptions)
      if (this.options.verbose) {
        console.log(`      📦 Generated ${path.basename(outputOptions.file)}`)
      }
    }
    
    await bundle.close()
  }

  // 构建类型声明文件
  async buildTypeDeclarations() {
    const inputPath = path.join(this.packagePath, 'index.ts')
    
    const bundle = await rollup({
      input: inputPath,
      external: generateExternals(this.packageInfo),
      plugins: [dts()]
    })
    
    await bundle.write({
      file: `${this.distPath}/index.d.ts`,
      format: 'es',
      inlineDynamicImports: true
    })
    
    await bundle.close()
    
    if (this.options.verbose) {
      console.log(`      📝 Generated index.d.ts`)
    }
  }

  // 生成子模块导出（支持按需引入）
  async generateSubmoduleExports() {
    const exports = this.packageInfo.exports
    
    if (!exports || Object.keys(exports).length <= 1) {
      return // 没有子模块导出
    }
    
    for (const [exportPath, sourcePath] of Object.entries(exports)) {
      if (exportPath === '.') continue // 跳过主导出
      
      const cleanPath = exportPath.replace('./', '')
      const sourceFile = path.resolve(this.packagePath, sourcePath)
      
      if (!fs.existsSync(sourceFile)) {
        console.warn(`      ⚠️  导出路径不存在: ${sourcePath}`)
        continue
      }
      
      await this.buildSubmodule(cleanPath, sourceFile)
    }
  }

  // 构建子模块
  async buildSubmodule(exportName, sourcePath) {
    const outputDir = path.join(this.distPath, exportName)
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    const externals = generateExternals(this.packageInfo)
    const globals = generateGlobals(externals)
    
    const inputOptions = {
      input: sourcePath,
      external: externals,
      plugins: [
        resolve({ preferBuiltins: false, browser: true }),
        commonjs(),
        typescript({
          tsconfig: path.join(this.packagePath, 'tsconfig.json'),
          declaration: false,
          outDir: outputDir
        }),
        ...(this.options.mode === 'production' ? [
          terser(BUILD_CONFIG.OPTIMIZATION.terser)
        ] : [])
      ]
    }
    
    const formats = [
      { 
        format: 'es', 
        file: `${outputDir}/index.esm.js`,
        sourcemap: this.options.mode === 'development',
        inlineDynamicImports: true
      },
      { 
        format: 'cjs', 
        file: `${outputDir}/index.cjs.js`,
        sourcemap: this.options.mode === 'development',
        inlineDynamicImports: true
      }
    ]
    
    const bundle = await rollup(inputOptions)
    
    for (const outputOptions of formats) {
      await bundle.write(outputOptions)
    }
    
    await bundle.close()
    
    // 生成类型声明
    const dtsBundle = await rollup({
      input: sourcePath,
      external: externals,
      plugins: [dts()]
    })
    
    await dtsBundle.write({
      file: `${outputDir}/index.d.ts`,
      format: 'es',
      inlineDynamicImports: true
    })
    
    await dtsBundle.close()
    
    if (this.options.verbose) {
      console.log(`      📦 Generated submodule: ${exportName}`)
    }
  }

  // 获取UMD包名
  getUMDName() {
    const name = this.packageInfo.fullName
      .replace('@leyaoyao/ai-component-', '')
      .replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
    
    return `AIComponent${name.charAt(0).toUpperCase() + name.slice(1)}`
  }
}

// CLI 入口
async function main() {
  const [packagePath, optionsJson] = process.argv.slice(2)
  
  if (!packagePath) {
    console.error('❌ 请提供包路径')
    process.exit(1)
  }
  
  const options = optionsJson ? JSON.parse(optionsJson) : {}
  const builder = new StandardPackageBuilder(packagePath, options)
  
  await builder.build()
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ 标准包构建失败:', error.message)
    process.exit(1)
  })
}

module.exports = StandardPackageBuilder