#!/usr/bin/env node

/**
 * Bundle 性能和体积优化脚本
 * 包含代码分割、压缩、去重、分析等高级优化功能
 */

const fs = require('fs')
const path = require('path')
const { rollup } = require('rollup')
const { visualizer } = require('rollup-plugin-visualizer')
const { getBundleAnalysis } = require('rollup-plugin-bundle-analyzer')
const gzipSize = require('gzip-size')
const brotliSize = require('brotli-size')

const { getAllPackages, formatBytes } = require('../build.config')

class BundleOptimizer {
  constructor(options = {}) {
    this.options = {
      analyze: true,
      compress: true,
      splitChunks: true,
      verbose: false,
      ...options
    }
    
    this.packages = getAllPackages()
    this.optimizationStats = new Map()
  }

  // 主优化流程
  async optimize() {
    console.log('⚡ 开始 Bundle 性能优化...')
    
    // 优化每个包
    for (const pkg of this.packages) {
      await this.optimizePackage(pkg)
    }
    
    // 生成全局优化报告
    await this.generateOptimizationReport()
    
    // 创建优化配置文件
    await this.createOptimizationConfigs()
    
    console.log('✅ Bundle 优化完成')
  }

  // 优化单个包
  async optimizePackage(pkg) {
    console.log(`  ⚡ 优化包: ${pkg.name}`)
    
    const distPath = path.join(pkg.path, 'dist')
    if (!fs.existsSync(distPath)) {
      console.log(`    ⚠️  跳过 ${pkg.name} - 未构建`)
      return
    }
    
    try {
      // 分析当前bundle
      const analysis = await this.analyzeBundleSize(distPath)
      
      // 应用优化策略
      const optimized = await this.applyOptimizations(pkg, analysis)
      
      // 记录优化结果
      this.optimizationStats.set(pkg.name, {
        original: analysis,
        optimized,
        improvement: this.calculateImprovement(analysis, optimized)
      })
      
      console.log(`    ✅ ${pkg.name} 优化完成`)
      
    } catch (error) {
      console.error(`    ❌ ${pkg.name} 优化失败:`, error.message)
    }
  }

  // 分析Bundle大小
  async analyzeBundleSize(distPath) {
    const analysis = {
      files: {},
      totalSize: 0,
      totalGzipSize: 0,
      totalBrotliSize: 0
    }
    
    const files = fs.readdirSync(distPath)
    
    for (const file of files) {
      const filePath = path.join(distPath, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isFile() && file.endsWith('.js')) {
        const content = fs.readFileSync(filePath)
        const size = content.length
        const gzipSize = await this.getGzipSize(content)
        const brotliSize = await this.getBrotliSize(content)
        
        analysis.files[file] = {
          size,
          gzipSize,
          brotliSize,
          sizeFormatted: formatBytes(size),
          gzipSizeFormatted: formatBytes(gzipSize),
          brotliSizeFormatted: formatBytes(brotliSize)
        }
        
        analysis.totalSize += size
        analysis.totalGzipSize += gzipSize
        analysis.totalBrotliSize += brotliSize
      }
    }
    
    analysis.totalSizeFormatted = formatBytes(analysis.totalSize)
    analysis.totalGzipSizeFormatted = formatBytes(analysis.totalGzipSize)
    analysis.totalBrotliSizeFormatted = formatBytes(analysis.totalBrotliSize)
    
    return analysis
  }

  // 应用优化策略
  async applyOptimizations(pkg, analysis) {
    const optimizations = []
    
    // 1. 代码分割优化
    if (this.shouldApplyCodeSplitting(pkg, analysis)) {
      const codeSplitResult = await this.applyCodeSplitting(pkg)
      optimizations.push(codeSplitResult)
    }
    
    // 2. 依赖去重优化
    const dedupeResult = await this.applyDuplicationRemoval(pkg)
    optimizations.push(dedupeResult)
    
    // 3. 压缩优化
    if (this.options.compress) {
      const compressionResult = await this.applyAdvancedCompression(pkg)
      optimizations.push(compressionResult)
    }
    
    // 4. 模块合并优化
    const moduleResult = await this.applyModuleMerging(pkg)
    optimizations.push(moduleResult)
    
    // 重新分析优化后的结果
    const optimizedAnalysis = await this.analyzeBundleSize(path.join(pkg.path, 'dist'))
    
    return {
      analysis: optimizedAnalysis,
      optimizations: optimizations.filter(opt => opt.applied)
    }
  }

  // 判断是否需要代码分割
  shouldApplyCodeSplitting(pkg, analysis) {
    // 如果单个文件超过50KB，建议分割
    return Object.values(analysis.files).some(file => file.size > 50000)
  }

  // 应用代码分割
  async applyCodeSplitting(pkg) {
    const result = {
      type: 'code-splitting',
      applied: false,
      sizeBefore: 0,
      sizeAfter: 0,
      improvement: 0
    }
    
    try {
      // 针对UI包的特殊处理
      if (pkg.name === 'ui') {
        await this.applySeparateChunksBuild(pkg)
        result.applied = true
      }
      
      // 其他包的动态导入优化
      else {
        await this.optimizeDynamicImports(pkg)
        result.applied = true
      }
      
    } catch (error) {
      console.warn(`    ⚠️  代码分割失败: ${error.message}`)
    }
    
    return result
  }

  // 为UI包应用分离chunk构建
  async applySeparateChunksBuild(pkg) {
    const platforms = ['web', 'vue', 'taro']
    
    for (const platform of platforms) {
      const platformPath = path.join(pkg.path, platform)
      if (!fs.existsSync(platformPath)) continue
      
      await this.buildWithChunkSeparation(pkg, platform)
    }
  }

  // 带chunk分离的构建
  async buildWithChunkSeparation(pkg, platform) {
    const inputPath = path.join(pkg.path, platform, 'index.ts')
    const outputDir = path.join(pkg.path, 'dist', platform, 'chunks')
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    const bundle = await rollup({
      input: inputPath,
      external: this.getExternalsForPlatform(platform),
      plugins: [
        ...this.getOptimizedPlugins(platform),
        // 添加chunk分析插件
        this.options.analyze && visualizer({
          filename: path.join(outputDir, 'chunk-analysis.html'),
          open: false,
          gzipSize: true,
          brotliSize: true
        })
      ].filter(Boolean)
    })
    
    await bundle.write({
      dir: outputDir,
      format: 'es',
      chunkFileNames: '[name]-[hash].js',
      manualChunks: this.getManualChunks(platform)
    })
    
    await bundle.close()
  }

  // 获取手动chunk配置
  getManualChunks(platform) {
    return (id) => {
      // vendor chunk for dependencies
      if (id.includes('node_modules')) {
        if (id.includes('react')) return 'react-vendor'
        if (id.includes('vue')) return 'vue-vendor'
        if (id.includes('antd')) return 'antd-vendor'
        return 'vendor'
      }
      
      // 组件chunk
      if (id.includes('/components/')) {
        return 'components'
      }
      
      // 工具函数chunk
      if (id.includes('/utils/')) {
        return 'utils'
      }
      
      // hooks chunk
      if (id.includes('/hooks/')) {
        return 'hooks'
      }
    }
  }

  // 应用依赖去重
  async applyDuplicationRemoval(pkg) {
    const result = {
      type: 'deduplication',
      applied: false,
      duplicatesFound: 0,
      sizeReduced: 0
    }
    
    try {
      const distPath = path.join(pkg.path, 'dist')
      const duplicates = await this.findDuplicates(distPath)
      
      if (duplicates.length > 0) {
        await this.removeDuplicates(duplicates)
        result.duplicatesFound = duplicates.length
        result.applied = true
      }
      
    } catch (error) {
      console.warn(`    ⚠️  去重失败: ${error.message}`)
    }
    
    return result
  }

  // 查找重复代码
  async findDuplicates(distPath) {
    const duplicates = []
    const files = fs.readdirSync(distPath).filter(f => f.endsWith('.js'))
    const codeBlocks = new Map()
    
    for (const file of files) {
      const content = fs.readFileSync(path.join(distPath, file), 'utf8')
      const blocks = this.extractCodeBlocks(content)
      
      blocks.forEach(block => {
        if (codeBlocks.has(block.hash)) {
          codeBlocks.get(block.hash).files.push({ file, ...block })
        } else {
          codeBlocks.set(block.hash, {
            code: block.code,
            files: [{ file, ...block }]
          })
        }
      })
    }
    
    codeBlocks.forEach((data, hash) => {
      if (data.files.length > 1 && data.code.length > 100) {
        duplicates.push({
          hash,
          code: data.code,
          files: data.files,
          size: data.code.length
        })
      }
    })
    
    return duplicates
  }

  // 提取代码块
  extractCodeBlocks(content) {
    const blocks = []
    const lines = content.split('\n')
    
    // 简单的代码块提取算法
    for (let i = 0; i < lines.length - 5; i++) {
      const block = lines.slice(i, i + 5).join('\n')
      if (block.trim().length > 50) {
        blocks.push({
          code: block,
          hash: this.hashCode(block),
          line: i + 1
        })
      }
    }
    
    return blocks
  }

  // 应用高级压缩
  async applyAdvancedCompression(pkg) {
    const result = {
      type: 'compression',
      applied: false,
      compressionRatio: 0
    }
    
    try {
      const distPath = path.join(pkg.path, 'dist')
      const files = fs.readdirSync(distPath).filter(f => f.endsWith('.js'))
      
      let totalBefore = 0
      let totalAfter = 0
      
      for (const file of files) {
        const filePath = path.join(distPath, file)
        const originalContent = fs.readFileSync(filePath, 'utf8')
        const compressed = await this.advancedCompress(originalContent)
        
        totalBefore += originalContent.length
        totalAfter += compressed.length
        
        fs.writeFileSync(filePath, compressed)
      }
      
      result.compressionRatio = ((totalBefore - totalAfter) / totalBefore * 100).toFixed(2)
      result.applied = true
      
    } catch (error) {
      console.warn(`    ⚠️  高级压缩失败: ${error.message}`)
    }
    
    return result
  }

  // 高级压缩算法
  async advancedCompress(code) {
    // 1. 移除不必要的空白
    let compressed = code
      .replace(/\s+/g, ' ')
      .replace(/;\s*/g, ';')
      .replace(/{\s*/g, '{')
      .replace(/}\s*/g, '}')
      .replace(/,\s*/g, ',')
    
    // 2. 简化console调用（生产环境）
    compressed = compressed.replace(/console\.(log|info|warn)\([^)]*\);?/g, '')
    
    // 3. 移除注释（保留重要的license注释）
    compressed = compressed.replace(/\/\*(?!.*@license)[\s\S]*?\*\//g, '')
    compressed = compressed.replace(/\/\/(?![^']*'[^']*$)(?![^"]*"[^"]*$).*$/gm, '')
    
    return compressed
  }

  // 应用模块合并
  async applyModuleMerging(pkg) {
    const result = {
      type: 'module-merging',
      applied: false,
      modulesMerged: 0
    }
    
    try {
      // 分析小模块并合并
      const smallModules = await this.findSmallModules(pkg)
      
      if (smallModules.length > 1) {
        await this.mergeSmallModules(pkg, smallModules)
        result.modulesMerged = smallModules.length
        result.applied = true
      }
      
    } catch (error) {
      console.warn(`    ⚠️  模块合并失败: ${error.message}`)
    }
    
    return result
  }

  // 查找小模块
  async findSmallModules(pkg) {
    const distPath = path.join(pkg.path, 'dist')
    const smallModules = []
    
    if (!fs.existsSync(distPath)) return smallModules
    
    const files = fs.readdirSync(distPath, { withFileTypes: true })
    
    for (const file of files) {
      if (file.isFile() && file.name.endsWith('.js')) {
        const filePath = path.join(distPath, file.name)
        const stat = fs.statSync(filePath)
        
        // 小于5KB的模块
        if (stat.size < 5000) {
          smallModules.push({
            name: file.name,
            path: filePath,
            size: stat.size
          })
        }
      }
    }
    
    return smallModules
  }

  // 合并小模块
  async mergeSmallModules(pkg, modules) {
    if (modules.length < 2) return
    
    const mergedContent = []
    const mergedPath = path.join(pkg.path, 'dist', 'merged-modules.js')
    
    for (const module of modules) {
      const content = fs.readFileSync(module.path, 'utf8')
      mergedContent.push(`// ${module.name}`)
      mergedContent.push(content)
      mergedContent.push('')
      
      // 删除原文件
      fs.unlinkSync(module.path)
    }
    
    fs.writeFileSync(mergedPath, mergedContent.join('\n'))
  }

  // 计算改进程度
  calculateImprovement(original, optimized) {
    const sizeReduction = original.totalSize - optimized.analysis.totalSize
    const sizeReductionPercent = (sizeReduction / original.totalSize * 100).toFixed(2)
    
    const gzipReduction = original.totalGzipSize - optimized.analysis.totalGzipSize
    const gzipReductionPercent = (gzipReduction / original.totalGzipSize * 100).toFixed(2)
    
    return {
      sizeReduction,
      sizeReductionPercent,
      gzipReduction,
      gzipReductionPercent,
      sizeReductionFormatted: formatBytes(sizeReduction),
      gzipReductionFormatted: formatBytes(gzipReduction)
    }
  }

  // 生成优化报告
  async generateOptimizationReport() {
    const report = {
      summary: {
        packagesOptimized: this.optimizationStats.size,
        totalSizeReduction: 0,
        totalGzipReduction: 0,
        averageImprovement: 0
      },
      packages: {}
    }
    
    let totalImprovement = 0
    
    this.optimizationStats.forEach((stats, packageName) => {
      const improvement = stats.improvement
      
      report.packages[packageName] = {
        original: stats.original,
        optimized: stats.optimized.analysis,
        improvement,
        optimizations: stats.optimized.optimizations
      }
      
      report.summary.totalSizeReduction += improvement.sizeReduction
      report.summary.totalGzipReduction += improvement.gzipReduction
      totalImprovement += parseFloat(improvement.sizeReductionPercent)
    })
    
    report.summary.averageImprovement = (totalImprovement / this.optimizationStats.size).toFixed(2)
    report.summary.totalSizeReductionFormatted = formatBytes(report.summary.totalSizeReduction)
    report.summary.totalGzipReductionFormatted = formatBytes(report.summary.totalGzipReduction)
    
    // 生成HTML报告
    const htmlReport = this.generateHTMLReport(report)
    fs.writeFileSync(path.join(process.cwd(), 'bundle-optimization-report.html'), htmlReport)
    
    // 生成JSON报告
    fs.writeFileSync(path.join(process.cwd(), 'bundle-optimization-report.json'), JSON.stringify(report, null, 2))
    
    console.log('📊 优化报告已生成:')
    console.log(`  - HTML报告: bundle-optimization-report.html`)
    console.log(`  - JSON报告: bundle-optimization-report.json`)
    console.log(`  - 总体积减少: ${report.summary.totalSizeReductionFormatted}`)
    console.log(`  - 平均优化: ${report.summary.averageImprovement}%`)
  }

  // 生成HTML报告
  generateHTMLReport(report) {
    return `<!DOCTYPE html>
<html>
<head>
    <title>AI Component Bundle 优化报告</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        .summary { background: #ecf0f1; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .summary-item { display: inline-block; margin: 10px 20px; }
        .summary-label { font-weight: bold; color: #2c3e50; }
        .summary-value { color: #27ae60; font-size: 1.2em; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #3498db; color: white; }
        tr:hover { background: #f5f5f5; }
        .improvement { color: #27ae60; font-weight: bold; }
        .size { font-family: monospace; }
        .optimization { background: #e8f5e8; padding: 5px 10px; border-radius: 3px; margin: 2px; display: inline-block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 AI Component Bundle 优化报告</h1>
        
        <div class="summary">
            <h2>📊 优化摘要</h2>
            <div class="summary-item">
                <div class="summary-label">优化包数量:</div>
                <div class="summary-value">${report.summary.packagesOptimized}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">总体积减少:</div>
                <div class="summary-value">${report.summary.totalSizeReductionFormatted}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">Gzip减少:</div>
                <div class="summary-value">${report.summary.totalGzipReductionFormatted}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">平均优化:</div>
                <div class="summary-value">${report.summary.averageImprovement}%</div>
            </div>
        </div>
        
        <h2>📦 包优化详情</h2>
        ${Object.entries(report.packages).map(([pkg, data]) => `
            <h3>${pkg}</h3>
            <table>
                <tr>
                    <th>指标</th>
                    <th>优化前</th>
                    <th>优化后</th>
                    <th>改进</th>
                </tr>
                <tr>
                    <td>原始大小</td>
                    <td class="size">${data.original.totalSizeFormatted}</td>
                    <td class="size">${data.optimized.totalSizeFormatted}</td>
                    <td class="improvement">${data.improvement.sizeReductionFormatted} (${data.improvement.sizeReductionPercent}%)</td>
                </tr>
                <tr>
                    <td>Gzip大小</td>
                    <td class="size">${data.original.totalGzipSizeFormatted}</td>
                    <td class="size">${data.optimized.totalGzipSizeFormatted}</td>
                    <td class="improvement">${data.improvement.gzipReductionFormatted} (${data.improvement.gzipReductionPercent}%)</td>
                </tr>
            </table>
            <div>
                <strong>应用的优化:</strong>
                ${data.optimizations.map(opt => `<span class="optimization">${opt.type}</span>`).join('')}
            </div>
        `).join('')}
        
        <h2>💡 优化建议</h2>
        <ul>
            <li>继续使用 Tree-shaking 减少未使用代码</li>
            <li>考虑使用 CDN 加载常用依赖</li>
            <li>启用 Brotli 压缩获得更好的压缩率</li>
            <li>定期运行优化脚本监控bundle大小</li>
        </ul>
        
        <p><small>报告生成时间: ${new Date().toLocaleString()}</small></p>
    </div>
</body>
</html>`
  }

  // 创建优化配置文件
  async createOptimizationConfigs() {
    // Webpack优化配置
    const webpackConfig = {
      mode: 'production',
      optimization: {
        usedExports: true,
        sideEffects: false,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
            },
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              enforce: true
            }
          }
        }
      },
      resolve: {
        mainFields: ['es2015', 'module', 'main']
      }
    }
    
    // Rollup优化配置
    const rollupConfig = {
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        unknownGlobalSideEffects: false
      },
      output: {
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            return 'vendor'
          }
        }
      }
    }
    
    fs.writeFileSync(path.join(process.cwd(), 'webpack.optimization.js'), 
      `module.exports = ${JSON.stringify(webpackConfig, null, 2)}`)
    
    fs.writeFileSync(path.join(process.cwd(), 'rollup.optimization.js'), 
      `export default ${JSON.stringify(rollupConfig, null, 2)}`)
    
    console.log('⚙️  优化配置文件已创建')
  }

  // 工具方法
  async getGzipSize(content) {
    return gzipSize.sync(content)
  }

  async getBrotliSize(content) {
    return brotliSize.sync(content)
  }

  hashCode(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return hash.toString()
  }

  getExternalsForPlatform(platform) {
    const commonExternals = ['react', 'react-dom', 'vue']
    const platformExternals = {
      web: ['antd', '@ant-design/icons', '@ant-design/x'],
      vue: ['ant-design-vue', '@ant-design/icons-vue'],
      taro: ['@tarojs/taro', '@tarojs/components', '@nutui/nutui-react-taro']
    }
    
    return [...commonExternals, ...(platformExternals[platform] || [])]
  }

  getOptimizedPlugins(platform) {
    // 返回优化插件配置 - 这里简化处理
    return []
  }

  optimizeDynamicImports(pkg) {
    // 优化动态导入 - 这里简化处理
    return Promise.resolve()
  }

  removeDuplicates(duplicates) {
    // 移除重复代码 - 这里简化处理
    return Promise.resolve()
  }
}

// CLI 入口
async function main() {
  const args = process.argv.slice(2)
  const options = {}
  
  // 解析参数
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--no-analyze':
        options.analyze = false
        break
      case '--no-compress':
        options.compress = false
        break
      case '--no-split':
        options.splitChunks = false
        break
      case '--verbose':
        options.verbose = true
        break
    }
  }
  
  const optimizer = new BundleOptimizer(options)
  await optimizer.optimize()
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Bundle 优化失败:', error.message)
    process.exit(1)
  })
}

module.exports = BundleOptimizer