# AI Component 发布脚本

本目录包含用于构建和发布 AI Component monorepo 的脚本。

## 快速开始

### 1. 测试环境
```bash
# 测试发布环境配置
pnpm run test:publish
```

### 2. 运行发布

**推荐方式（跨平台）：**
```bash
pnpm run publish
```

**其他方式：**
```bash
# Windows 命令行
pnpm run publish:cmd

# Windows PowerShell
pnpm run publish:ps1

# Unix/Linux/macOS/WSL
pnpm run publish:bash
```

## 脚本说明

| 脚本 | 描述 | 平台 |
|------|------|------|
| `build-and-publish.sh` | 主发布脚本（Bash） | Unix/Linux/macOS/Git Bash |
| `build-and-publish.cmd` | Windows 批处理包装器 | Windows |
| `build-and-publish.ps1` | PowerShell 包装器 | Windows |
| `run-publish.js` | 跨平台 Node.js 启动器 | 所有平台 |
| `test-publish.js` | 环境测试脚本 | 所有平台 |

## 环境要求

### 必需：
- Node.js >= 16.0.0
- pnpm
- Git

### Windows 用户：
- 推荐安装 Git for Windows（包含 Git Bash）
- 或使用 PowerShell 5.0+

## 发布选项

```bash
# 基本用法
pnpm run publish [version] [options]

# 示例
pnpm run publish                    # 自动递增版本
pnpm run publish 1.2.3             # 指定版本
pnpm run publish --dry-run          # 测试模式
pnpm run publish --skip-tests       # 跳过测试
pnpm run publish --force            # 强制发布
```

## 环境变量

设置 Sonatype Nexus 认证信息：

```bash
# Windows
set NPM_USERNAME=your-username
set NPM_PASSWORD=your-password

# Unix/Linux/macOS
export NPM_USERNAME="your-username"
export NPM_PASSWORD="your-password"
```

## 故障排除

### 问题 1: 路径错误
如果遇到路径相关错误，尝试：
1. 使用 `pnpm run test:publish` 检查环境
2. 使用不同的启动方式（cmd/ps1/bash）

### 问题 2: WSL 错误
如果在 Windows 上遇到 WSL 错误：
1. 使用 `pnpm run publish:cmd` 或 `pnpm run publish:ps1`
2. 避免通过 WSL 运行脚本

### 问题 3: 认证失败
1. 检查用户名密码是否正确
2. 确保用户在 Nexus 中有发布权限
3. 验证仓库 URL 配置

## 配置

脚本配置位于 `build-and-publish.sh` 头部：

```bash
COMPANY_REGISTRY="https://npm-registry.leyaoyao.com/"
COMPANY_SCOPE="@lyy-npm-group"
```

packages 构建顺序：
```bash
PACKAGES_BUILD_ORDER=(
    "adapters"
    "config"  
    "core"
    "ui"
)
```