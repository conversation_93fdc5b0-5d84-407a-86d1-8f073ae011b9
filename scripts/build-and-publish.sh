#!/bin/bash

# =============================================================================
# AI Component Monorepo Build & Publish Script
# =============================================================================
# 功能: 自动化构建和发布所有packages到公司npm仓库
# 作者: AI Component Team
# 用法: ./scripts/build-and-publish.sh [version] [--dry-run] [--skip-tests] [--force]
# =============================================================================

set -e  # 任何命令失败时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"
PACKAGES_DIR="${ROOT_DIR}/packages"
BUILD_LOG="${ROOT_DIR}/build.log"
PUBLISH_LOG="${ROOT_DIR}/publish.log"

# 公司npm仓库配置
COMPANY_REGISTRY="https://npm-registry.leyaoyao.com/"  # 替换为公司实际仓库地址
COMPANY_SCOPE="@lyy-npm-group"

# packages构建顺序（考虑依赖关系）
PACKAGES_BUILD_ORDER=(
    "adapters"
    "config"
    "core"
    "ui"
)

# 参数解析
VERSION=""
DRY_RUN=false
SKIP_TESTS=false
FORCE_PUBLISH=false
HELP=false

for arg in "$@"; do
    case $arg in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --force)
            FORCE_PUBLISH=true
            shift
            ;;
        --help|-h)
            HELP=true
            shift
            ;;
        --*)
            echo -e "${RED}未知参数: $arg${NC}"
            exit 1
            ;;
        *)
            if [[ -z "$VERSION" ]]; then
                VERSION="$arg"
            fi
            shift
            ;;
    esac
done

# 帮助信息
show_help() {
    cat << EOF
AI Component Monorepo Build & Publish Script

用法: $0 [version] [options]

参数:
    version              新版本号 (语义化版本，如: 1.2.3, 1.2.3-beta.1)
                        如果不提供，将自动递增patch版本

选项:
    --dry-run           只执行构建和检查，不实际发布
    --skip-tests        跳过测试阶段
    --force             强制发布，即使检查失败
    --help, -h          显示此帮助信息

示例:
    $0                           # 自动递增patch版本并发布
    $0 1.2.3                     # 发布指定版本
    $0 1.2.3-beta.1              # 发布beta版本
    $0 --dry-run                 # 仅测试构建过程
    $0 1.2.3 --skip-tests        # 跳过测试直接发布

环境变量:
    NPM_USERNAME            Sonatype Nexus用户名
    NPM_PASSWORD            Sonatype Nexus密码
    COMPANY_REGISTRY        公司npm仓库地址（默认: $COMPANY_REGISTRY）
    NODE_ENV                环境标识（production/development）

EOF
}

if [[ "$HELP" == true ]]; then
    show_help
    exit 0
fi

# 工具函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    # 检查Node.js版本
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | sed 's/v//')
    REQUIRED_NODE_VERSION="16.0.0"
    if ! npx semver -r ">=$REQUIRED_NODE_VERSION" "$NODE_VERSION" &> /dev/null; then
        log_error "Node.js 版本过低，需要 >= $REQUIRED_NODE_VERSION，当前: $NODE_VERSION"
        exit 1
    fi
    
    # 检查pnpm
    if ! command -v pnpm &> /dev/null; then
        log_error "pnpm 未安装，请运行: npm install -g pnpm"
        exit 1
    fi
    
    # 检查git工作区
    if [[ -n $(git status --porcelain) ]] && [[ "$FORCE_PUBLISH" != true ]]; then
        log_error "Git工作区不干净，请先提交或暂存变更，或使用 --force 强制发布"
        git status --short
        exit 1
    fi
    
    # 检查认证信息
    if [[ "$DRY_RUN" != true ]]; then
        # Sonatype Nexus 使用用户名密码认证
        if [[ -z "$NPM_USERNAME" ]] || [[ -z "$NPM_PASSWORD" ]]; then
            log_warning "未设置 NPM_USERNAME 或 NPM_PASSWORD 环境变量"
            if [[ -z "$NPM_USERNAME" ]]; then
                read -p "请输入npm用户名: " NPM_USERNAME
                export NPM_USERNAME
            fi
            if [[ -z "$NPM_PASSWORD" ]]; then
                read -p "请输入npm密码: " -s NPM_PASSWORD
                echo
                export NPM_PASSWORD
            fi
        fi
        
        # 使用npm login生成认证token
        log_info "配置Sonatype Nexus认证..."
        echo -e "$NPM_USERNAME\n$NPM_PASSWORD\n" | npm login --registry="$COMPANY_REGISTRY" --scope="$COMPANY_SCOPE" &> /dev/null || {
            log_error "Nexus认证失败，请检查用户名和密码"
            exit 1
        }
        log_success "Nexus认证成功"
    fi
    
    log_success "依赖检查通过"
}

# 获取当前版本
get_current_version() {
    local package_json="${ROOT_DIR}/package.json"
    if [[ -f "$package_json" ]]; then
        # 处理Windows路径问题
        if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
            # 转换为Windows路径格式
            local win_path=$(cygpath -w "$package_json" 2>/dev/null || echo "$package_json")
            node -p "require('$win_path').version"
        else
            node -p "require('$package_json').version"
        fi
    else
        echo "0.0.0"
    fi
}

# 计算新版本
calculate_new_version() {
    local current_version="$1"
    local new_version="$2"
    
    if [[ -n "$new_version" ]]; then
        # 验证版本格式
        if ! npx semver "$new_version" &> /dev/null; then
            log_error "无效的版本格式: $new_version"
            exit 1
        fi
        echo "$new_version"
    else
        # 自动递增patch版本
        npx semver -i patch "$current_version"
    fi
}

# 更新package版本
update_package_versions() {
    local new_version="$1"
    log_step "更新package版本到 $new_version..."
    
    # 更新根package.json
    if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        # Windows路径处理
        local win_root=$(cygpath -w "$ROOT_DIR" 2>/dev/null || echo "$ROOT_DIR")
        node -e "
            const fs = require('fs');
            const pkg = require('${win_root}/package.json'.replace(/\\\\/g, '/'));
            pkg.version = '$new_version';
            fs.writeFileSync('${win_root}/package.json'.replace(/\\\\/g, '/'), JSON.stringify(pkg, null, 2) + '\\n');
        "
    else
        node -e "
            const fs = require('fs');
            const pkg = require('${ROOT_DIR}/package.json');
            pkg.version = '$new_version';
            fs.writeFileSync('${ROOT_DIR}/package.json', JSON.stringify(pkg, null, 2) + '\\n');
        "
    fi
    
    # 更新所有packages的版本
    for package in "${PACKAGES_BUILD_ORDER[@]}"; do
        local package_dir="${PACKAGES_DIR}/${package}"
        local package_json="${package_dir}/package.json"
        
        if [[ -f "$package_json" ]]; then
            log_info "更新 ${COMPANY_SCOPE}/ai-component-${package} 版本"
            if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
                # Windows路径处理
                local win_pkg_json=$(cygpath -w "$package_json" 2>/dev/null || echo "$package_json")
                node -e "
                    const fs = require('fs');
                    const pkg = require('$win_pkg_json'.replace(/\\\\/g, '/'));
                    pkg.version = '$new_version';
                    
                    // 更新workspace依赖版本
                    ['dependencies', 'devDependencies', 'peerDependencies'].forEach(depType => {
                        if (pkg[depType]) {
                            Object.keys(pkg[depType]).forEach(dep => {
                                if (dep.startsWith('${COMPANY_SCOPE}/ai-component-') && pkg[depType][dep] === 'workspace:*') {
                                    pkg[depType][dep] = '^$new_version';
                                }
                            });
                        }
                    });
                    
                    fs.writeFileSync('$win_pkg_json'.replace(/\\\\/g, '/'), JSON.stringify(pkg, null, 2) + '\\n');
                "
            else
                node -e "
                    const fs = require('fs');
                    const pkg = require('$package_json');
                    pkg.version = '$new_version';
                    
                    // 更新workspace依赖版本
                    ['dependencies', 'devDependencies', 'peerDependencies'].forEach(depType => {
                        if (pkg[depType]) {
                            Object.keys(pkg[depType]).forEach(dep => {
                                if (dep.startsWith('${COMPANY_SCOPE}/ai-component-') && pkg[depType][dep] === 'workspace:*') {
                                    pkg[depType][dep] = '^$new_version';
                                }
                            });
                        }
                    });
                    
                    fs.writeFileSync('$package_json', JSON.stringify(pkg, null, 2) + '\\n');
                "
            fi
        fi
    done
    
    log_success "版本更新完成"
}

# 安装依赖
install_dependencies() {
    log_step "安装依赖..."
    cd "$ROOT_DIR"
    
    if ! pnpm install --frozen-lockfile; then
        log_error "依赖安装失败"
        exit 1
    fi
    
    log_success "依赖安装完成"
}

# 运行测试
run_tests() {
    if [[ "$SKIP_TESTS" == true ]]; then
        log_warning "跳过测试阶段"
        return 0
    fi
    
    log_step "运行测试..."
    cd "$ROOT_DIR"
    
    # 运行lint检查
    if command -v pnpm &> /dev/null && pnpm run lint &> /dev/null; then
        log_info "运行代码格式检查..."
        pnpm run lint
    fi
    
    # 运行类型检查
    if command -v pnpm &> /dev/null && pnpm run type-check &> /dev/null; then
        log_info "运行类型检查..."
        pnpm run type-check
    fi
    
    # 运行单元测试
    for package in "${PACKAGES_BUILD_ORDER[@]}"; do
        local package_dir="${PACKAGES_DIR}/${package}"
        if [[ -f "${package_dir}/package.json" ]]; then
            cd "$package_dir"
            if grep -q '"test":' package.json; then
                log_info "运行 ${package} 测试..."
                pnpm run test || {
                    log_error "${package} 测试失败"
                    exit 1
                }
            fi
        fi
    done
    
    log_success "所有测试通过"
}

# 构建packages
build_packages() {
    log_step "构建packages..."
    cd "$ROOT_DIR"
    
    # 清理旧的构建文件
    log_info "清理旧的构建文件..."
    pnpm run clean:packages
    
    # 按依赖顺序构建
    for package in "${PACKAGES_BUILD_ORDER[@]}"; do
        local package_dir="${PACKAGES_DIR}/${package}"
        if [[ -f "${package_dir}/package.json" ]]; then
            log_info "构建 ${COMPANY_SCOPE}/ai-component-${package}..."
            cd "$package_dir"
            
            if ! pnpm run build 2>&1 | tee -a "$BUILD_LOG"; then
                log_error "${package} 构建失败，查看 $BUILD_LOG 获取详细信息"
                exit 1
            fi
            
            # 验证构建产物
            if [[ ! -d "dist" ]]; then
                log_error "${package} 构建产物不存在"
                exit 1
            fi
            
            log_success "${package} 构建完成"
        fi
    done
    
    log_success "所有packages构建完成"
}

# 预发布检查
pre_publish_check() {
    log_step "预发布检查..."
    
    # 检查构建产物
    for package in "${PACKAGES_BUILD_ORDER[@]}"; do
        local package_dir="${PACKAGES_DIR}/${package}"
        local dist_dir="${package_dir}/dist"
        
        if [[ ! -d "$dist_dir" ]]; then
            log_error "${package} 缺少构建产物"
            exit 1
        fi
        
        # 检查关键文件
        local package_json="${package_dir}/package.json"
        local main_field
        if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
            local win_pkg_json=$(cygpath -w "$package_json" 2>/dev/null || echo "$package_json")
            main_field=$(node -p "require('$win_pkg_json'.replace(/\\\\/g, '/')).main || ''")
        else
            main_field=$(node -p "require('$package_json').main || ''")
        fi
        local module_field
        local types_field
        if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
            module_field=$(node -p "require('$win_pkg_json'.replace(/\\\\/g, '/')).module || ''")
            types_field=$(node -p "require('$win_pkg_json'.replace(/\\\\/g, '/')).types || ''")
        else
            module_field=$(node -p "require('$package_json').module || ''")
            types_field=$(node -p "require('$package_json').types || ''")
        fi
        
        if [[ -n "$main_field" && ! -f "${package_dir}/${main_field}" ]]; then
            log_error "${package} main字段指向的文件不存在: $main_field"
            exit 1
        fi
        
        if [[ -n "$module_field" && ! -f "${package_dir}/${module_field}" ]]; then
            log_error "${package} module字段指向的文件不存在: $module_field"
            exit 1
        fi
        
        if [[ -n "$types_field" && ! -f "${package_dir}/${types_field}" ]]; then
            log_error "${package} types字段指向的文件不存在: $types_field"
            exit 1
        fi
    done
    
    # 配置Nexus scoped registry
    log_info "配置Nexus scoped registry..."
    npm config set "${COMPANY_SCOPE}:registry" "$COMPANY_REGISTRY"
    
    # 确保scope总是发布到正确的registry
    npm config set always-auth true
    
    log_success "预发布检查通过"
}

# 发布packages
publish_packages() {
    if [[ "$DRY_RUN" == true ]]; then
        log_warning "Dry run模式，跳过实际发布"
        return 0
    fi
    
    log_step "发布packages到 $COMPANY_REGISTRY..."
    
    # Nexus 3.x 使用npm login进行认证，认证信息已在check_dependencies中配置
    
    # 按依赖顺序发布
    for package in "${PACKAGES_BUILD_ORDER[@]}"; do
        local package_dir="${PACKAGES_DIR}/${package}"
        if [[ -f "${package_dir}/package.json" ]]; then
            cd "$package_dir"
            
            local package_name="${COMPANY_SCOPE}/ai-component-${package}"
            local package_version
            if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
                local win_current_pkg=$(cygpath -w "./package.json" 2>/dev/null || echo "./package.json")
                package_version=$(node -p "require('$win_current_pkg'.replace(/\\\\/g, '/')).version")
            else
                package_version=$(node -p "require('./package.json').version")
            fi
            
            log_info "发布 ${package_name}@${package_version}..."
            
            # 检查版本是否已存在
            if npm view "${package_name}@${package_version}" version &> /dev/null; then
                if [[ "$FORCE_PUBLISH" != true ]]; then
                    log_error "版本 ${package_name}@${package_version} 已存在，使用 --force 强制发布"
                    exit 1
                else
                    log_warning "强制覆盖已存在的版本 ${package_name}@${package_version}"
                fi
            fi
            
            # 执行发布 - Nexus需要明确指定registry
            if ! npm publish --registry="$COMPANY_REGISTRY" --access=public 2>&1 | tee -a "$PUBLISH_LOG"; then
                # 检查是否是Nexus特定错误
                if grep -q "401 Unauthorized" "$PUBLISH_LOG"; then
                    log_error "认证失败：请检查用户名密码是否正确，或用户是否有发布权限"
                elif grep -q "403 Forbidden" "$PUBLISH_LOG"; then
                    log_error "权限不足：用户没有向 ${package_name} 发布的权限"
                elif grep -q "409 Conflict" "$PUBLISH_LOG"; then
                    log_error "版本冲突：${package_name}@${package_version} 已存在且不允许覆盖"
                else
                    log_error "${package_name} 发布失败，查看 $PUBLISH_LOG 获取详细信息"
                fi
                exit 1
            fi
            
            log_success "${package_name}@${package_version} 发布成功"
        fi
    done
    
    log_success "所有packages发布完成"
}

# 创建git tag
create_git_tag() {
    if [[ "$DRY_RUN" == true ]]; then
        log_warning "Dry run模式，跳过git tag创建"
        return 0
    fi
    
    local version="$1"
    local tag_name="v${version}"
    
    log_step "创建git tag: $tag_name"
    
    # 提交版本变更
    git add -A
    git commit -m "chore(release): bump version to $version

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>" || true
    
    # 创建tag
    if git tag -a "$tag_name" -m "Release $version"; then
        log_success "Git tag $tag_name 创建成功"
        
        # 推送tag（可选）
        read -p "是否推送tag到远程仓库? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            git push origin "$tag_name"
            git push origin master
            log_success "Tag推送完成"
        fi
    else
        log_warning "Git tag创建失败（可能已存在）"
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 恢复npm配置
    npm config delete "${COMPANY_SCOPE}:registry" &> /dev/null || true
    npm config delete always-auth &> /dev/null || true
    
    # 登出Nexus（保护认证信息）
    npm logout --registry="$COMPANY_REGISTRY" &> /dev/null || true
}

# 主函数
main() {
    # 设置清理函数
    trap cleanup EXIT
    
    log_info "🚀 开始AI Component发布流程..."
    log_info "Root目录: $ROOT_DIR"
    log_info "目标仓库: $COMPANY_REGISTRY"
    
    # 检查依赖
    check_dependencies
    
    # 获取版本信息
    local current_version
    current_version=$(get_current_version)
    local new_version
    new_version=$(calculate_new_version "$current_version" "$VERSION")
    
    log_info "当前版本: $current_version"
    log_info "目标版本: $new_version"
    
    if [[ "$DRY_RUN" == true ]]; then
        log_warning "🧪 Dry Run模式 - 不会实际发布"
    fi
    
    # 确认发布
    if [[ "$DRY_RUN" != true ]]; then
        echo
        read -p "确认发布版本 $new_version 到 $COMPANY_REGISTRY? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "发布已取消"
            exit 0
        fi
    fi
    
    # 更新版本
    update_package_versions "$new_version"
    
    # 安装依赖
    install_dependencies
    
    # 运行测试
    run_tests
    
    # 构建packages
    build_packages
    
    # 预发布检查
    pre_publish_check
    
    # 发布packages
    publish_packages
    
    # 创建git tag
    create_git_tag "$new_version"
    
    echo
    log_success "🎉 发布完成！"
    log_info "版本: $new_version"
    log_info "仓库: $COMPANY_REGISTRY"
    
    if [[ "$DRY_RUN" != true ]]; then
        echo
        log_info "📦 已发布的packages:"
        for package in "${PACKAGES_BUILD_ORDER[@]}"; do
            echo "  - ${COMPANY_SCOPE}/ai-component-${package}@${new_version}"
        done
        echo
        log_info "💡 安装命令:"
        echo "  # 配置scope registry"
        echo "  npm config set ${COMPANY_SCOPE}:registry $COMPANY_REGISTRY"
        echo ""
        echo "  # 安装组件"
        echo "  npm install ${COMPANY_SCOPE}/ai-component-ui@${new_version}"
    fi
}

# 运行主函数
main "$@"