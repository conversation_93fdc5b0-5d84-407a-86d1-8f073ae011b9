#!/usr/bin/env node

/**
 * Cross-platform publish script runner
 * Automatically detects the environment and runs the appropriate script
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get script arguments
const args = process.argv.slice(2);

// Detect platform
const isWindows = process.platform === 'win32';
const scriptDir = __dirname;

function runScript(command, args) {
    console.log(`Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
        stdio: 'inherit',
        shell: true,
        cwd: path.join(scriptDir, '..')
    });

    child.on('error', (error) => {
        console.error('Failed to start script:', error);
        process.exit(1);
    });

    child.on('exit', (code) => {
        process.exit(code);
    });
}

if (isWindows) {
    // On Windows, try Git Bash directly first to avoid WSL issues
    const gitBashPaths = [
        'C:\\Program Files\\Git\\bin\\bash.exe',
        'C:\\Program Files (x86)\\Git\\bin\\bash.exe',
        path.join(process.env.ProgramFiles || '', 'Git\\bin\\bash.exe'),
        'C:\\Program Files\\Git\\usr\\bin\\bash.exe',
        'C:\\Program Files (x86)\\Git\\usr\\bin\\bash.exe'
    ];
    
    let gitBash = null;
    for (const bashPath of gitBashPaths) {
        if (fs.existsSync(bashPath)) {
            gitBash = bashPath;
            console.log(`Found Git Bash at: ${bashPath}`);
            break;
        }
    }
    
    if (gitBash) {
        const bashScript = path.join(scriptDir, 'build-and-publish.sh');
        // Use Git Bash directly to avoid WSL issues
        runScript(`"${gitBash}"`, [bashScript, ...args]);
    } else {
        console.error('Error: Git Bash not found. Please install Git for Windows.');
        console.error('Download from: https://git-scm.com/download/win');
        console.error('Or try running: pnpm run publish:ps1 for PowerShell version');
        process.exit(1);
    }
} else {
    // On Unix-like systems, run the bash script directly
    const bashScript = path.join(scriptDir, 'build-and-publish.sh');
    runScript(bashScript, args);
}