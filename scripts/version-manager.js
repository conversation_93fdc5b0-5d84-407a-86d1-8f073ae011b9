#!/usr/bin/env node

/**
 * Version Manager - AI Component项目版本管理工具
 * 
 * 功能：
 * - 统一管理monorepo中所有packages的版本
 * - 处理workspace依赖版本更新
 * - 版本预检查和验证
 * - 生成版本变更报告
 */

const fs = require('fs');
const path = require('path');
const semver = require('semver');

class VersionManager {
    constructor(rootDir = process.cwd()) {
        this.rootDir = rootDir;
        this.packagesDir = path.join(rootDir, 'packages');
        this.packageDirs = [
            'adapters',
            'config', 
            'core',
            'ui'
        ];
        this.companyScope = '@leyaoyao';
    }

    /**
     * 获取根package.json的版本
     */
    getCurrentVersion() {
        const packageJsonPath = path.join(this.rootDir, 'package.json');
        if (!fs.existsSync(packageJsonPath)) {
            throw new Error('Root package.json not found');
        }
        
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        return packageJson.version || '0.0.0';
    }

    /**
     * 获取所有packages的版本信息
     */
    getAllPackageVersions() {
        const versions = {};
        
        for (const packageDir of this.packageDirs) {
            const packageJsonPath = path.join(this.packagesDir, packageDir, 'package.json');
            if (fs.existsSync(packageJsonPath)) {
                const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
                versions[packageJson.name] = packageJson.version;
            }
        }
        
        return versions;
    }

    /**
     * 验证版本格式
     */
    validateVersion(version) {
        if (!semver.valid(version)) {
            throw new Error(`Invalid version format: ${version}`);
        }
        return true;
    }

    /**
     * 比较版本
     */
    compareVersions(currentVersion, newVersion) {
        const comparison = semver.compare(newVersion, currentVersion);
        
        if (comparison > 0) {
            return 'upgrade';
        } else if (comparison < 0) {
            return 'downgrade';
        } else {
            return 'same';
        }
    }

    /**
     * 生成新版本
     */
    generateNewVersion(currentVersion, releaseType = 'patch') {
        const validReleaseTypes = ['major', 'minor', 'patch', 'premajor', 'preminor', 'prepatch', 'prerelease'];
        
        if (!validReleaseTypes.includes(releaseType)) {
            throw new Error(`Invalid release type: ${releaseType}`);
        }
        
        return semver.inc(currentVersion, releaseType);
    }

    /**
     * 更新所有packages的版本
     */
    updateAllVersions(newVersion) {
        this.validateVersion(newVersion);
        
        const changes = [];
        
        // 更新根package.json
        const rootPackageJsonPath = path.join(this.rootDir, 'package.json');
        const rootPackageJson = JSON.parse(fs.readFileSync(rootPackageJsonPath, 'utf8'));
        const oldRootVersion = rootPackageJson.version;
        
        rootPackageJson.version = newVersion;
        fs.writeFileSync(rootPackageJsonPath, JSON.stringify(rootPackageJson, null, 2) + '\n');
        
        changes.push({
            package: 'root',
            oldVersion: oldRootVersion,
            newVersion: newVersion,
            path: rootPackageJsonPath
        });

        // 更新所有packages
        for (const packageDir of this.packageDirs) {
            const packageJsonPath = path.join(this.packagesDir, packageDir, 'package.json');
            
            if (!fs.existsSync(packageJsonPath)) {
                console.warn(`Package.json not found: ${packageJsonPath}`);
                continue;
            }
            
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            const oldVersion = packageJson.version;
            
            // 更新版本
            packageJson.version = newVersion;
            
            // 更新workspace依赖版本
            ['dependencies', 'devDependencies', 'peerDependencies'].forEach(depType => {
                if (packageJson[depType]) {
                    Object.keys(packageJson[depType]).forEach(dep => {
                        if (dep.startsWith(`${this.companyScope}/ai-component-`)) {
                            if (packageJson[depType][dep] === 'workspace:*') {
                                packageJson[depType][dep] = `^${newVersion}`;
                            }
                        }
                    });
                }
            });
            
            fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
            
            changes.push({
                package: packageJson.name,
                oldVersion: oldVersion,
                newVersion: newVersion,
                path: packageJsonPath
            });
        }
        
        return changes;
    }

    /**
     * 检查版本一致性
     */
    checkVersionConsistency() {
        const rootVersion = this.getCurrentVersion();
        const packageVersions = this.getAllPackageVersions();
        
        const inconsistencies = [];
        
        Object.entries(packageVersions).forEach(([packageName, version]) => {
            if (version !== rootVersion) {
                inconsistencies.push({
                    package: packageName,
                    version: version,
                    expected: rootVersion
                });
            }
        });
        
        return {
            isConsistent: inconsistencies.length === 0,
            rootVersion: rootVersion,
            inconsistencies: inconsistencies
        };
    }

    /**
     * 生成版本变更报告
     */
    generateChangeReport(changes) {
        const report = {
            timestamp: new Date().toISOString(),
            totalChanges: changes.length,
            changes: changes,
            summary: {}
        };
        
        // 统计变更类型
        changes.forEach(change => {
            const changeType = this.compareVersions(change.oldVersion, change.newVersion);
            if (!report.summary[changeType]) {
                report.summary[changeType] = 0;
            }
            report.summary[changeType]++;
        });
        
        return report;
    }

    /**
     * 检查workspace依赖
     */
    checkWorkspaceDependencies() {
        const issues = [];
        
        for (const packageDir of this.packageDirs) {
            const packageJsonPath = path.join(this.packagesDir, packageDir, 'package.json');
            
            if (!fs.existsSync(packageJsonPath)) {
                continue;
            }
            
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            ['dependencies', 'devDependencies'].forEach(depType => {
                if (packageJson[depType]) {
                    Object.entries(packageJson[depType]).forEach(([dep, version]) => {
                        if (dep.startsWith(`${this.companyScope}/ai-component-`)) {
                            if (version === 'workspace:*') {
                                // 这是正确的workspace依赖格式
                            } else if (version.startsWith('^') || version.startsWith('~')) {
                                // 检查是否应该是workspace依赖
                                const targetPackage = dep.replace(`${this.companyScope}/ai-component-`, '');
                                if (this.packageDirs.includes(targetPackage)) {
                                    issues.push({
                                        package: packageJson.name,
                                        dependency: dep,
                                        currentVersion: version,
                                        issue: 'Should use workspace:*',
                                        suggestion: 'workspace:*'
                                    });
                                }
                            }
                        }
                    });
                }
            });
        }
        
        return issues;
    }

    /**
     * 修复workspace依赖
     */
    fixWorkspaceDependencies() {
        const fixes = [];
        
        for (const packageDir of this.packageDirs) {
            const packageJsonPath = path.join(this.packagesDir, packageDir, 'package.json');
            
            if (!fs.existsSync(packageJsonPath)) {
                continue;
            }
            
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            let hasChanges = false;
            
            ['dependencies', 'devDependencies'].forEach(depType => {
                if (packageJson[depType]) {
                    Object.keys(packageJson[depType]).forEach(dep => {
                        if (dep.startsWith(`${this.companyScope}/ai-component-`)) {
                            const targetPackage = dep.replace(`${this.companyScope}/ai-component-`, '');
                            if (this.packageDirs.includes(targetPackage) && packageJson[depType][dep] !== 'workspace:*') {
                                fixes.push({
                                    package: packageJson.name,
                                    dependency: dep,
                                    oldVersion: packageJson[depType][dep],
                                    newVersion: 'workspace:*'
                                });
                                packageJson[depType][dep] = 'workspace:*';
                                hasChanges = true;
                            }
                        }
                    });
                }
            });
            
            if (hasChanges) {
                fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
            }
        }
        
        return fixes;
    }
}

// CLI接口
if (require.main === module) {
    const versionManager = new VersionManager();
    const command = process.argv[2];
    const args = process.argv.slice(3);
    
    try {
        switch (command) {
            case 'current':
                console.log(versionManager.getCurrentVersion());
                break;
                
            case 'check':
                const consistency = versionManager.checkVersionConsistency();
                if (consistency.isConsistent) {
                    console.log('✅ All packages have consistent versions');
                    console.log(`Current version: ${consistency.rootVersion}`);
                } else {
                    console.log('❌ Version inconsistencies found:');
                    consistency.inconsistencies.forEach(issue => {
                        console.log(`  ${issue.package}: ${issue.version} (expected: ${issue.expected})`);
                    });
                    process.exit(1);
                }
                break;
                
            case 'update':
                const newVersion = args[0];
                if (!newVersion) {
                    console.error('Error: Version required for update command');
                    console.log('Usage: node version-manager.js update <version>');
                    process.exit(1);
                }
                
                const changes = versionManager.updateAllVersions(newVersion);
                console.log('✅ Version update completed');
                console.log(`Updated ${changes.length} packages to version ${newVersion}`);
                
                const report = versionManager.generateChangeReport(changes);
                console.log('\nChange Summary:');
                Object.entries(report.summary).forEach(([type, count]) => {
                    console.log(`  ${type}: ${count}`);
                });
                break;
                
            case 'generate':
                const releaseType = args[0] || 'patch';
                const currentVersion = versionManager.getCurrentVersion();
                const generatedVersion = versionManager.generateNewVersion(currentVersion, releaseType);
                console.log(generatedVersion);
                break;
                
            case 'workspace-check':
                const workspaceIssues = versionManager.checkWorkspaceDependencies();
                if (workspaceIssues.length === 0) {
                    console.log('✅ Workspace dependencies are correctly configured');
                } else {
                    console.log('❌ Workspace dependency issues found:');
                    workspaceIssues.forEach(issue => {
                        console.log(`  ${issue.package} -> ${issue.dependency}: ${issue.issue}`);
                        console.log(`    Current: ${issue.currentVersion}`);
                        console.log(`    Suggested: ${issue.suggestion}`);
                    });
                    process.exit(1);
                }
                break;
                
            case 'workspace-fix':
                const fixes = versionManager.fixWorkspaceDependencies();
                if (fixes.length === 0) {
                    console.log('✅ No workspace dependency fixes needed');
                } else {
                    console.log(`✅ Fixed ${fixes.length} workspace dependencies:`);
                    fixes.forEach(fix => {
                        console.log(`  ${fix.package} -> ${fix.dependency}: ${fix.oldVersion} → ${fix.newVersion}`);
                    });
                }
                break;
                
            case 'list':
                const versions = versionManager.getAllPackageVersions();
                console.log('📦 Package Versions:');
                Object.entries(versions).forEach(([name, version]) => {
                    console.log(`  ${name}: ${version}`);
                });
                break;
                
            default:
                console.log(`
Version Manager - AI Component版本管理工具

用法: node version-manager.js <command> [args]

命令:
  current           显示当前版本
  check             检查版本一致性
  update <version>  更新所有包到指定版本
  generate [type]   生成新版本号 (type: major|minor|patch|prerelease)
  workspace-check   检查workspace依赖配置
  workspace-fix     修复workspace依赖配置
  list              列出所有包的版本

示例:
  node version-manager.js current
  node version-manager.js update 1.2.3
  node version-manager.js generate minor
  node version-manager.js check
`);
                break;
        }
    } catch (error) {
        console.error('Error:', error.message);
        process.exit(1);
    }
}

module.exports = VersionManager;