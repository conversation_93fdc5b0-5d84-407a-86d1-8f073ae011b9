#!/usr/bin/env node

/**
 * 构建验证和测试脚本
 * 验证构建输出的完整性、兼容性和性能
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const { getAllPackages, formatBytes } = require('../build.config')

class BuildValidator {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      strict: false,
      skipTypeCheck: false,
      skipSizeCheck: false,
      ...options
    }
    
    this.packages = getAllPackages()
    this.validationResults = new Map()
    this.errors = []
    this.warnings = []
  }

  // 主验证流程
  async validate() {
    console.log('🔍 开始构建验证...')
    
    try {
      // 验证每个包
      for (const pkg of this.packages) {
        await this.validatePackage(pkg)
      }
      
      // 验证包之间的依赖关系
      await this.validateDependencies()
      
      // 验证导出完整性
      await this.validateExports()
      
      // 生成验证报告
      await this.generateValidationReport()
      
      // 输出结果
      this.printValidationSummary()
      
      if (this.errors.length > 0) {
        console.error('\n❌ 验证失败，发现错误')
        process.exit(1)
      } else {
        console.log('\n✅ 构建验证通过')
      }
      
    } catch (error) {
      console.error('\n❌ 验证过程失败:', error.message)
      process.exit(1)
    }
  }

  // 验证单个包
  async validatePackage(pkg) {
    console.log(`  🔍 验证包: ${pkg.name}`)
    
    const result = {
      package: pkg.name,
      status: 'success',
      checks: {},
      errors: [],
      warnings: [],
      stats: {}
    }
    
    try {
      // 1. 验证构建输出存在
      result.checks.buildOutput = await this.validateBuildOutput(pkg)
      
      // 2. 验证文件完整性
      result.checks.fileIntegrity = await this.validateFileIntegrity(pkg)
      
      // 3. 验证模块导出
      result.checks.moduleExports = await this.validateModuleExports(pkg)
      
      // 4. 验证类型声明
      if (!this.options.skipTypeCheck) {
        result.checks.typeDeclarations = await this.validateTypeDeclarations(pkg)
      }
      
      // 5. 验证文件大小
      if (!this.options.skipSizeCheck) {
        result.checks.fileSize = await this.validateFileSize(pkg)
      }
      
      // 6. 验证平台兼容性
      if (pkg.name === 'ui') {
        result.checks.platformCompatibility = await this.validatePlatformCompatibility(pkg)
      }
      
      // 7. 验证依赖关系
      result.checks.dependencies = await this.validatePackageDependencies(pkg)
      
      // 收集统计信息
      result.stats = await this.collectPackageStats(pkg)
      
    } catch (error) {
      result.status = 'error'
      result.errors.push(error.message)
      this.errors.push(`${pkg.name}: ${error.message}`)
    }
    
    // 收集警告
    Object.values(result.checks).forEach(check => {
      if (check.warnings) {
        result.warnings.push(...check.warnings)
        this.warnings.push(...check.warnings.map(w => `${pkg.name}: ${w}`))
      }
      if (check.errors) {
        result.errors.push(...check.errors)
        this.errors.push(...check.errors.map(e => `${pkg.name}: ${e}`))
      }
    })
    
    this.validationResults.set(pkg.name, result)
    
    const statusIcon = result.status === 'success' ? '✅' : '❌'
    console.log(`    ${statusIcon} ${pkg.name} 验证${result.status === 'success' ? '通过' : '失败'}`)
    
    if (result.warnings.length > 0 && this.options.verbose) {
      result.warnings.forEach(warning => {
        console.log(`      ⚠️  ${warning}`)
      })
    }
  }

  // 验证构建输出
  async validateBuildOutput(pkg) {
    const result = { status: 'success', errors: [], warnings: [] }
    const distPath = path.join(pkg.path, 'dist')
    
    if (!fs.existsSync(distPath)) {
      result.status = 'error'
      result.errors.push('构建输出目录不存在')
      return result
    }
    
    const expectedFiles = this.getExpectedFiles(pkg)
    
    for (const file of expectedFiles) {
      const filePath = path.join(distPath, file)
      if (!fs.existsSync(filePath)) {
        result.errors.push(`缺少预期文件: ${file}`)
        result.status = 'error'
      }
    }
    
    return result
  }

  // 获取预期文件列表
  getExpectedFiles(pkg) {
    const baseFiles = ['index.cjs.js', 'index.esm.js', 'index.d.ts']
    
    if (pkg.name === 'ui') {
      return [
        ...baseFiles,
        'web/index.esm.js',
        'web/index.cjs.js', 
        'web/index.d.ts',
        'vue/index.esm.js',
        'vue/index.cjs.js',
        'vue/index.d.ts',
        'taro/index.esm.js',
        'taro/index.cjs.js',
        'taro/index.d.ts',
        'style/index.css'
      ]
    }
    
    return baseFiles
  }

  // 验证文件完整性
  async validateFileIntegrity(pkg) {
    const result = { status: 'success', errors: [], warnings: [] }
    const distPath = path.join(pkg.path, 'dist')
    
    const files = this.getAllFiles(distPath)
    
    for (const file of files) {
      try {
        // 验证JavaScript文件语法
        if (file.endsWith('.js')) {
          await this.validateJavaScriptSyntax(file)
        }
        
        // 验证TypeScript声明文件
        if (file.endsWith('.d.ts')) {
          await this.validateTypeScriptDeclaration(file)
        }
        
        // 验证CSS文件
        if (file.endsWith('.css')) {
          await this.validateCSSFile(file)
        }
        
      } catch (error) {
        result.errors.push(`文件 ${path.relative(distPath, file)} 验证失败: ${error.message}`)
        result.status = 'error'
      }
    }
    
    return result
  }

  // 验证JavaScript语法
  async validateJavaScriptSyntax(filePath) {
    const content = fs.readFileSync(filePath, 'utf8')
    
    try {
      // 简单的语法检查 - 在实际项目中可以使用 ESLint 或其他工具
      new Function(content)
    } catch (error) {
      throw new Error(`JavaScript语法错误: ${error.message}`)
    }
    
    // 检查是否包含源映射
    if (!content.includes('//# sourceMappingURL=') && this.options.strict) {
      throw new Error('缺少源映射')
    }
  }

  // 验证TypeScript声明文件
  async validateTypeScriptDeclaration(filePath) {
    const content = fs.readFileSync(filePath, 'utf8')
    
    // 基本语法检查
    const issues = []
    
    // 检查是否有未解析的导入
    const importMatches = content.match(/import.*from ['"]([^'"]+)['"]/g)
    if (importMatches) {
      importMatches.forEach(match => {
        const moduleName = match.match(/from ['"]([^'"]+)['"]/)[1]
        if (moduleName.startsWith('./') || moduleName.startsWith('../')) {
          // 相对路径导入 - 检查文件是否存在
          const importPath = path.resolve(path.dirname(filePath), moduleName)
          if (!fs.existsSync(importPath) && !fs.existsSync(importPath + '.d.ts')) {
            issues.push(`类型声明中的导入路径不存在: ${moduleName}`)
          }
        }
      })
    }
    
    if (issues.length > 0) {
      throw new Error(issues.join('; '))
    }
  }

  // 验证CSS文件
  async validateCSSFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8')
    
    // 基本CSS语法检查
    const braceCount = (content.match(/{/g) || []).length - (content.match(/}/g) || []).length
    if (braceCount !== 0) {
      throw new Error('CSS括号不匹配')
    }
  }

  // 验证模块导出
  async validateModuleExports(pkg) {
    const result = { status: 'success', errors: [], warnings: [] }
    
    try {
      // 检查主入口文件
      const mainEntry = path.join(pkg.path, 'dist', 'index.esm.js')
      if (fs.existsSync(mainEntry)) {
        const hasExports = await this.checkModuleHasExports(mainEntry)
        if (!hasExports) {
          result.warnings.push('主入口文件没有导出内容')
        }
      }
      
      // 检查package.json中声明的导出
      const packageJson = JSON.parse(fs.readFileSync(path.join(pkg.path, 'package.json'), 'utf8'))
      if (packageJson.exports) {
        for (const [exportPath, targetPath] of Object.entries(packageJson.exports)) {
          if (typeof targetPath === 'string') {
            const fullPath = path.resolve(pkg.path, targetPath)
            if (!fs.existsSync(fullPath)) {
              result.errors.push(`导出路径不存在: ${exportPath} -> ${targetPath}`)
              result.status = 'error'
            }
          }
        }
      }
      
    } catch (error) {
      result.errors.push(`模块导出验证失败: ${error.message}`)
      result.status = 'error'
    }
    
    return result
  }

  // 检查模块是否有导出
  async checkModuleHasExports(filePath) {
    const content = fs.readFileSync(filePath, 'utf8')
    return /export\s+/.test(content) || /module\.exports\s*=/.test(content)
  }

  // 验证类型声明
  async validateTypeDeclarations(pkg) {
    const result = { status: 'success', errors: [], warnings: [] }
    
    try {
      const distPath = path.join(pkg.path, 'dist')
      const dtsFiles = this.getAllFiles(distPath).filter(f => f.endsWith('.d.ts'))
      
      if (dtsFiles.length === 0) {
        result.warnings.push('没有找到类型声明文件')
        return result
      }
      
      // 使用TypeScript编译器检查类型声明
      try {
        execSync(`npx tsc --noEmit --skipLibCheck ${dtsFiles.join(' ')}`, {
          cwd: pkg.path,
          stdio: 'pipe'
        })
      } catch (error) {
        const output = error.stdout?.toString() || error.stderr?.toString()
        if (output.includes('error TS')) {
          result.errors.push(`TypeScript类型错误: ${output}`)
          result.status = 'error'
        }
      }
      
    } catch (error) {
      result.warnings.push(`类型声明验证跳过: ${error.message}`)
    }
    
    return result
  }

  // 验证文件大小
  async validateFileSize(pkg) {
    const result = { status: 'success', errors: [], warnings: [], stats: {} }
    const distPath = path.join(pkg.path, 'dist')
    
    const files = this.getAllFiles(distPath)
    let totalSize = 0
    
    const sizeLimits = {
      'index.esm.js': 100 * 1024, // 100KB
      'index.cjs.js': 100 * 1024,
      'index.umd.js': 150 * 1024  // 150KB
    }
    
    for (const file of files) {
      const stat = fs.statSync(file)
      const fileName = path.basename(file)
      const size = stat.size
      
      totalSize += size
      
      // 检查单文件大小限制
      if (sizeLimits[fileName] && size > sizeLimits[fileName]) {
        result.warnings.push(`文件 ${fileName} 大小超过建议值: ${formatBytes(size)} > ${formatBytes(sizeLimits[fileName])}`)
      }
    }
    
    result.stats.totalSize = totalSize
    result.stats.totalSizeFormatted = formatBytes(totalSize)
    
    // 检查总大小限制
    const totalSizeLimit = pkg.name === 'ui' ? 500 * 1024 : 200 * 1024 // UI包500KB，其他200KB
    if (totalSize > totalSizeLimit) {
      result.warnings.push(`包总大小超过建议值: ${formatBytes(totalSize)} > ${formatBytes(totalSizeLimit)}`)
    }
    
    return result
  }

  // 验证平台兼容性（UI包）
  async validatePlatformCompatibility(pkg) {
    const result = { status: 'success', errors: [], warnings: [] }
    const platforms = ['web', 'vue', 'taro']
    
    for (const platform of platforms) {
      const platformPath = path.join(pkg.path, 'dist', platform)
      
      if (!fs.existsSync(platformPath)) {
        result.errors.push(`缺少 ${platform} 平台构建`)
        result.status = 'error'
        continue
      }
      
      // 验证平台特定依赖
      const platformEntry = path.join(platformPath, 'index.esm.js')
      if (fs.existsSync(platformEntry)) {
        const content = fs.readFileSync(platformEntry, 'utf8')
        
        // 检查是否使用了正确的平台依赖
        const platformDeps = this.getPlatformDependencies(platform)
        platformDeps.required.forEach(dep => {
          if (!content.includes(dep) && this.options.strict) {
            result.warnings.push(`${platform} 平台可能缺少必要依赖: ${dep}`)
          }
        })
        
        // 检查是否误用了其他平台的依赖
        platformDeps.forbidden.forEach(dep => {
          if (content.includes(dep)) {
            result.errors.push(`${platform} 平台不应使用: ${dep}`)
            result.status = 'error'
          }
        })
      }
    }
    
    return result
  }

  // 获取平台依赖要求
  getPlatformDependencies(platform) {
    const deps = {
      web: {
        required: [],
        forbidden: ['@tarojs/', 'vue']
      },
      vue: {
        required: [],
        forbidden: ['react', '@tarojs/']
      },
      taro: {
        required: [],
        forbidden: ['react-dom', 'vue']
      }
    }
    
    return deps[platform] || { required: [], forbidden: [] }
  }

  // 验证包依赖关系
  async validatePackageDependencies(pkg) {
    const result = { status: 'success', errors: [], warnings: [] }
    
    try {
      const packageJson = JSON.parse(fs.readFileSync(path.join(pkg.path, 'package.json'), 'utf8'))
      
      // 检查内部依赖版本一致性
      const internalDeps = Object.keys(packageJson.dependencies || {})
        .filter(dep => dep.startsWith('@leyaoyao/ai-component-'))
      
      for (const dep of internalDeps) {
        const depVersion = packageJson.dependencies[dep]
        if (depVersion !== pkg.version && !depVersion.startsWith('^') && !depVersion.startsWith('~')) {
          result.warnings.push(`内部依赖版本不一致: ${dep}@${depVersion} vs ${pkg.version}`)
        }
      }
      
      // 检查peer dependencies
      const peerDeps = Object.keys(packageJson.peerDependencies || {})
      if (peerDeps.length > 0) {
        for (const peerDep of peerDeps) {
          // 验证peer dependency在构建产物中被正确外部化
          const mainFile = path.join(pkg.path, 'dist', 'index.esm.js')
          if (fs.existsSync(mainFile)) {
            const content = fs.readFileSync(mainFile, 'utf8')
            if (content.includes(`from '${peerDep}'`) || content.includes(`require('${peerDep}')`)) {
              result.errors.push(`Peer dependency ${peerDep} 应该被外部化`)
              result.status = 'error'
            }
          }
        }
      }
      
    } catch (error) {
      result.errors.push(`依赖验证失败: ${error.message}`)
      result.status = 'error'
    }
    
    return result
  }

  // 验证全局依赖关系
  async validateDependencies() {
    console.log('  🔗 验证包之间依赖关系...')
    
    const depGraph = new Map()
    
    // 构建依赖图
    for (const pkg of this.packages) {
      const packageJson = JSON.parse(fs.readFileSync(path.join(pkg.path, 'package.json'), 'utf8'))
      const internalDeps = Object.keys(packageJson.dependencies || {})
        .filter(dep => dep.startsWith('@leyaoyao/ai-component-'))
        .map(dep => dep.replace('@leyaoyao/ai-component-', ''))
      
      depGraph.set(pkg.name, internalDeps)
    }
    
    // 检查循环依赖
    const visited = new Set()
    const recursionStack = new Set()
    
    const hasCycle = (node) => {
      visited.add(node)
      recursionStack.add(node)
      
      const neighbors = depGraph.get(node) || []
      
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          if (hasCycle(neighbor)) return true
        } else if (recursionStack.has(neighbor)) {
          this.errors.push(`检测到循环依赖: ${node} -> ${neighbor}`)
          return true
        }
      }
      
      recursionStack.delete(node)
      return false
    }
    
    for (const pkg of this.packages) {
      if (!visited.has(pkg.name)) {
        hasCycle(pkg.name)
      }
    }
  }

  // 验证导出完整性
  async validateExports() {
    console.log('  📤 验证导出完整性...')
    
    for (const pkg of this.packages) {
      const packageJson = JSON.parse(fs.readFileSync(path.join(pkg.path, 'package.json'), 'utf8'))
      const exports = packageJson.exports || {}
      
      for (const [exportPath, targetPath] of Object.entries(exports)) {
        if (typeof targetPath === 'object') {
          // 检查条件导出
          for (const [condition, actualPath] of Object.entries(targetPath)) {
            const fullPath = path.resolve(pkg.path, actualPath)
            if (!fs.existsSync(fullPath)) {
              this.errors.push(`${pkg.name}: 导出文件不存在 ${exportPath}[${condition}] -> ${actualPath}`)
            }
          }
        } else if (typeof targetPath === 'string') {
          const fullPath = path.resolve(pkg.path, targetPath)
          if (!fs.existsSync(fullPath)) {
            this.errors.push(`${pkg.name}: 导出文件不存在 ${exportPath} -> ${targetPath}`)
          }
        }
      }
    }
  }

  // 收集包统计信息
  async collectPackageStats(pkg) {
    const stats = {
      fileCount: 0,
      totalSize: 0,
      largestFile: null,
      fileTypes: {}
    }
    
    const distPath = path.join(pkg.path, 'dist')
    if (!fs.existsSync(distPath)) return stats
    
    const files = this.getAllFiles(distPath)
    
    let largestSize = 0
    
    for (const file of files) {
      const stat = fs.statSync(file)
      const ext = path.extname(file)
      
      stats.fileCount++
      stats.totalSize += stat.size
      
      if (stat.size > largestSize) {
        largestSize = stat.size
        stats.largestFile = {
          path: path.relative(distPath, file),
          size: stat.size,
          sizeFormatted: formatBytes(stat.size)
        }
      }
      
      stats.fileTypes[ext] = (stats.fileTypes[ext] || 0) + 1
    }
    
    stats.totalSizeFormatted = formatBytes(stats.totalSize)
    
    return stats
  }

  // 生成验证报告
  async generateValidationReport() {
    const report = {
      summary: {
        packagesValidated: this.validationResults.size,
        totalErrors: this.errors.length,
        totalWarnings: this.warnings.length,
        overallStatus: this.errors.length === 0 ? 'success' : 'failed'
      },
      packages: {},
      errors: this.errors,
      warnings: this.warnings
    }
    
    this.validationResults.forEach((result, packageName) => {
      report.packages[packageName] = result
    })
    
    const reportPath = path.join(process.cwd(), 'build-validation-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    
    console.log(`📊 验证报告已生成: ${reportPath}`)
  }

  // 打印验证摘要
  printValidationSummary() {
    console.log('\n📊 验证摘要:')
    console.log('┌─────────────────┬──────────┬──────────┬──────────┐')
    console.log('│ 包名            │ 状态     │ 错误     │ 警告     │')
    console.log('├─────────────────┼──────────┼──────────┼──────────┤')
    
    this.validationResults.forEach((result, packageName) => {
      const status = result.status === 'success' ? '✅ 通过' : '❌ 失败'
      const errors = result.errors.length.toString()
      const warnings = result.warnings.length.toString()
      
      console.log(`│ ${packageName.padEnd(15)} │ ${status.padEnd(8)} │ ${errors.padEnd(8)} │ ${warnings.padEnd(8)} │`)
    })
    
    console.log('├─────────────────┼──────────┼──────────┼──────────┤')
    console.log(`│ 总计            │ ${this.validationResults.size.toString().padEnd(8)} │ ${this.errors.length.toString().padEnd(8)} │ ${this.warnings.length.toString().padEnd(8)} │`)
    console.log('└─────────────────┴──────────┴──────────┴──────────┘')
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️  警告:')
      this.warnings.forEach(warning => console.log(`  ${warning}`))
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ 错误:')
      this.errors.forEach(error => console.log(`  ${error}`))
    }
  }

  // 获取目录下所有文件
  getAllFiles(dir) {
    const files = []
    
    if (!fs.existsSync(dir)) return files
    
    const walk = (currentDir) => {
      const entries = fs.readdirSync(currentDir, { withFileTypes: true })
      
      for (const entry of entries) {
        const fullPath = path.join(currentDir, entry.name)
        
        if (entry.isFile()) {
          files.push(fullPath)
        } else if (entry.isDirectory()) {
          walk(fullPath)
        }
      }
    }
    
    walk(dir)
    return files
  }
}

// CLI 入口
async function main() {
  const args = process.argv.slice(2)
  const options = {}
  
  // 解析参数
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--verbose':
        options.verbose = true
        break
      case '--strict':
        options.strict = true
        break
      case '--skip-types':
        options.skipTypeCheck = true
        break
      case '--skip-size':
        options.skipSizeCheck = true
        break
      case '--help':
        console.log(`
构建验证脚本

用法:
  node validate-build.js [选项]

选项:
  --verbose      详细输出
  --strict       严格模式
  --skip-types   跳过类型检查
  --skip-size    跳过大小检查
  --help         显示帮助

示例:
  node validate-build.js --verbose --strict
        `)
        process.exit(0)
        break
    }
  }
  
  const validator = new BuildValidator(options)
  await validator.validate()
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ 验证失败:', error.message)
    process.exit(1)
  })
}

module.exports = BuildValidator