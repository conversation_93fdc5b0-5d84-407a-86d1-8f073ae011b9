#!/usr/bin/env node

/**
 * Simple test to verify Node.js can run the publish process
 */

const path = require('path');
const fs = require('fs');

console.log('Testing publish environment...\n');

// Test 1: Check Node.js version
console.log('1. Node.js version:', process.version);

// Test 2: Check current directory
console.log('2. Current directory:', process.cwd());

// Test 3: Check if package.json is accessible
const packageJsonPath = path.join(process.cwd(), 'package.json');
console.log('3. Package.json path:', packageJsonPath);
console.log('   Exists:', fs.existsSync(packageJsonPath));

if (fs.existsSync(packageJsonPath)) {
    try {
        const pkg = require(packageJsonPath);
        console.log('   Name:', pkg.name);
        console.log('   Version:', pkg.version);
    } catch (err) {
        console.error('   Error reading package.json:', err.message);
    }
}

// Test 4: Check npm/pnpm
const { execSync } = require('child_process');
try {
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    console.log('4. npm version:', npmVersion);
} catch (err) {
    console.error('4. npm not found');
}

try {
    const pnpmVersion = execSync('pnpm --version', { encoding: 'utf8' }).trim();
    console.log('5. pnpm version:', pnpmVersion);
} catch (err) {
    console.error('5. pnpm not found');
}

console.log('\nEnvironment test complete.');
console.log('\nTo run the publish script, use one of these commands:');
console.log('  - pnpm run publish');
console.log('  - npm run publish');
console.log('  - node scripts/run-publish.js');

if (process.platform === 'win32') {
    console.log('\nOn Windows, you can also use:');
    console.log('  - pnpm run publish:cmd');
    console.log('  - scripts\\build-and-publish.cmd');
}