#!/usr/bin/env node

/**
 * Pre-Publish Check - 发布前检查脚本
 * 
 * 功能：
 * - 验证package.json配置
 * - 检查构建产物完整性
 * - 验证exports字段配置
 * - 检查依赖安全性
 * - 验证文件完整性
 * - 检查npm registry配置
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PrePublishChecker {
    constructor(rootDir = process.cwd()) {
        this.rootDir = rootDir;
        this.packagesDir = path.join(rootDir, 'packages');
        this.packageDirs = ['adapters', 'config', 'core', 'ui'];
        this.companyScope = '@leyaoyao';
        this.errors = [];
        this.warnings = [];
    }

    /**
     * 记录错误
     */
    addError(message, package = 'root') {
        this.errors.push({ package, message, type: 'error' });
        console.log(`❌ [${package}] ${message}`);
    }

    /**
     * 记录警告
     */
    addWarning(message, package = 'root') {
        this.warnings.push({ package, message, type: 'warning' });
        console.log(`⚠️  [${package}] ${message}`);
    }

    /**
     * 记录成功
     */
    logSuccess(message, package = 'root') {
        console.log(`✅ [${package}] ${message}`);
    }

    /**
     * 检查package.json配置
     */
    checkPackageJson(packageDir, packageName) {
        const packageJsonPath = path.join(this.packagesDir, packageDir, 'package.json');
        
        if (!fs.existsSync(packageJsonPath)) {
            this.addError(`package.json不存在: ${packageJsonPath}`, packageName);
            return false;
        }

        try {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            // 必需字段检查
            const requiredFields = ['name', 'version', 'description', 'main', 'types'];
            for (const field of requiredFields) {
                if (!packageJson[field]) {
                    this.addError(`缺少必需字段: ${field}`, packageName);
                }
            }

            // 名称规范检查
            if (!packageJson.name.startsWith(this.companyScope)) {
                this.addError(`包名应以 ${this.companyScope} 开头`, packageName);
            }

            // 版本格式检查
            if (packageJson.version && !packageJson.version.match(/^\d+\.\d+\.\d+/)) {
                this.addError(`版本格式无效: ${packageJson.version}`, packageName);
            }

            // license检查
            if (!packageJson.license) {
                this.addWarning('缺少license字段', packageName);
            }

            // keywords检查
            if (!packageJson.keywords || packageJson.keywords.length === 0) {
                this.addWarning('缺少keywords字段', packageName);
            }

            // files字段检查
            if (!packageJson.files || packageJson.files.length === 0) {
                this.addWarning('缺少files字段，可能导致发布不完整', packageName);
            }

            // exports字段检查
            if (packageJson.exports) {
                this.checkExportsField(packageJson.exports, packageDir, packageName);
            }

            // 依赖检查
            this.checkDependencies(packageJson, packageName);

            this.logSuccess('package.json配置检查通过', packageName);
            return true;

        } catch (error) {
            this.addError(`package.json解析失败: ${error.message}`, packageName);
            return false;
        }
    }

    /**
     * 检查exports字段配置
     */
    checkExportsField(exports, packageDir, packageName) {
        const packagePath = path.join(this.packagesDir, packageDir);
        
        const checkExportPath = (exportPath, exportKey) => {
            if (typeof exportPath === 'string') {
                const fullPath = path.join(packagePath, exportPath);
                if (!fs.existsSync(fullPath)) {
                    this.addError(`exports字段指向的文件不存在: ${exportKey} -> ${exportPath}`, packageName);
                }
            } else if (typeof exportPath === 'object') {
                Object.values(exportPath).forEach(subPath => {
                    if (typeof subPath === 'string') {
                        const fullPath = path.join(packagePath, subPath);
                        if (!fs.existsSync(fullPath)) {
                            this.addError(`exports字段指向的文件不存在: ${exportKey} -> ${subPath}`, packageName);
                        }
                    }
                });
            }
        };

        Object.entries(exports).forEach(([key, value]) => {
            checkExportPath(value, key);
        });
    }

    /**
     * 检查依赖配置
     */
    checkDependencies(packageJson, packageName) {
        // 检查workspace依赖
        ['dependencies', 'devDependencies'].forEach(depType => {
            if (packageJson[depType]) {
                Object.entries(packageJson[depType]).forEach(([dep, version]) => {
                    if (dep.startsWith(`${this.companyScope}/ai-component-`)) {
                        const targetPackage = dep.replace(`${this.companyScope}/ai-component-`, '');
                        if (this.packageDirs.includes(targetPackage)) {
                            // 内部依赖应该使用workspace:*格式
                            if (version !== 'workspace:*' && !version.startsWith('^')) {
                                this.addWarning(`内部依赖建议使用workspace:*格式: ${dep}`, packageName);
                            }
                        }
                    }
                });
            }
        });

        // 检查peerDependencies
        if (packageJson.peerDependencies) {
            Object.entries(packageJson.peerDependencies).forEach(([dep, version]) => {
                if (!version.includes('>=') && !version.includes('^') && !version.includes('~')) {
                    this.addWarning(`peerDependency建议使用范围版本: ${dep}@${version}`, packageName);
                }
            });
        }
    }

    /**
     * 检查构建产物
     */
    checkBuildArtifacts(packageDir, packageName) {
        const packagePath = path.join(this.packagesDir, packageDir);
        const distPath = path.join(packagePath, 'dist');
        
        if (!fs.existsSync(distPath)) {
            this.addError('缺少dist目录，请先运行构建', packageName);
            return false;
        }

        const packageJsonPath = path.join(packagePath, 'package.json');
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

        // 检查main、module、types字段指向的文件
        const checkField = (field) => {
            if (packageJson[field]) {
                const filePath = path.join(packagePath, packageJson[field]);
                if (!fs.existsSync(filePath)) {
                    this.addError(`${field}字段指向的文件不存在: ${packageJson[field]}`, packageName);
                    return false;
                }
            }
            return true;
        };

        let allFilesExist = true;
        allFilesExist &= checkField('main');
        allFilesExist &= checkField('module');
        allFilesExist &= checkField('types');

        // 检查常见的构建产物
        const expectedFiles = [
            'dist/index.cjs.js',
            'dist/index.esm.js', 
            'dist/index.d.ts'
        ];

        for (const file of expectedFiles) {
            const filePath = path.join(packagePath, file);
            if (!fs.existsSync(filePath)) {
                this.addWarning(`预期的构建产物不存在: ${file}`, packageName);
            }
        }

        // 检查构建产物大小
        this.checkBuildSize(packagePath, packageName);

        if (allFilesExist) {
            this.logSuccess('构建产物检查通过', packageName);
        }

        return allFilesExist;
    }

    /**
     * 检查构建产物大小
     */
    checkBuildSize(packagePath, packageName) {
        const distPath = path.join(packagePath, 'dist');
        
        try {
            const stats = this.getDirSize(distPath);
            const sizeMB = stats / (1024 * 1024);
            
            if (sizeMB > 10) {
                this.addWarning(`构建产物较大: ${sizeMB.toFixed(2)}MB`, packageName);
            } else {
                this.logSuccess(`构建产物大小: ${sizeMB.toFixed(2)}MB`, packageName);
            }
        } catch (error) {
            this.addWarning(`无法计算构建产物大小: ${error.message}`, packageName);
        }
    }

    /**
     * 计算目录大小
     */
    getDirSize(dirPath) {
        let size = 0;
        
        const files = fs.readdirSync(dirPath);
        for (const file of files) {
            const filePath = path.join(dirPath, file);
            const stats = fs.statSync(filePath);
            
            if (stats.isDirectory()) {
                size += this.getDirSize(filePath);
            } else {
                size += stats.size;
            }
        }
        
        return size;
    }

    /**
     * 检查安全性
     */
    checkSecurity() {
        console.log('\n🔍 安全性检查...');
        
        try {
            // 检查npm audit
            execSync('npm audit --audit-level=high --json', { 
                cwd: this.rootDir,
                stdio: 'pipe'
            });
            this.logSuccess('npm audit检查通过');
        } catch (error) {
            try {
                const auditResult = JSON.parse(error.stdout.toString());
                if (auditResult.metadata && auditResult.metadata.vulnerabilities) {
                    const { vulnerabilities } = auditResult.metadata;
                    const totalVulns = Object.values(vulnerabilities).reduce((sum, count) => sum + count, 0);
                    
                    if (totalVulns > 0) {
                        this.addWarning(`发现 ${totalVulns} 个安全漏洞，建议修复后发布`);
                    }
                }
            } catch (parseError) {
                this.addWarning('无法解析npm audit结果');
            }
        }

        // 检查敏感文件
        this.checkSensitiveFiles();
    }

    /**
     * 检查敏感文件
     */
    checkSensitiveFiles() {
        const sensitivePatterns = [
            '.env',
            '.env.local',
            '.env.production',
            '*.pem',
            '*.p12',
            '*.key',
            '**/node_modules/**',
            '**/.git/**'
        ];

        const checkDirectory = (dir, packageName) => {
            if (!fs.existsSync(dir)) return;
            
            const files = fs.readdirSync(dir, { withFileTypes: true });
            
            for (const file of files) {
                const filePath = path.join(dir, file.name);
                
                if (file.isDirectory()) {
                    // 跳过node_modules和.git目录
                    if (file.name !== 'node_modules' && file.name !== '.git') {
                        checkDirectory(filePath, packageName);
                    }
                } else {
                    // 检查敏感文件
                    for (const pattern of sensitivePatterns) {
                        const cleanPattern = pattern.replace('**/', '').replace('*', '');
                        if (file.name.includes(cleanPattern) || file.name.endsWith(cleanPattern)) {
                            this.addWarning(`发现潜在敏感文件: ${file.name}`, packageName);
                        }
                    }
                }
            }
        };

        // 检查每个package
        for (const packageDir of this.packageDirs) {
            const packagePath = path.join(this.packagesDir, packageDir);
            const packageName = `${this.companyScope}/ai-component-${packageDir}`;
            checkDirectory(packagePath, packageName);
        }
    }

    /**
     * 检查git状态
     */
    checkGitStatus() {
        console.log('\n📋 Git状态检查...');
        
        try {
            const status = execSync('git status --porcelain', { 
                cwd: this.rootDir,
                encoding: 'utf8'
            });
            
            if (status.trim()) {
                this.addWarning('工作区有未提交的变更');
                console.log('未提交的文件:');
                console.log(status);
            } else {
                this.logSuccess('工作区干净');
            }
        } catch (error) {
            this.addWarning('无法检查git状态');
        }

        // 检查当前分支
        try {
            const branch = execSync('git rev-parse --abbrev-ref HEAD', {
                cwd: this.rootDir,
                encoding: 'utf8'
            }).trim();
            
            if (branch !== 'master' && branch !== 'main') {
                this.addWarning(`当前分支不是主分支: ${branch}`);
            } else {
                this.logSuccess(`当前分支: ${branch}`);
            }
        } catch (error) {
            this.addWarning('无法获取当前分支信息');
        }
    }

    /**
     * 检查npm配置
     */
    checkNpmConfig() {
        console.log('\n📦 NPM配置检查...');
        
        try {
            const registry = execSync('npm config get registry', {
                encoding: 'utf8'
            }).trim();
            
            console.log(`当前registry: ${registry}`);
            
            // 检查是否已配置认证
            try {
                execSync('npm whoami', { stdio: 'pipe' });
                this.logSuccess('NPM认证状态正常');
            } catch (error) {
                this.addWarning('NPM未认证，发布前需要登录');
            }
            
        } catch (error) {
            this.addWarning('无法检查npm配置');
        }
    }

    /**
     * 生成检查报告
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                errors: this.errors.length,
                warnings: this.warnings.length,
                total: this.errors.length + this.warnings.length
            },
            details: {
                errors: this.errors,
                warnings: this.warnings
            },
            recommendation: this.errors.length === 0 ? 'READY_TO_PUBLISH' : 'FIX_ERRORS_FIRST'
        };

        return report;
    }

    /**
     * 运行所有检查
     */
    async runAllChecks() {
        console.log('🚀 开始发布前检查...\n');

        // 1. 检查每个package
        console.log('📋 Package配置检查...');
        for (const packageDir of this.packageDirs) {
            const packageName = `${this.companyScope}/ai-component-${packageDir}`;
            this.checkPackageJson(packageDir, packageName);
            this.checkBuildArtifacts(packageDir, packageName);
        }

        // 2. 安全性检查
        this.checkSecurity();

        // 3. Git状态检查
        this.checkGitStatus();

        // 4. NPM配置检查
        this.checkNpmConfig();

        // 5. 生成报告
        const report = this.generateReport();
        
        console.log('\n📊 检查完成');
        console.log(`❌ 错误: ${report.summary.errors}`);
        console.log(`⚠️  警告: ${report.summary.warnings}`);
        
        if (report.summary.errors > 0) {
            console.log('\n❌ 发布前检查失败，请修复以下错误：');
            this.errors.forEach(error => {
                console.log(`  [${error.package}] ${error.message}`);
            });
            return false;
        } else if (report.summary.warnings > 0) {
            console.log('\n⚠️  检查通过但有警告：');
            this.warnings.forEach(warning => {
                console.log(`  [${warning.package}] ${warning.message}`);
            });
            console.log('\n✅ 可以发布，但建议先处理警告');
            return true;
        } else {
            console.log('\n✅ 所有检查通过，可以安全发布！');
            return true;
        }
    }
}

// CLI接口
if (require.main === module) {
    const checker = new PrePublishChecker();
    
    checker.runAllChecks().then(passed => {
        process.exit(passed ? 0 : 1);
    }).catch(error => {
        console.error('检查过程中发生错误:', error);
        process.exit(1);
    });
}

module.exports = PrePublishChecker;