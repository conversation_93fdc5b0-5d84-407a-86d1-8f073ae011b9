#!/usr/bin/env node

/**
 * Publish script with environment setup
 * Usage: node scripts/publish-env.js [username] [password] [options]
 */

const { spawn } = require('child_process');
const path = require('path');

function main() {
    const args = process.argv.slice(2);
    
    // Extract username and password from arguments
    let username, password;
    let remainingArgs = [];
    
    // Look for non-option arguments as credentials
    const nonOptionArgs = args.filter(arg => !arg.startsWith('--'));
    const optionArgs = args.filter(arg => arg.startsWith('--'));
    
    if (nonOptionArgs.length >= 2) {
        username = nonOptionArgs[0];
        password = nonOptionArgs[1];
        remainingArgs = nonOptionArgs.slice(2).concat(optionArgs);
    } else {
        // Fallback to environment variables
        username = process.env.NPM_USERNAME;
        password = process.env.NPM_PASSWORD;
        remainingArgs = args;
    }
    
    if (!username || !password) {
        console.error('❌ Missing credentials!');
        console.log('');
        console.log('Usage:');
        console.log('  node scripts/publish-env.js <username> <password> [options]');
        console.log('');
        console.log('Examples:');
        console.log('  node scripts/publish-env.js daizhitao daizhitao123');
        console.log('  node scripts/publish-env.js daizhitao daizhitao123 --dry-run');
        console.log('  node scripts/publish-env.js daizhitao daizhitao123 1.2.3');
        process.exit(1);
    }
    
    // Set environment variables
    const env = {
        ...process.env,
        NPM_USERNAME: username,
        NPM_PASSWORD: password
    };
    
    console.log(`🔐 Using credentials for: ${username}`);
    console.log('🚀 Starting publish process...\n');
    
    // Run the main publish script
    const publishScript = path.join(__dirname, 'publish-simple.js');
    const child = spawn('node', [publishScript, ...remainingArgs], {
        stdio: 'inherit',
        env: env
    });
    
    child.on('exit', (code) => {
        process.exit(code);
    });
    
    child.on('error', (error) => {
        console.error('Failed to start publish script:', error);
        process.exit(1);
    });
}

if (require.main === module) {
    main();
}