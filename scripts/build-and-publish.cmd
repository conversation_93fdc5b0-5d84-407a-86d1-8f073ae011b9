@echo off
REM =============================================================================
REM AI Component Monorepo Build & Publish Script for Windows
REM =============================================================================
REM This is a Windows wrapper for the bash script
REM =============================================================================

REM Check if Git Bash is available
where git >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Git is not installed or not in PATH
    echo Please install Git for Windows: https://git-scm.com/download/win
    exit /b 1
)

REM Try to find Git Bash - avoid WSL paths
set "GIT_BASH="
if exist "C:\Program Files\Git\bin\bash.exe" (
    set "GIT_BASH=C:\Program Files\Git\bin\bash.exe"
) else if exist "C:\Program Files (x86)\Git\bin\bash.exe" (
    set "GIT_BASH=C:\Program Files (x86)\Git\bin\bash.exe"
) else if exist "%ProgramFiles%\Git\bin\bash.exe" (
    set "GIT_BASH=%ProgramFiles%\Git\bin\bash.exe"
) else if exist "C:\Program Files\Git\usr\bin\bash.exe" (
    set "GIT_BASH=C:\Program Files\Git\usr\bin\bash.exe"
) else if exist "C:\Program Files (x86)\Git\usr\bin\bash.exe" (
    set "GIT_BASH=C:\Program Files (x86)\Git\usr\bin\bash.exe"
)

if "%GIT_BASH%"=="" (
    echo [ERROR] Could not find Git Bash
    echo Please ensure Git for Windows is properly installed
    echo Alternative: Try 'pnpm run publish:ps1' for PowerShell version
    exit /b 1
)

REM Get the directory of this script
set "SCRIPT_DIR=%~dp0"
set "SCRIPT_DIR=%SCRIPT_DIR:~0,-1%"

echo Found Git Bash: %GIT_BASH%
echo Running publish script with Git Bash...

REM Set MSYS_NO_PATHCONV to prevent path conversion issues
set MSYS_NO_PATHCONV=1
set MSYS2_ARG_CONV_EXCL=*

REM Run the bash script with Git Bash
"%GIT_BASH%" "%SCRIPT_DIR%\build-and-publish.sh" %*