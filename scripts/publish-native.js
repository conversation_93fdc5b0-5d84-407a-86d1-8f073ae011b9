#!/usr/bin/env node

/**
 * Native Node.js publish script for AI Component
 * This script runs entirely in Node.js without shell dependencies
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

// Configuration  
const COMPANY_REGISTRY = 'https://npm-registry.leyaoyao.com/repository/lyy-npm-hosted/';
const COMPANY_SCOPE = '@leyaoyao';
const PACKAGES_BUILD_ORDER = ['adapters', 'config', 'core', 'ui'];

// ANSI color codes
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

// Logging functions
function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logError(message) {
    log(`[ERROR] ${message}`, 'red');
}

function logSuccess(message) {
    log(`[SUCCESS] ${message}`, 'green');
}

function logInfo(message) {
    log(`[INFO] ${message}`, 'blue');
}

function logWarning(message) {
    log(`[WARNING] ${message}`, 'yellow');
}

function logStep(message) {
    log(`\n[STEP] ${message}`, 'cyan');
}

// Helper functions
function execCommand(command, options = {}) {
    try {
        return execSync(command, { encoding: 'utf8', ...options });
    } catch (error) {
        throw new Error(`Command failed: ${command}\n${error.message}`);
    }
}

function fileExists(filePath) {
    try {
        return fs.existsSync(filePath);
    } catch {
        return false;
    }
}

function readJson(filePath) {
    return JSON.parse(fs.readFileSync(filePath, 'utf8'));
}

function writeJson(filePath, data) {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2) + '\n');
}

async function promptUser(question) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            rl.close();
            resolve(answer);
        });
    });
}

async function promptPassword(question) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
        terminal: true
    });

    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            rl.close();
            console.log(); // New line after password
            resolve(answer);
        });
        // Hide password input
        rl._writeToOutput = function _writeToOutput(stringToWrite) {
            if (rl.line.length === 0) {
                rl.output.write(stringToWrite);
            } else {
                rl.output.write('*');
            }
        };
    });
}

// Main functions
async function checkDependencies() {
    logStep('Checking system dependencies...');

    // Check Node.js version
    const nodeVersion = process.version;
    logInfo(`Node.js version: ${nodeVersion}`);

    // Check pnpm
    try {
        const pnpmVersion = execCommand('pnpm --version').trim();
        logInfo(`pnpm version: ${pnpmVersion}`);
    } catch {
        logError('pnpm is not installed. Please install pnpm first.');
        process.exit(1);
    }

    // Check git status
    try {
        const gitStatus = execCommand('git status --porcelain');
        if (gitStatus.trim()) {
            logWarning('Git working directory is not clean:');
            console.log(gitStatus);
            const answer = await promptUser('Continue anyway? (y/N): ');
            if (answer.toLowerCase() !== 'y') {
                process.exit(1);
            }
        }
    } catch {
        logWarning('Not a git repository or git not available');
    }

    // Check authentication
    const username = process.env.NPM_USERNAME;
    const password = process.env.NPM_PASSWORD;

    if (!username || !password) {
        logWarning('NPM credentials not set in environment');
        if (!username) {
            process.env.NPM_USERNAME = await promptUser('Enter NPM username: ');
        }
        if (!password) {
            process.env.NPM_PASSWORD = await promptPassword('Enter NPM password: ');
        }
    }

    // Configure npm for Nexus
    logInfo('Configuring Sonatype Nexus authentication...');
    try {
        const username = process.env.NPM_USERNAME;
        const password = process.env.NPM_PASSWORD;
        
        if (!username || !password) {
            logError('Missing NPM credentials');
            process.exit(1);
        }
        
        // Set registry for scope
        execCommand(`npm config set ${COMPANY_SCOPE}:registry ${COMPANY_REGISTRY}`);
        
        // Configure authentication for Nexus
        const registryHost = COMPANY_REGISTRY.replace(/^https?:\/\//, '').replace(/\/$/, '');
        const authString = Buffer.from(`${username}:${password}`).toString('base64');
        
        // Set auth token for the registry
        execCommand(`npm config set //${registryHost}/:_auth ${authString}`);
        
        logSuccess('Nexus authentication configured');
    } catch (error) {
        logError('Failed to configure Nexus authentication');
        throw error;
    }

    logSuccess('Dependency check passed');
}

function getCurrentVersion() {
    const packageJson = path.join(process.cwd(), 'package.json');
    if (fileExists(packageJson)) {
        return readJson(packageJson).version;
    }
    return '0.0.0';
}

function calculateNewVersion(currentVersion, increment = 'patch') {
    const parts = currentVersion.split('.');
    const major = parseInt(parts[0]) || 0;
    const minor = parseInt(parts[1]) || 0;
    const patch = parseInt(parts[2]) || 0;

    switch (increment) {
        case 'major':
            return `${major + 1}.0.0`;
        case 'minor':
            return `${major}.${minor + 1}.0`;
        case 'patch':
        default:
            return `${major}.${minor}.${patch + 1}`;
    }
}

function updatePackageVersions(newVersion) {
    logStep(`Updating package versions to ${newVersion}...`);

    // Update root package.json
    const rootPackageJson = path.join(process.cwd(), 'package.json');
    const rootPkg = readJson(rootPackageJson);
    rootPkg.version = newVersion;
    writeJson(rootPackageJson, rootPkg);

    // Update all packages
    const packagesDir = path.join(process.cwd(), 'packages');
    for (const packageName of PACKAGES_BUILD_ORDER) {
        const packageJsonPath = path.join(packagesDir, packageName, 'package.json');
        if (fileExists(packageJsonPath)) {
            logInfo(`Updating ${COMPANY_SCOPE}/ai-component-${packageName} version`);
            const pkg = readJson(packageJsonPath);
            pkg.version = newVersion;

            // Update workspace dependencies
            ['dependencies', 'devDependencies', 'peerDependencies'].forEach(depType => {
                if (pkg[depType]) {
                    Object.keys(pkg[depType]).forEach(dep => {
                        if (dep.startsWith(`${COMPANY_SCOPE}/ai-component-`) && pkg[depType][dep] === 'workspace:*') {
                            pkg[depType][dep] = `^${newVersion}`;
                        }
                    });
                }
            });

            writeJson(packageJsonPath, pkg);
        }
    }

    logSuccess('Version update completed');
}

async function buildPackages() {
    logStep('Building packages...');

    try {
        logInfo('Installing dependencies...');
        execCommand('pnpm install', { stdio: 'inherit' });

        logInfo('Running build...');
        execCommand('pnpm run build:packages:clean', { stdio: 'inherit' });

        logSuccess('Build completed successfully');
    } catch (error) {
        logError('Build failed');
        throw error;
    }
}

async function publishPackages(isDryRun = false) {
    logStep(`${isDryRun ? '[DRY RUN] ' : ''}Publishing packages to ${COMPANY_REGISTRY}...`);

    const packagesDir = path.join(process.cwd(), 'packages');

    for (const packageName of PACKAGES_BUILD_ORDER) {
        const packageDir = path.join(packagesDir, packageName);
        const packageJsonPath = path.join(packageDir, 'package.json');

        if (fileExists(packageJsonPath)) {
            const pkg = readJson(packageJsonPath);
            const fullPackageName = `${COMPANY_SCOPE}/ai-component-${packageName}`;
            const version = pkg.version;

            logInfo(`${isDryRun ? '[DRY RUN] ' : ''}Publishing ${fullPackageName}@${version}...`);

            if (!isDryRun) {
                try {
                    execCommand(`npm publish --registry="${COMPANY_REGISTRY}" --access=public`, {
                        cwd: packageDir,
                        stdio: 'inherit'
                    });
                    logSuccess(`${fullPackageName}@${version} published successfully`);
                } catch (error) {
                    if (error.message.includes('401')) {
                        logError('Authentication failed: Check username/password');
                    } else if (error.message.includes('403')) {
                        logError('Permission denied: User lacks publish rights');
                    } else if (error.message.includes('409')) {
                        logError(`Version conflict: ${fullPackageName}@${version} already exists`);
                    } else {
                        logError(`Failed to publish ${fullPackageName}`);
                    }
                    throw error;
                }
            }
        }
    }

    if (!isDryRun) {
        logSuccess('All packages published successfully');
    }
}

function cleanup() {
    logInfo('Cleaning up...');
    try {
        const registryHost = COMPANY_REGISTRY.replace(/^https?:\/\//, '').replace(/\/$/, '');
        execCommand(`npm config delete ${COMPANY_SCOPE}:registry`);
        execCommand(`npm config delete //${registryHost}/:_auth`);
    } catch {
        // Ignore cleanup errors
    }
}

// Main execution
async function main() {
    console.log(colors.cyan);
    console.log('🚀 AI Component Monorepo Build & Publish Script');
    console.log('=' .repeat(50));
    console.log(colors.reset);

    const args = process.argv.slice(2);
    const isDryRun = args.includes('--dry-run');
    const skipTests = args.includes('--skip-tests');
    const forcePublish = args.includes('--force');

    let version = args.find(arg => !arg.startsWith('--'));

    try {
        await checkDependencies();

        const currentVersion = getCurrentVersion();
        logInfo(`Current version: ${currentVersion}`);

        if (!version) {
            version = calculateNewVersion(currentVersion);
            logInfo(`Auto-incrementing to: ${version}`);
        }

        if (!isDryRun) {
            updatePackageVersions(version);
        }

        await buildPackages();

        if (!skipTests) {
            logStep('Running tests...');
            try {
                execCommand('pnpm test', { stdio: 'inherit' });
                logSuccess('Tests passed');
            } catch {
                if (!forcePublish) {
                    logError('Tests failed. Use --force to publish anyway');
                    process.exit(1);
                }
                logWarning('Tests failed, but continuing due to --force flag');
            }
        }

        await publishPackages(isDryRun);

        if (!isDryRun) {
            logStep('Creating git tag...');
            try {
                execCommand(`git add -A`);
                execCommand(`git commit -m "chore: release v${version}"`);
                execCommand(`git tag -a v${version} -m "Release v${version}"`);
                logSuccess(`Created tag v${version}`);
            } catch {
                logWarning('Failed to create git tag');
            }
        }

        console.log('\n' + colors.green + '✅ Publish completed successfully!' + colors.reset);
        console.log('\n📦 Published packages:');
        for (const pkg of PACKAGES_BUILD_ORDER) {
            console.log(`  - ${COMPANY_SCOPE}/ai-component-${pkg}@${version}`);
        }
        console.log('\n💡 Install command:');
        console.log(`  npm config set ${COMPANY_SCOPE}:registry ${COMPANY_REGISTRY}`);
        console.log(`  npm install ${COMPANY_SCOPE}/ai-component-ui@${version}`);

    } catch (error) {
        logError('Publish failed: ' + error.message);
        process.exit(1);
    } finally {
        cleanup();
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        logError(error.message);
        process.exit(1);
    });
}

module.exports = { main };