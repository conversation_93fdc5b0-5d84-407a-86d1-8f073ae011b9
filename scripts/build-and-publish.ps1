# AI Component Monorepo Build & Publish Script for Windows PowerShell
# This is a PowerShell wrapper for the Node.js publish runner

param(
    [string[]]$Args = @()
)

Write-Host "AI Component Build & Publish (PowerShell)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Check if Node.js is available
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$RootDir = Split-Path -Parent $ScriptDir
$RunnerScript = Join-Path $ScriptDir "run-publish.js"

# Change to project root directory
Set-Location $RootDir

Write-Host "Running publish script..." -ForegroundColor Blue

# Run the Node.js runner
try {
    if ($Args.Count -gt 0) {
        & node $RunnerScript $Args
    } else {
        & node $RunnerScript
    }
} catch {
    Write-Host "Failed to run publish script: $_" -ForegroundColor Red
    exit 1
}