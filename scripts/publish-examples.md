# 发布脚本使用示例

## 📋 使用前准备

### 1. 设置环境变量

```bash
# 在 ~/.bashrc 或 ~/.zshrc 中添加
export NPM_TOKEN="your-company-npm-token"
export COMPANY_REGISTRY="https://npm.leyaoyao.com"
```

### 2. 验证环境

```bash
# 检查Node.js版本
node --version  # 应该 >= 16.0.0

# 检查pnpm版本
pnpm --version  # 应该 >= 8.0.0

# 检查git状态
git status      # 应该是clean状态
```

## 🚀 基本发布场景

### 场景1：日常bug修复发布

```bash
# 开发完成后，提交代码
git add .
git commit -m "fix: 修复Vue悬浮模式拖拽问题"

# 快速发布patch版本
pnpm run publish:quick

# 输出示例：
# ✅ 发布完成！
# 版本: 1.0.1
# 📦 已发布的packages:
#   - @leyaoyao/ai-component-adapters@1.0.1
#   - @leyaoyao/ai-component-config@1.0.1  
#   - @leyaoyao/ai-component-core@1.0.1
#   - @leyaoyao/ai-component-ui@1.0.1
```

### 场景2：新功能发布

```bash
# 新功能开发完成
git commit -m "feat: 添加文件上传配置功能"

# 发布minor版本
pnpm run publish 1.1.0

# 或者自动生成minor版本
NEW_VERSION=$(node scripts/version-manager.js generate minor)
pnpm run publish $NEW_VERSION
```

### 场景3：预发布测试

```bash
# 先测试发布流程，不会实际发布
pnpm run publish:test

# 输出示例：
# 🧪 Dry Run模式 - 不会实际发布
# ✅ 构建检查通过
# ✅ 所有检查通过，可以安全发布！
```

### 场景4：Beta版本发布

```bash
# 发布beta版本供内部测试
pnpm run publish:beta

# 输出示例：
# 🔬 Beta版本发布...
# 当前版本: 1.0.0
# Beta版本: 1.0.1-beta.0
# ✅ Beta版本发布完成！
```

## 🔧 高级使用场景

### 场景5：版本管理

```bash
# 检查当前版本状态
pnpm run version:check

# 输出示例：
# ✅ All packages have consistent versions
# Current version: 1.0.0

# 查看所有包的版本
node scripts/version-manager.js list

# 输出示例：
# 📦 Package Versions:
#   @leyaoyao/ai-component-adapters: 1.0.0
#   @leyaoyao/ai-component-config: 1.0.0
#   @leyaoyao/ai-component-core: 1.0.0
#   @leyaoyao/ai-component-ui: 1.0.0
```

### 场景6：工作区问题修复

```bash
# 检查工作区问题
pnpm run publish:check

# 输出示例（如果有问题）：
# ❌ Workspace dependency issues found:
#   @leyaoyao/ai-component-ui -> @leyaoyao/ai-component-core: Should use workspace:*
#   Current: ^1.0.0
#   Suggested: workspace:*

# 自动修复问题
pnpm run publish:fix

# 输出示例：
# 🔧 修复工作区问题...
# ✅ Fixed 1 workspace dependencies:
#   @leyaoyao/ai-component-ui -> @leyaoyao/ai-component-core: ^1.0.0 → workspace:*
```

### 场景7：发布失败恢复

```bash
# 如果发布过程中失败，清理并重试
node scripts/publish-helper.js clean
node scripts/publish-helper.js rebuild

# 重新尝试发布
pnpm run publish:quick
```

## 🤖 CI/CD 使用场景

### 场景8：手动触发GitHub Actions

```bash
# 在GitHub仓库的Actions页面手动触发
# workflow_dispatch 参数：
# - version: 1.2.3 (可选，留空自动递增)
# - release_type: patch/minor/major/prerelease
# - dry_run: true/false (测试发布)
# - skip_tests: true/false (跳过测试)
```

### 场景9：自动发布设置

```bash
# 推送到master分支自动触发发布
git push origin master

# GitHub Actions会自动：
# 1. 运行所有检查和测试
# 2. 构建packages
# 3. 发布到npm仓库
# 4. 创建GitHub Release
# 5. 发送通知
```

## 📊 监控和调试

### 场景10：发布监控

```bash
# 查看构建日志
tail -f build.log

# 查看发布日志  
tail -f publish.log

# 检查发布结果
npm view @leyaoyao/ai-component-ui versions --json

# 输出示例：
# [
#   "1.0.0",
#   "1.0.1",
#   "1.1.0"
# ]
```

### 场景11：问题排查

```bash
# 详细调试模式
DEBUG=1 bash scripts/build-and-publish.sh --dry-run

# 检查特定包的状态
cd packages/ui
npm pack --dry-run

# 验证package.json配置
node scripts/pre-publish-check.js
```

## 🔄 回滚场景

### 场景12：版本回滚

```bash
# 如果新版本有问题，可以手动回滚版本
# 注意：npm不支持删除已发布的版本，只能发布新的修复版本

# 方式1：发布修复版本
git revert HEAD  # 回滚代码变更
pnpm run publish:quick  # 发布修复版本

# 方式2：使用hotfix分支
git checkout -b hotfix/1.0.2
# 修复问题
git commit -m "hotfix: 修复关键问题"
pnpm run publish 1.0.2
```

## 💡 最佳实践示例

### 场景13：完整发布流程

```bash
#!/bin/bash
# 标准发布流程脚本

echo "🚀 开始标准发布流程..."

# 1. 检查环境
echo "1. 检查发布环境..."
pnpm run publish:check || exit 1

# 2. 运行测试
echo "2. 运行测试发布..."
pnpm run publish:test || exit 1

# 3. 确认发布
echo "3. 确认要发布吗? (y/N)"
read -r response
if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    echo "4. 执行发布..."
    pnpm run publish:quick
    
    echo "5. 验证发布结果..."
    sleep 5
    npm view @leyaoyao/ai-component-ui version
    
    echo "✅ 发布流程完成！"
else
    echo "❌ 发布已取消"
fi
```

### 场景14：多环境发布

```bash
# 开发环境发布beta版本
if [[ "$NODE_ENV" == "development" ]]; then
    pnpm run publish:beta
# 生产环境发布正式版本  
elif [[ "$NODE_ENV" == "production" ]]; then
    pnpm run publish:quick
else
    echo "未知环境: $NODE_ENV"
    exit 1
fi
```

## 📋 常用命令速查

```bash
# 快速命令
pnpm run publish:quick          # 快速发布
pnpm run publish:test           # 测试发布
pnpm run publish:check          # 检查状态
pnpm run publish:fix            # 修复问题

# 版本管理
pnpm run version:current        # 当前版本
pnpm run version:check          # 版本检查
node scripts/version-manager.js generate minor  # 生成版本

# 高级功能
bash scripts/build-and-publish.sh 1.2.3 --dry-run  # 自定义发布
node scripts/publish-helper.js info                 # 包信息
node scripts/publish-helper.js notes 1.2.3         # 发布说明
```

---

💡 **提示**: 建议先在测试环境熟悉这些命令，然后再在生产环境使用。