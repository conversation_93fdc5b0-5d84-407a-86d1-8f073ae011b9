#!/bin/bash

# PromptX 项目启动脚本

echo "🚀 Starting PromptX project setup..."

# 1. 克隆项目（如果还没有）
if [ ! -d "PromptX" ]; then
  echo "📥 Cloning PromptX repository..."
  git clone https://github.com/Deepractice/PromptX.git
  cd PromptX
else
  echo "📁 Entering PromptX directory..."
  cd PromptX
fi

# 2. 检查是否有 package.json
if [ -f "package.json" ]; then
  echo "📦 Found package.json, installing dependencies..."
  
  # 检查使用哪个包管理器
  if [ -f "pnpm-lock.yaml" ]; then
    echo "Using pnpm..."
    pnpm install
  elif [ -f "yarn.lock" ]; then
    echo "Using yarn..."
    yarn install
  else
    echo "Using npm..."
    npm install
  fi
  
  # 3. 查找启动脚本
  echo "🔍 Looking for start scripts..."
  if grep -q '"dev"' package.json; then
    echo "Starting with dev script..."
    npm run dev
  elif grep -q '"start"' package.json; then
    echo "Starting with start script..."
    npm start
  else
    echo "⚠️  No standard start script found. Check package.json for available scripts."
    npm run
  fi
  
elif [ -f "requirements.txt" ]; then
  echo "🐍 Found Python project..."
  
  # 创建虚拟环境
  if [ ! -d "venv" ]; then
    python3 -m venv venv
  fi
  
  # 激活虚拟环境并安装依赖
  source venv/bin/activate
  pip install -r requirements.txt
  
  # 查找主文件
  if [ -f "main.py" ]; then
    python main.py
  elif [ -f "app.py" ]; then
    python app.py
  else
    echo "⚠️  No standard Python entry point found."
  fi
  
else
  echo "❌ Unable to determine project type. Please check the README.md for instructions."
fi