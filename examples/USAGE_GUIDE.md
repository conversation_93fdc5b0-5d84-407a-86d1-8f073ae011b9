# AI聊天组件使用指南

本指南介绍如何在不同平台中正确使用 `@ai-component` 包中的组件。

## 项目结构

```
packages/
├── core/           # 核心逻辑层
├── config/         # 配置管理
├── adapters/       # 适配器层
└── ui/             # UI组件层
    ├── web/        # React Web组件
    ├── vue/        # Vue组件
    ├── taro/       # Taro跨端组件
    └── index.ts    # 统一导出
```

## 安装依赖

### 基础依赖
```bash
npm install @ai-component/ui @ai-component/core @ai-component/config @ai-component/adapters
```

### 平台特定依赖

#### React Web
```bash
npm install @ant-design/x @ant-design/icons antd react react-dom
```

#### Vue
```bash
npm install @ant-design/x @ant-design/icons-vue ant-design-vue vue
```

#### Taro
```bash
npm install @tarojs/taro @tarojs/components @nutui/nutui-react-taro
```

## 使用方式

### 1. React Web 项目

```tsx
import React from 'react'
import { ReactAIChatComponent } from '@ai-component/ui'
// 或者使用自动检测的组件
// import { AIChatComponent } from '@ai-component/ui'

const App: React.FC = () => {
  const config = {
    showAvatar: true,
    placeholder: '请输入您的问题...',
    maxInputLength: 2000,
  }

  const prompts = [
    {
      key: 'greeting',
      label: '👋 问候',
      content: '你好，很高兴见到你！',
      show: true
    }
  ]

  const handleSend = (message: string, files?: any[]) => {
    console.log('发送消息:', message)
    // 实现发送逻辑
  }

  return (
    <ReactAIChatComponent
      config={config}
      prompts={prompts}
      onSend={handleSend}
      enableFileUpload={true}
      maxFiles={9}
      maxFileSize={3}
    />
  )
}
```

### 2. Vue 项目

#### 基础用法
```vue
<template>
  <VueAIChatComponent
    :config="config"
    :prompts="prompts"
    :enable-file-upload="true"
    :max-files="9"
    :max-file-size="3"
    @send="handleSend"
    @receive="handleReceive"
    @error="handleError"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { VueAIChatComponent } from '@ai-component/ui'
// 或者使用自动检测的组件
// import { AIChatComponent } from '@ai-component/ui'

const config = ref({
  showAvatar: true,
  placeholder: '请输入您的问题...',
  maxInputLength: 2000,
  theme: 'light', // 'light' | 'dark'
  enableAutoScroll: true,
  sessionId: 'my-chat-session'
})

const prompts = ref([
  {
    key: 'greeting',
    label: '👋 问候',
    content: '你好，很高兴见到你！',
    show: true
  }
])

const handleSend = (message: string, files?: any[]) => {
  console.log('发送消息:', message)
  // 实现发送逻辑
}

const handleReceive = (message: string) => {
  console.log('收到消息:', message)
}

const handleError = (error: string) => {
  console.error('聊天错误:', error)
}
</script>
```

#### 悬浮模式用法
```vue
<template>
  <VueAIChatComponent
    :floating-mode="true"
    :floating-config="floatingConfig"
    :chat-dialog-config="dialogConfig"
    :config="config"
    :prompts="prompts"
    :enable-file-upload="true"
    @send="handleSend"
    @floating-toggle="handleFloatingToggle"
    @position-change="handlePositionChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { VueAIChatComponent } from '@ai-component/ui'

const config = ref({
  showAvatar: true,
  theme: 'light',
  enableAutoScroll: true,
  sessionId: 'floating-chat'
})

const floatingConfig = ref({
  enabled: true,
  icon: '💬',
  position: {
    x: 20,
    y: 100,
    side: 'right'
  },
  dragEnabled: true,
  rememberPosition: true
})

const dialogConfig = ref({
  title: '智能助手',
  width: 380,
  height: 500,
  position: 'auto'
})

const handleFloatingToggle = (expanded: boolean) => {
  console.log('悬浮窗口', expanded ? '展开' : '收起')
}

const handlePositionChange = (position: { x: number; y: number; side: 'left' | 'right' }) => {
  console.log('位置变更:', position)
}
</script>
```

### 3. Taro 跨端项目

```tsx
import React from 'react'
import { TaroAIChatComponent } from '@ai-component/ui'
// 或者使用自动检测的组件
// import { AIChatComponent } from '@ai-component/ui'

const App: React.FC = () => {
  const config = {
    showAvatar: true,
    placeholder: '请输入您的问题...',
    maxInputLength: 500, // 小程序建议限制更小
  }

  return (
    <TaroAIChatComponent
      config={config}
      onSend={(message) => console.log(message)}
      style={{ height: '100vh' }}
    />
  )
}
```

## 配置项说明

### ComponentConfig
```typescript
interface ComponentConfig {
  showAvatar?: boolean          // 是否显示头像
  placeholder?: string          // 输入框占位符
  maxInputLength?: number       // 最大输入长度
  enableAutoScroll?: boolean    // 是否自动滚动
  timeout?: number             // 请求超时时间
  style?: Record<string, any>  // 自定义样式
  theme?: 'light' | 'dark'     // 主题模式
  showHistory?: boolean        // 是否显示历史记录
  sessionId?: string           // 会话ID，用于历史记录存储
}
```

### PromptItem 提示词配置
```typescript
interface PromptItem {
  key: string                  // 唯一标识
  label?: string              // 显示标签
  title?: string              // 标题
  content?: string            // 提示词内容
  icon?: VNode | string       // 图标
  show?: boolean              // 是否显示
}
```

### Props
```typescript
interface AIChatComponentProps {
  config?: Partial<ComponentConfig>
  onSend?: (message: string, files?: any[]) => void
  onReceive?: (message: string) => void
  onError?: (error: string) => void
  prompts?: PromptItem[]       // 提示词列表
  enableFileUpload?: boolean   // 是否启用文件上传
  maxFiles?: number           // 最大文件数量
  maxFileSize?: number        // 最大文件大小(MB)
  placeholder?: string        // 占位符文本
  disabled?: boolean          // 是否禁用
  className?: string          // 自定义类名
  style?: React.CSSProperties // 自定义样式
  
  // 悬浮模式相关
  floatingMode?: boolean      // 是否启用悬浮模式
  floatingConfig?: Partial<FloatingConfig> // 悬浮配置
  avatarConfig?: Partial<AvatarConfig>     // 头像配置
  chatDialogConfig?: Partial<ChatDialogConfig> // 对话框配置
  onFloatingToggle?: (expanded: boolean) => void // 悬浮窗口切换事件
  onPositionChange?: (position: { x: number; y: number; side: 'left' | 'right' }) => void // 位置变更事件
  
  // 自定义渲染函数
  renderHeader?: (props: CustomRenderProps) => VNode
  renderEmpty?: (props: CustomRenderProps) => VNode
  renderMessage?: (message: Message, props: CustomRenderProps) => VNode
  renderMessageActions?: (message: Message, props: CustomRenderProps) => VNode
  renderLoading?: (props: CustomRenderProps) => VNode
  renderInputTools?: (props: CustomRenderProps) => VNode
  renderFooter?: (props: CustomRenderProps) => VNode
}
```

### 悬浮模式配置
```typescript
interface FloatingConfig {
  enabled?: boolean
  icon?: VNode | string
  position?: {
    x?: number
    y?: number
    side?: 'left' | 'right'
  }
  dragEnabled?: boolean
  rememberPosition?: boolean
}

interface ChatDialogConfig {
  title?: string
  width?: number
  height?: number
  minWidth?: number
  minHeight?: number
  position?: 'auto' | 'left' | 'right' | 'center'
}

interface AvatarConfig {
  size?: number | 'small' | 'default' | 'large'
  user?: {
    src?: string
    icon?: VNode | string
    size?: number | 'small' | 'default' | 'large'
  }
  assistant?: {
    src?: string
    icon?: VNode | string
    size?: number | 'small' | 'default' | 'large'
  }
  showAvatar?: boolean
}
```

## 示例项目

- **React示例**: `examples/react-demo/` - 展示React Web版本的完整功能，包含悬浮模式、主题切换、场景预设等
- **Vue示例**: `examples/vue-demo/` - 展示Vue版本的完整功能，包含悬浮模式、实时配置、多场景演示
- **包内示例**: `packages/ui/web/example.tsx` - React组件内置示例，基础功能演示
- **包内示例**: `packages/ui/vue/example.vue` - Vue组件内置示例，包含悬浮模式、暗色主题等高级功能

## 运行示例

### React示例
```bash
cd examples/react-demo
npm install
npm run dev
```

### Vue示例
```bash
cd examples/vue-demo
npm install
npm run dev
```

## 特性支持

| 功能 | Web (React) | Vue | Taro |
|------|------------|-----|------|
| 基础聊天 | ✅ | ✅ | ✅ |
| 文件上传 | ✅ | ✅ | ⚠️ |
| 拖拽上传 | ✅ | ✅ | ❌ |
| 提示词 | ✅ | ✅ | ✅ |
| 主题切换 | ✅ | ✅ | ✅ |
| 自动滚动 | ✅ | ✅ | ✅ |
| 悬浮模式 | ✅ | ✅ | ❌ |
| 可拖拽悬浮 | ✅ | ✅ | ❌ |
| 自定义插槽 | ✅ | ✅ | ✅ |
| 历史记录 | ✅ | ✅ | ✅ |
| 头像配置 | ✅ | ✅ | ✅ |

注：
- ✅ 完全支持
- ⚠️ 部分支持（功能受平台限制）
- ❌ 不支持

## 自定义开发

如果需要基于现有组件进行自定义开发，请参考：
- `packages/ui/web/chat-component.tsx` - React组件实现
- `packages/ui/vue/chat-component.vue` - Vue组件实现
- `packages/core/` - 核心逻辑实现

## 平台检测

组件包提供了自动平台检测功能：

```typescript
import { AIChatComponent, detectPlatform } from '@ai-component/ui'

const platform = detectPlatform() // 'web' | 'vue' | 'taro'
console.log('当前平台:', platform)

// AIChatComponent 会自动选择对应平台的组件
```

## 故障排除

### 1. 类型错误
确保安装了对应的类型声明包：
```bash
npm install @types/react @types/react-dom  # React项目
npm install vue-tsc                        # Vue项目
```

### 2. 样式问题
确保导入了必要的CSS文件：
```typescript
import 'ant-design-vue/dist/reset.css'  // Vue项目
import 'antd/dist/reset.css'            // React项目
```

### 3. 依赖冲突
检查项目中的依赖版本是否兼容，建议使用最新版本的框架。

### 4. 悬浮模式问题
悬浮模式需要将组件挂载到 `body` 元素，确保：
- 项目中没有阻止 Teleport 的 CSP 策略
- 页面样式不会影响悬浮元素的定位
- 在 SPA 应用中正确处理路由切换

### 5. Vue 编译错误
如果遇到 `defineProps` 类型错误，确保：
- 使用的是 Vue 3.3+ 版本
- TypeScript 配置正确
- 组件中的类型定义正确导入

### 6. 文件上传问题
确保服务端正确处理文件上传：
- 支持 multipart/form-data 格式
- 设置正确的文件大小限制
- 处理文件类型验证 