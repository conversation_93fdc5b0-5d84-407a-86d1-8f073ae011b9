import { useState } from 'react'
import { 
  ConfigProvider, 
  theme,
  Card, 
  Typography, 
  Space, 
  message, 
  Row, 
  Col, 
  Switch, 
  Select, 
  Divider, 
  Tabs, 
  Slider,
  Radio,
  Tag,
  Alert,
  Input
} from 'antd'
import { 
  BgColorsOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  BulbOutlined,
  ExperimentOutlined,
  ExpandOutlined,
  CloudUploadOutlined
} from '@ant-design/icons'
import { AIChatComponent, Prompt } from '../../../packages/ui/web'
import './App.css'

const { Title, Paragraph, Text } = Typography

// 预设场景配置
const PRESET_SCENARIOS = {
  default: {
    name: '默认配置',
    config: {
    apiEndpoint: '/api/chat',
    placeholder: '请输入您想咨询的问题...',
    showHistory: true,
    showAvatar: true,
    maxInputLength: 2000,
    enableAutoScroll: true,
    theme: 'auto',
    },
    prompts: [
      { key: 'greeting', label: '👋 问候', content: '你好，很高兴见到你！', show: true },
      { key: 'help', label: '❓ 帮助', content: '请问有什么可以帮助您的吗？', show: true },
    ]
  },
  customer_service: {
    name: '客服场景',
    config: {
      apiEndpoint: '/api/customer-service',
      placeholder: '请描述您遇到的问题，我们将为您提供帮助...',
      showHistory: true,
      showAvatar: true,
      maxInputLength: 1000,
      enableAutoScroll: true,
      theme: 'light',
    },
    prompts: [
      { key: 'order', label: '📦 订单问题', content: '我想咨询订单相关问题', show: true },
      { key: 'refund', label: '💰 退款咨询', content: '我需要办理退款', show: true },
      { key: 'product', label: '🛍️ 产品咨询', content: '我想了解产品详情', show: true },
      { key: 'technical', label: '🔧 技术支持', content: '我遇到了技术问题', show: true },
    ]
  },
  programming: {
    name: '编程助手',
    config: {
      apiEndpoint: '/api/coding',
      placeholder: '描述您的编程问题或需求...',
      showHistory: true,
      showAvatar: true,
      maxInputLength: 3000,
      enableAutoScroll: true,
      theme: 'dark',
    },
    prompts: [
      { key: 'debug', label: '🐛 代码调试', content: '帮我分析这段代码的问题', show: true },
      { key: 'optimize', label: '⚡ 性能优化', content: '如何优化这段代码的性能？', show: true },
      { key: 'review', label: '👀 代码审查', content: '请帮我审查这段代码', show: true },
      { key: 'explain', label: '📖 代码解释', content: '请解释这段代码的工作原理', show: true },
    ]
  },
  floating: {
    name: '悬浮客服',
    config: {
      apiEndpoint: '/api/support',
      placeholder: '您好，有什么可以帮您？',
      showHistory: false,
      showAvatar: true,
      maxInputLength: 500,
      enableAutoScroll: true,
      theme: 'light',
    },
    prompts: [
      { key: 'faq', label: '常见问题', content: '查看常见问题列表', show: true },
      { key: 'connect', label: '联系真人', content: '我需要转接人工客服', show: true },
    ]
  }
}

type ScenarioKey = keyof typeof PRESET_SCENARIOS;

function App() {
  const [darkMode, setDarkMode] = useState(false);
  const [floatingMode, setFloatingMode] = useState(false);
  const [activeTab, setActiveTab] = useState('mode');
  
  // 使用一个 state 来管理所有从场景中获取的配置
  const [webDemoConfig, setWebDemoConfig] = useState({
    ...PRESET_SCENARIOS.default.config,
    prompts: PRESET_SCENARIOS.default.prompts.map(p => ({
      key: p.key,
      title: p.label,
      content: p.content,
    })) as Prompt[]
  });
  
  const [activeScenarioKey, setActiveScenarioKey] = useState<ScenarioKey>('default');
  
  const [avatarConfig, setAvatarConfig] = useState({
    showAvatar: true,
    size: 'default' as 'small' | 'default' | 'large',
  });
  const [dialogConfig, setDialogConfig] = useState({
    width: 380,
    height: 600,
  });
  
  // 上传相关状态
  const [uploadMode, setUploadMode] = useState<'none' | 'mock' | 'config'>('mock');
  const [uploadConfig, setUploadConfig] = useState({
    url: '/api/upload',
    method: 'POST' as 'POST' | 'PUT' | 'PATCH',
    headers: {
      'Authorization': 'Bearer demo-token',
    },
    fieldName: 'file',
    timeout: 30000,
    requestType: 'form-data' as 'form-data' | 'json',
    onProgress: (percent: number, file: File) => {
      console.log(`上传进度: ${percent}%, 文件: ${file.name}`);
      message.info(`上传进度: ${percent}%`);
    },
    responseParser: (response: any) => {
      // 模拟响应解析
      if (response.success) {
        return {
          url: response.data.url,
          name: response.data.name,
        };
      }
      throw new Error(response.message || '上传失败');
    },
  });
  
  // 模拟上传函数
  const mockUploadFile = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      // 模拟上传进度
      let progress = 0;
      const timer = setInterval(() => {
        progress += 20;
        if (progress <= 100) {
          message.info(`模拟上传进度: ${progress}%`);
        }
        if (progress >= 100) {
          clearInterval(timer);
          // 模拟返回一个文件URL
          const reader = new FileReader();
          reader.onload = (e) => {
            resolve(e.target?.result as string);
          };
          reader.readAsDataURL(file);
        }
      }, 300);
    });
  };

  const darkThemeConfig = {
    algorithm: theme.darkAlgorithm,
    token: {
      colorPrimary: '#619bf9', // 一个更柔和的蓝色
      colorBgLayout: '#1e1e1e', // 深灰色背景
      colorBgContainer: '#2a2a2a', // 组件容器背景
      colorTextBase: '#e0e0e0', // 柔和的白色文本
      colorBorder: '#424242',
    },
  };

  const lightThemeConfig = {
    algorithm: theme.defaultAlgorithm,
    token: {
      colorPrimary: '#1677ff',
    },
  };

  const handleScenarioChange = (key: ScenarioKey) => {
    const scenario = PRESET_SCENARIOS[key];
    if (scenario) {
      setWebDemoConfig({
        ...scenario.config,
        prompts: scenario.prompts.map(p => ({
          key: p.key,
          title: p.label,
          content: p.content,
        })) as Prompt[],
      });
      setFloatingMode(key === 'floating');
      setActiveScenarioKey(key);
      message.success(`场景已切换: ${scenario.name}`);
    }
  };
  
  const sharedChatProps = {
    avatarConfig,
    chatDialogConfig: dialogConfig,
    placeholder: webDemoConfig.placeholder,
    config: {
      showHistory: webDemoConfig.showHistory,
      sessionId: 'react-demo-session',
      enableAutoScroll: webDemoConfig.enableAutoScroll,
    },
    prompts: webDemoConfig.prompts,
    enableFileUpload: uploadMode !== 'none',
    uploadFile: uploadMode === 'mock' ? mockUploadFile : undefined,
    uploadConfig: uploadMode === 'config' ? uploadConfig : undefined,
  };

  const renderPrompts = () => {
    if (webDemoConfig.prompts.length === 0) return null;
    return (
      <div className="prompts-container">
        {webDemoConfig.prompts.map(p => (
          <Tag key={p.key}>{p.title}</Tag>
        ))}
      </div>
    );
  }

  return (
    <ConfigProvider theme={darkMode ? darkThemeConfig : lightThemeConfig}>
      <div className={`app-container ${darkMode ? 'dark' : ''}`}>
        <header className="app-header">
          <Title level={2}>AI 组件演示</Title>
          <Text>React & Vue 跨框架UI组件</Text>
        </header>
        <main className="app-main">
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={8}>
              <Card
                title={
                  <Space>
                    <SettingOutlined />
                    <Text strong>控制面板</Text>
                  </Space>
                }
                className="control-panel"
              >
                <Tabs 
                  activeKey={activeTab} 
                  onChange={setActiveTab}
                  size="small"
                  items={[
                    {
                      key: 'mode',
                      label: (
                        <span>
                          <PlayCircleOutlined />
                          场景演示
                        </span>
                      ),
                      children: (
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <Text strong>预设场景:</Text>
                          <Select
                            value={activeScenarioKey}
                            style={{ width: '100%', marginTop: 8 }}
                            onChange={handleScenarioChange}
                          >
                            {Object.keys(PRESET_SCENARIOS).map(key => (
                              <Select.Option key={key} value={key}>
                                {PRESET_SCENARIOS[key as ScenarioKey].name}
                              </Select.Option>
                            ))}
                          </Select>
                        </Space>
                      )
                    },
                    {
                      key: 'appearance',
                      label: (
                        <span>
                          <BgColorsOutlined />
                          外观设置
                        </span>
                      ),
                      children: (
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div>
                            <Text strong>暗黑模式:</Text>
                            <Switch checked={darkMode} onChange={setDarkMode} style={{ marginLeft: 8 }} />
                          </div>
                          <Divider>头像设置</Divider>

                          <div>
                            <Text strong>显示头像:</Text>
                            <br />
                            <Switch
                              checked={avatarConfig.showAvatar}
                              onChange={(checked) => setAvatarConfig(prev => ({ ...prev, showAvatar: checked }))}
                              style={{ marginTop: 8 }}
                            />
                          </div>

                          <div>
                            <Text strong>头像大小:</Text>
                            <Radio.Group 
                              value={avatarConfig.size} 
                              onChange={(e) => setAvatarConfig(prev => ({ ...prev, size: e.target.value as 'small' | 'default' | 'large' }))}
                              style={{ marginTop: 8 }}
                              size="small"
                            >
                              <Radio.Button value="small">小</Radio.Button>
                              <Radio.Button value="default">中</Radio.Button>
                              <Radio.Button value="large">大</Radio.Button>
                            </Radio.Group>
                          </div>

                          <Divider>通用设置</Divider>

                          <div>
                            <Text strong>加载历史记录:</Text>
                            <Switch 
                              checked={webDemoConfig.showHistory} 
                              onChange={(checked) => setWebDemoConfig(p => ({ ...p, showHistory: checked }))} 
                              style={{ marginTop: 8 }} 
                            />
                          </div>

                          <div>
                            <Text strong>自动滚动:</Text>
                            <br />
                            <Switch
                              checked={webDemoConfig.enableAutoScroll}
                              onChange={(checked) => {
                                setWebDemoConfig(p => ({...p, enableAutoScroll: checked}));
                                message.success('自动滚动已更新');
                              }}
                              style={{ marginTop: 8 }}
                            />
                          </div>
                        </Space>
                      )
                    },
                    {
                      key: 'features',
                      label: (
                        <span>
                          <BulbOutlined />
                          功能配置
                        </span>
                      ),
                      children: (
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div>
                            <Text strong>最大输入长度:</Text>
                            <Slider
                              min={100}
                              max={5000}
                              value={webDemoConfig.maxInputLength}
                              onChange={(value) => {
                                setWebDemoConfig(p => ({...p, maxInputLength: value}));
                              }}
                              style={{ marginTop: 8 }}
                            />
                            <Text type="secondary">{webDemoConfig.maxInputLength} 字符</Text>
                          </div>

                          <div>
                            <Text strong>占位符文本:</Text>
                            <Select
                              value={webDemoConfig.placeholder}
                              onChange={(value) => {
                                setWebDemoConfig(p => ({...p, placeholder: value}));
                                message.success('占位符文本已更新');
                              }}
                              style={{ width: '100%', marginTop: 8 }}
                            >
                              <Select.Option value="请输入您想咨询的问题...">
                                默认提示
                              </Select.Option>
                              <Select.Option value="有什么可以帮助您的吗？">
                                客服风格
                              </Select.Option>
                              <Select.Option value="描述您的需求或问题...">
                                专业风格
                              </Select.Option>
                              <Select.Option value="开始对话吧！">
                                友好风格
                              </Select.Option>
                            </Select>
                          </div>

                          <div>
                            <Text strong>API 端点:</Text>
                            <Select
                              value={webDemoConfig.apiEndpoint}
                              onChange={(value) => {
                                setWebDemoConfig(p => ({...p, apiEndpoint: value}));
                                message.success('API 端点已更新');
                              }}
                              style={{ width: '100%', marginTop: 8 }}
                            >
                              <Select.Option value="/api/chat">通用聊天</Select.Option>
                              <Select.Option value="/api/customer-service">客服</Select.Option>
                              <Select.Option value="/api/coding">编程助手</Select.Option>
                              <Select.Option value="/api/creative">创意助手</Select.Option>
                            </Select>
                          </div>
                        </Space>
                      )
                    },
                    {
                      key: 'upload',
                      label: (
                        <span>
                          <CloudUploadOutlined />
                          文件上传
                        </span>
                      ),
                      children: (
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div>
                            <Text strong>上传方式:</Text>
                            <Radio.Group 
                              value={uploadMode}
                              onChange={(e) => {
                                setUploadMode(e.target.value);
                                const modeNames = {
                                  none: '禁用上传',
                                  mock: '模拟上传',
                                  config: '配置上传'
                                };
                                message.success(`上传模式已切换为: ${modeNames[e.target.value]}`);
                              }}
                              style={{ marginTop: 8 }}
                              size="small"
                            >
                              <Radio.Button value="none">禁用上传</Radio.Button>
                              <Radio.Button value="mock">模拟上传</Radio.Button>
                              <Radio.Button value="config">配置上传</Radio.Button>
                            </Radio.Group>
                          </div>
                          
                          {uploadMode === 'config' && (
                            <>
                              <Divider orientation="left">上传配置</Divider>
                              <div>
                                <Text strong>上传地址:</Text>
                                <Input 
                                  value={uploadConfig.url}
                                  onChange={(e) => setUploadConfig(prev => ({ ...prev, url: e.target.value }))}
                                  placeholder="请输入上传API地址"
                                  style={{ marginTop: 8 }}
                                />
                              </div>
                              <div>
                                <Text strong>HTTP方法:</Text>
                                <Select 
                                  value={uploadConfig.method}
                                  onChange={(value) => setUploadConfig(prev => ({ ...prev, method: value }))}
                                  style={{ width: '100%', marginTop: 8 }}
                                >
                                  <Select.Option value="POST">POST</Select.Option>
                                  <Select.Option value="PUT">PUT</Select.Option>
                                  <Select.Option value="PATCH">PATCH</Select.Option>
                                </Select>
                              </div>
                              <div>
                                <Text strong>请求类型:</Text>
                                <Radio.Group 
                                  value={uploadConfig.requestType}
                                  onChange={(e) => setUploadConfig(prev => ({ ...prev, requestType: e.target.value }))}
                                  style={{ marginTop: 8 }}
                                  size="small"
                                >
                                  <Radio.Button value="form-data">FormData</Radio.Button>
                                  <Radio.Button value="json">JSON</Radio.Button>
                                </Radio.Group>
                              </div>
                              <div>
                                <Text strong>文件字段名:</Text>
                                <Input 
                                  value={uploadConfig.fieldName}
                                  onChange={(e) => setUploadConfig(prev => ({ ...prev, fieldName: e.target.value }))}
                                  placeholder="默认为 'file'"
                                  style={{ marginTop: 8 }}
                                />
                              </div>
                              <div>
                                <Text strong>超时时间:</Text>
                                <Slider 
                                  min={5}
                                  max={120}
                                  step={5}
                                  value={uploadConfig.timeout / 1000}
                                  onChange={(value) => setUploadConfig(prev => ({ ...prev, timeout: value * 1000 }))}
                                  style={{ marginTop: 8 }}
                                />
                                <Text type="secondary">{uploadConfig.timeout / 1000} 秒</Text>
                              </div>
                            </>
                          )}
                          
                          {uploadMode === 'config' ? (
                            <Alert 
                              message="配置上传模式"
                              description="使用配置对象定义上传参数，支持进度跟踪、错误处理等高级功能。"
                              type="info"
                              showIcon
                            />
                          ) : uploadMode === 'mock' ? (
                            <Alert 
                              message="模拟上传模式"
                              description="使用自定义函数模拟文件上传，仅用于演示。"
                              type="warning"
                              showIcon
                            />
                          ) : (
                            <Alert 
                              message="上传功能已禁用"
                              description="文件上传功能将不可用。"
                              type="error"
                              showIcon
                            />
                          )}
                        </Space>
                      )
                    },
                    {
                      key: 'floating',
                      label: (
                        <span>
                          <ExpandOutlined />
                          悬浮模式
                        </span>
                      ),
                      children: (
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div>
                            <Text strong>启用悬浮模式:</Text>
                            <br />
                            <Switch
                              checked={floatingMode}
                              onChange={setFloatingMode}
                              style={{ marginTop: 8 }}
                            />
                          </div>
                          <Alert message="启用后，聊天窗口将以可拖拽的悬浮球形式存在。" type="info" showIcon />
                          <div>
                            <Text strong>对话框宽度:</Text>
                            <Slider
                              min={300}
                              max={800}
                              value={dialogConfig.width}
                              onChange={(value) => setDialogConfig(prev => ({ ...prev, width: value }))}
                              style={{ marginTop: 8 }}
                              disabled={!floatingMode}
                            />
                             <Text type="secondary">{dialogConfig.width} px</Text>
                          </div>
                           <div>
                            <Text strong>对话框高度:</Text>
                            <Slider
                              min={400}
                              max={1000}
                              value={dialogConfig.height}
                              onChange={(value) => setDialogConfig(prev => ({ ...prev, height: value }))}
                              style={{ marginTop: 8 }}
                              disabled={!floatingMode}
                            />
                            <Text type="secondary">{dialogConfig.height} px</Text>
                          </div>
                        </Space>
                      )
                    }
                  ]}
                />

                <Divider />
                
                <Alert
                  message="实时预览"
                  description="修改配置后，右侧组件会实时更新"
                  type="info"
                  showIcon
                  style={{ marginTop: 16 }}
                />
              </Card>
            </Col>

            {/* 右侧演示区域 */}
            <Col xs={24} lg={16}>
              <div className="demo-area">
                {!floatingMode && (
                  <AIChatComponent 
                    {...sharedChatProps}
                    // 非悬浮模式下，不传递dialog config，让其自适应
                    chatDialogConfig={{}}
                    floatingMode={false} 
                    renderSenderHeader={renderPrompts}
                  />
                )}
                {floatingMode && (
                  <div className="floating-mode-placeholder">
                    <p>悬浮窗模式已激活</p>
                    <span>请在右下角查找悬浮按钮</span>
                  </div>
                )}
              </div>
            </Col>
          </Row>

          {/* 底部功能特性展示 */}
          <div className="features-showcase">
            <Title level={3}>
              <ExperimentOutlined style={{ marginRight: 8 }} />
              功能特性展示
            </Title>
            <Row gutter={[16, 16]}>
              <Col xs={12} md={6}>
                <Card size="small" className="feature-card">
                  <div className="feature-icon">🎯</div>
                  <Title level={5}>多场景适配</Title>
                <Paragraph>
                    客服、编程、创意等多种预设场景
                </Paragraph>
                </Card>
                </Col>
                
                <Col xs={12} md={6}>
                  <Card size="small" className="feature-card">
                    <div className="feature-icon">🔧</div>
                    <Title level={5}>实时配置</Title>
                  <Paragraph>
                      类似 Storybook 的交互式配置面板
                  </Paragraph>
                  </Card>
                  </Col>
                  
                  <Col xs={12} md={6}>
                    <Card size="small" className="feature-card">
                      <div className="feature-icon">🎨</div>
                      <Title level={5}>主题切换</Title>
                    <Paragraph>
                        支持亮色、暗色主题动态切换
                    </Paragraph>
                    </Card>
                    </Col>
                    
                    <Col xs={12} md={6}>
                      <Card size="small" className="feature-card">
                        <div className="feature-icon">📊</div>
                        <Title level={5}>使用统计</Title>
                      <Paragraph>
                          实时显示消息数量和错误统计
                      </Paragraph>
                    </Card>
                    </Col>
                  </Row>
                </div>

                <div className="footer">
                  <Paragraph type="secondary">
                    AI组件 v1.0.0 - 跨平台AI聊天组件解决方案
                  </Paragraph>
                </div>
              </main>
              {floatingMode && (
                <AIChatComponent 
                  {...sharedChatProps} 
                  // 悬浮模式下，传递dialog config
                  floatingMode={true} 
                  renderSenderHeader={renderPrompts}
                />
              )}
            </div>
          </ConfigProvider>
        )
      }

export default App 