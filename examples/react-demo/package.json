{"name": "@ai-component/react-demo", "version": "1.0.0", "private": true, "description": "React AI Chat Component Demo", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.2.0", "@ant-design/x": "^1.0.0", "@leyaoyao/ai-component-adapters": "workspace:*", "@leyaoyao/ai-component-config": "workspace:*", "@leyaoyao/ai-component-core": "workspace:*", "@leyaoyao/ai-component-ui": "workspace:*", "antd": "^5.12.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^6.3.5"}}