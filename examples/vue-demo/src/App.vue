<template>
  <ConfigProvider :theme="darkMode ? darkThemeConfig : lightThemeConfig">
    <div :class="['app-container', { dark: darkMode }]">
      <header class="app-header">
        <Title :level="2">AI 组件演示</Title>
        <Text>React & Vue 跨框架UI组件</Text>
      </header>
      
      <main class="app-main">
        <a-row :gutter="[24, 24]">
          <a-col :xs="24" :lg="8">
            <a-card
              class="control-panel"
              :body-style="{ padding: '20px' }"
            >
              <template #title>
                <a-space>
                  <SettingOutlined />
                  <Text strong>控制面板</Text>
                </a-space>
              </template>
              
              <a-tabs 
                v-model:activeKey="activeTab" 
                size="small"
              >
                <a-tab-pane key="mode" tab="场景演示">
                  <a-space direction="vertical" style="width: 100%">
                    <Text strong>预设场景:</Text>
                    <a-select 
                      v-model:value="activeScenarioKey"
                      style="width: 100%; margin-top: 8px"
                      @change="handleScenarioChange"
                    >
                      <a-select-option 
                        v-for="(scenario, key) in PRESET_SCENARIOS" 
                        :key="key" 
                        :value="key"
                      >
                        {{ scenario.name }}
                      </a-select-option>
                    </a-select>
                  </a-space>
                </a-tab-pane>
                
                <a-tab-pane key="appearance" tab="外观设置">
                  <a-space direction="vertical" style="width: 100%">
                    <div>
                      <Text strong>暗黑模式:</Text>
                      <a-switch 
                        v-model:checked="darkMode" 
                        style="margin-left: 8px"
                      />
                    </div>
                    <a-divider>头像设置</a-divider>
                    <div>
                      <Text strong>显示头像:</Text>
                      <br>
                      <a-switch 
                        v-model:checked="avatarConfig.showAvatar"
                        style="margin-top: 8px"
                      />
                    </div>
                    <div>
                      <Text strong>头像大小:</Text>
                      <a-radio-group 
                        v-model:value="avatarConfig.size"
                        style="margin-top: 8px"
                        size="small"
                      >
                        <a-radio-button value="small">小</a-radio-button>
                        <a-radio-button value="default">中</a-radio-button>
                        <a-radio-button value="large">大</a-radio-button>
                      </a-radio-group>
                    </div>
                    <a-divider>通用设置</a-divider>
                    <div>
                      <Text strong>加载历史记录:</Text>
                      <a-switch 
                        v-model:checked="vueDemoConfig.showHistory"
                        style="margin-top: 8px"
                      />
                    </div>
                    <div>
                      <Text strong>自动滚动:</Text>
                      <br>
                      <a-switch 
                        v-model:checked="vueDemoConfig.enableAutoScroll"
                        @change="() => message.success('自动滚动已更新')"
                        style="margin-top: 8px"
                      />
                    </div>
                  </a-space>
                </a-tab-pane>
                
                <a-tab-pane key="features" tab="功能配置">
                  <a-space direction="vertical" style="width: 100%">
                    <div>
                      <Text strong>最大输入长度:</Text>
                      <a-slider 
                        v-model:value="vueDemoConfig.maxInputLength"
                        :min="100"
                        :max="5000"
                        style="margin-top: 8px"
                      />
                      <Text type="secondary">{{ vueDemoConfig.maxInputLength }} 字符</Text>
                    </div>
                    <div>
                      <Text strong>占位符文本:</Text>
                      <a-select 
                        v-model:value="vueDemoConfig.placeholder"
                        @change="handlePlaceholderChange"
                        style="width: 100%; margin-top: 8px"
                      >
                        <a-select-option value="请输入您想咨询的问题...">默认提示</a-select-option>
                        <a-select-option value="有什么可以帮助您的吗？">客服风格</a-select-option>
                        <a-select-option value="描述您的需求或问题...">专业风格</a-select-option>
                        <a-select-option value="开始对话吧！">友好风格</a-select-option>
                      </a-select>
                    </div>
                    <div>
                      <Text strong>API 端点:</Text>
                      <a-select 
                        v-model:value="vueDemoConfig.apiEndpoint"
                        @change="handleApiEndpointChange"
                        style="width: 100%; margin-top: 8px"
                      >
                        <a-select-option value="/api/chat">通用聊天</a-select-option>
                        <a-select-option value="/api/customer-service">客服</a-select-option>
                        <a-select-option value="/api/coding">编程助手</a-select-option>
                        <a-select-option value="/api/creative">创意助手</a-select-option>
                      </a-select>
                    </div>
                  </a-space>
                </a-tab-pane>
                
                <a-tab-pane key="upload" tab="文件上传">
                  <a-space direction="vertical" style="width: 100%">
                    <div>
                      <Text strong>上传方式:</Text>
                      <a-radio-group 
                        v-model:value="uploadMode"
                        @change="handleUploadModeChange"
                        style="margin-top: 8px"
                        size="small"
                      >
                        <a-radio-button value="none">禁用上传</a-radio-button>
                        <a-radio-button value="mock">模拟上传</a-radio-button>
                        <a-radio-button value="config">配置上传</a-radio-button>
                      </a-radio-group>
                    </div>
                    
                    <template v-if="uploadMode === 'config'">
                      <a-divider orientation="left">上传配置</a-divider>
                      <div>
                        <Text strong>上传地址:</Text>
                        <a-input 
                          v-model:value="uploadConfig.url"
                          placeholder="请输入上传API地址"
                          style="margin-top: 8px"
                        />
                      </div>
                      <div>
                        <Text strong>HTTP方法:</Text>
                        <a-select 
                          v-model:value="uploadConfig.method"
                          style="width: 100%; margin-top: 8px"
                        >
                          <a-select-option value="POST">POST</a-select-option>
                          <a-select-option value="PUT">PUT</a-select-option>
                          <a-select-option value="PATCH">PATCH</a-select-option>
                        </a-select>
                      </div>
                      <div>
                        <Text strong>请求类型:</Text>
                        <a-radio-group 
                          v-model:value="uploadConfig.requestType"
                          style="margin-top: 8px"
                          size="small"
                        >
                          <a-radio-button value="form-data">FormData</a-radio-button>
                          <a-radio-button value="json">JSON</a-radio-button>
                        </a-radio-group>
                      </div>
                      <div>
                        <Text strong>文件字段名:</Text>
                        <a-input 
                          v-model:value="uploadConfig.fieldName"
                          placeholder="默认为 'file'"
                          style="margin-top: 8px"
                        />
                      </div>
                      <div>
                        <Text strong>超时时间:</Text>
                        <a-slider 
                          v-model:value="uploadConfigTimeout"
                          :min="5"
                          :max="120"
                          :step="5"
                          style="margin-top: 8px"
                        />
                        <Text type="secondary">{{ uploadConfigTimeout }} 秒</Text>
                      </div>
                    </template>
                    
                    <a-alert 
                      v-if="uploadMode === 'config'"
                      message="配置上传模式"
                      description="使用配置对象定义上传参数，支持进度跟踪、错误处理等高级功能。"
                      type="info"
                      show-icon
                    />
                    <a-alert 
                      v-else-if="uploadMode === 'mock'"
                      message="模拟上传模式"
                      description="使用自定义函数模拟文件上传，仅用于演示。"
                      type="warning"
                      show-icon
                    />
                    <a-alert 
                      v-else
                      message="上传功能已禁用"
                      description="文件上传功能将不可用。"
                      type="error"
                      show-icon
                    />
                  </a-space>
                </a-tab-pane>
                
                <a-tab-pane key="floating" tab="悬浮模式">
                  <a-space direction="vertical" style="width: 100%">
                    <div>
                      <Text strong>启用悬浮模式:</Text>
                      <br>
                      <a-switch 
                        v-model:checked="floatingMode"
                        style="margin-top: 8px"
                      />
                    </div>
                    <a-alert 
                      message="启用后，聊天窗口将以可拖拽的悬浮球形式存在。"
                      type="info"
                      show-icon
                    />
                    <div>
                      <Text strong>对话框宽度:</Text>
                      <a-slider 
                        v-model:value="dialogConfig.width"
                        :min="300"
                        :max="800"
                        :disabled="!floatingMode"
                        style="margin-top: 8px"
                      />
                      <Text type="secondary">{{ dialogConfig.width }} px</Text>
                    </div>
                    <div>
                      <Text strong>对话框高度:</Text>
                      <a-slider 
                        v-model:value="dialogConfig.height"
                        :min="400"
                        :max="1000"
                        :disabled="!floatingMode"
                        style="margin-top: 8px"
                      />
                      <Text type="secondary">{{ dialogConfig.height }} px</Text>
                    </div>
                  </a-space>
                </a-tab-pane>
              </a-tabs>
              
              <a-divider />
              
              <a-alert 
                message="实时预览" 
                :description="`当前场景: ${PRESET_SCENARIOS[activeScenarioKey].name}`"
                type="info" 
                show-icon 
                style="margin-top: 16px;"
              />
            </a-card>
          </a-col>
          
          <a-col :xs="24" :lg="16">
            <a-card 
              class="demo-container"
              :body-style="{ padding: 0, height: '700px' }"
            >
              <template #title>
                <a-space>
                  <ExperimentOutlined />
                  <Text strong>聊天组件演示</Text>
                  <a-badge 
                    :count="PRESET_SCENARIOS[activeScenarioKey].prompts?.length || 0" 
                    :number-style="{ backgroundColor: '#52c41a' }"
                  >
                    <a-tag color="blue">提示词</a-tag>
                  </a-badge>
                </a-space>
              </template>
              
              <div class="demo-area">
                <div v-if="floatingMode" class="floating-mode-placeholder">
                  <p>🎈 悬浮模式已启用</p>
                  <Text type="secondary">聊天窗口将显示为可拖拽的悬浮球</Text>
                </div>
                
                <AIChatComponent
                  v-show="!floatingMode"
                  :config="{
                    showHistory: vueDemoConfig.showHistory,
                    sessionId: 'vue-demo-session',
                    enableAutoScroll: vueDemoConfig.enableAutoScroll,
                    maxInputLength: vueDemoConfig.maxInputLength,
                    theme: darkMode ? 'dark' : 'light'
                  }"
                  :avatar-config="avatarConfig"
                  :prompts="vueDemoConfig.prompts"
                  :placeholder="vueDemoConfig.placeholder"
                  :enable-file-upload="uploadMode !== 'none'"
                  :upload-file="uploadMode === 'mock' ? mockUploadFile : undefined"
                  :upload-config="uploadMode === 'config' ? { ...uploadConfig, timeout: uploadConfigTimeout * 1000 } : undefined"
                  :max-files="9"
                  :max-file-size="10"
                  :disabled="false"
                  @send="handleSend"
                  @receive="handleReceive"
                  @error="handleError"
                  @clear="() => message.success('对话已清空')"
                  style="height: 100%; border: none;"
                />
                
                <AIChatComponent
                  v-if="floatingMode"
                  :floating-mode="true"
                  :floating-config="{
                    dragEnabled: true,
                    rememberPosition: true,
                    icon: '💬',
                    position: { x: 20, y: 100, side: 'right' }
                  }"
                  :chat-dialog-config="dialogConfig"
                  :config="{
                    showHistory: vueDemoConfig.showHistory,
                    sessionId: 'vue-demo-floating-session',
                    enableAutoScroll: vueDemoConfig.enableAutoScroll,
                    maxInputLength: vueDemoConfig.maxInputLength,
                    theme: darkMode ? 'dark' : 'light'
                  }"
                  :avatar-config="avatarConfig"
                  :prompts="vueDemoConfig.prompts"
                  :placeholder="vueDemoConfig.placeholder"
                  :enable-file-upload="uploadMode !== 'none'"
                  :upload-file="uploadMode === 'mock' ? mockUploadFile : undefined"
                  :upload-config="uploadMode === 'config' ? { ...uploadConfig, timeout: uploadConfigTimeout * 1000 } : undefined"
                  :max-files="9"
                  :max-file-size="10"
                  :disabled="false"
                  @send="handleSend"
                  @receive="handleReceive"
                  @error="handleError"
                  @clear="() => message.success('对话已清空')"
                  @floating-toggle="handleFloatingToggle"
                  @position-change="handlePositionChange"
                />
              </div>
            </a-card>
          </a-col>
        </a-row>
        
        <div class="features-showcase">
          <Title :level="3">✨ 核心特性展示</Title>
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" :lg="6" v-for="feature in features" :key="feature.key">
              <a-card size="small" class="feature-card" hoverable>
                <div class="feature-icon">{{ feature.icon }}</div>
                <Title :level="5">{{ feature.title }}</Title>
                <Paragraph>{{ feature.description }}</Paragraph>
              </a-card>
            </a-col>
          </a-row>
        </div>
        
        <footer class="footer">
          <Text type="secondary">
            💡 提示: 尝试不同的场景配置，体验各种功能特性
          </Text>
        </footer>
      </main>
    </div>
  </ConfigProvider>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, h } from 'vue'
import { 
  ConfigProvider, 
  theme,
  Card as ACard, 
  Typography, 
  Space as ASpace, 
  message, 
  Row as ARow, 
  Col as ACol,
  Switch as ASwitch, 
  Select as ASelect, 
  Divider as ADivider, 
  Tabs as ATabs, 
  TabPane as ATabPane,
  Slider as ASlider,
  Radio as ARadio,
  RadioGroup as ARadioGroup,
  RadioButton as ARadioButton,
  Tag as ATag,
  Alert as AAlert,
  Badge as ABadge,
  Input as AInput
} from 'ant-design-vue'
import { 
  SettingOutlined,
  ExperimentOutlined,
} from '@ant-design/icons-vue'
import { VueAIChatComponent as AIChatComponent } from '../../../packages/ui/vue'

const { Title, Paragraph, Text } = Typography

// 预设场景配置 - 与React版本完全一致
const PRESET_SCENARIOS = {
  default: {
    name: '默认配置',
    config: {
      apiEndpoint: '/api/chat',
      placeholder: '请输入您想咨询的问题...',
      showHistory: true,
      showAvatar: true,
      maxInputLength: 2000,
      enableAutoScroll: true,
      theme: 'auto',
    },
    prompts: [
      { key: 'greeting', title: '👋 问候', content: '你好，很高兴见到你！' },
      { key: 'help', title: '❓ 帮助', content: '请问有什么可以帮助您的吗？' },
    ]
  },
  customer_service: {
    name: '客服场景',
    config: {
      apiEndpoint: '/api/customer-service',
      placeholder: '请描述您遇到的问题，我们将为您提供帮助...',
      showHistory: true,
      showAvatar: true,
      maxInputLength: 1000,
      enableAutoScroll: true,
      theme: 'light',
    },
    prompts: [
      { key: 'order', title: '📦 订单问题', content: '我想咨询订单相关问题' },
      { key: 'refund', title: '💰 退款咨询', content: '我需要办理退款' },
      { key: 'product', title: '🛍️ 产品咨询', content: '我想了解产品详情' },
      { key: 'technical', title: '🔧 技术支持', content: '我遇到了技术问题' },
    ]
  },
  programming: {
    name: '编程助手',
    config: {
      apiEndpoint: '/api/coding',
      placeholder: '描述您的编程问题或需求...',
      showHistory: true,
      showAvatar: true,
      maxInputLength: 3000,
      enableAutoScroll: true,
      theme: 'dark',
    },
    prompts: [
      { key: 'debug', title: '🐛 代码调试', content: '帮我分析这段代码的问题' },
      { key: 'optimize', title: '⚡ 性能优化', content: '如何优化这段代码的性能？' },
      { key: 'review', title: '👀 代码审查', content: '请帮我审查这段代码' },
      { key: 'explain', title: '📖 代码解释', content: '请解释这段代码的工作原理' },
    ]
  },
  floating: {
    name: '悬浮客服',
    config: {
      apiEndpoint: '/api/support',
      placeholder: '您好，有什么可以帮您？',
      showHistory: false,
      showAvatar: true,
      maxInputLength: 500,
      enableAutoScroll: true,
      theme: 'light',
    },
    prompts: [
      { key: 'faq', title: '常见问题', content: '查看常见问题列表' },
      { key: 'connect', title: '联系真人', content: '我需要转接人工客服' },
    ]
  }
}

type ScenarioKey = keyof typeof PRESET_SCENARIOS

// 响应式状态
const darkMode = ref(false)
const floatingMode = ref(false)
const activeTab = ref('mode')
const activeScenarioKey = ref<ScenarioKey>('default')

// 使用一个 state 来管理所有从场景中获取的配置
const vueDemoConfig = reactive({
  ...PRESET_SCENARIOS.default.config,
  prompts: PRESET_SCENARIOS.default.prompts
})

const avatarConfig = reactive({
  showAvatar: true,
  size: 'default' as 'small' | 'default' | 'large',
})

const dialogConfig = reactive({
  width: 380,
  height: 600,
})

// 上传相关状态
const uploadMode = ref<'none' | 'mock' | 'config'>('mock')
const uploadConfigTimeout = ref(30)
const uploadConfig = reactive({
  url: '/api/upload',
  method: 'POST' as 'POST' | 'PUT' | 'PATCH',
  headers: {
    'Authorization': 'Bearer demo-token',
  },
  fieldName: 'file',
  requestType: 'form-data' as 'form-data' | 'json',
  onProgress: (percent: number, file: File) => {
    console.log(`上传进度: ${percent}%, 文件: ${file.name}`)
    message.info(`上传进度: ${percent}%`)
  },
  responseParser: (response: any) => {
    if (response.success) {
      return {
        url: response.data.url,
        name: response.data.name,
      }
    }
    throw new Error(response.message || '上传失败')
  },
})

// 模拟上传函数
const mockUploadFile = async (file: File): Promise<string> => {
  return new Promise((resolve) => {
    let progress = 0
    const timer = setInterval(() => {
      progress += 20
      if (progress <= 100) {
        message.info(`模拟上传进度: ${progress}%`)
      }
      if (progress >= 100) {
        clearInterval(timer)
        const reader = new FileReader()
        reader.onload = (e) => {
          resolve(e.target?.result as string)
        }
        reader.readAsDataURL(file)
      }
    }, 300)
  })
}

// 主题配置
const darkThemeConfig = {
  algorithm: theme.darkAlgorithm,
  token: {
    colorPrimary: '#619bf9',
    colorBgLayout: '#1e1e1e',
    colorBgContainer: '#2a2a2a',
    colorTextBase: '#e0e0e0',
    colorBorder: '#424242',
  },
}

const lightThemeConfig = {
  algorithm: theme.defaultAlgorithm,
  token: {
    colorPrimary: '#1677ff',
  },
}

// 功能特性数据
const features = [
  { key: 'cross-platform', icon: '🚀', title: '跨平台支持', description: 'React、Vue、Taro三端统一' },
  { key: 'floating', icon: '🎈', title: '悬浮模式', description: '可拖拽悬浮球，智能边缘吸附' },
  { key: 'custom-slots', icon: '🔧', title: '自定义插槽', description: '7个插槽位置，高度可定制' },
  { key: 'file-upload', icon: '📎', title: '文件上传', description: '支持图片、文档、Office文件上传，拖拽操作' },
  { key: 'themes', icon: '🎨', title: '主题切换', description: '内置明暗主题，自定义样式' },
  { key: 'history', icon: '💾', title: '历史记录', description: '本地存储，会话恢复' },
  { key: 'prompts', icon: '💬', title: '快捷提示', description: '预设提示词，快速输入' },
  { key: 'typescript', icon: '🛡️', title: 'TypeScript', description: '完整类型支持，开发友好' },
]


// 监听暗黑模式变化
watch(darkMode, (newValue) => {
  message.info(`主题已切换为${newValue ? '暗黑' : '明亮'}模式`)
})

// 监听历史记录配置变化
watch(() => vueDemoConfig.showHistory, (newValue) => {
  message.info(`历史记录功能已${newValue ? '启用' : '禁用'}`)
})

// 监听悬浮模式变化
watch(floatingMode, (newValue) => {
  message.info(`悬浮模式已${newValue ? '启用' : '禁用'}`)
})

// 场景切换处理
const handleScenarioChange = (key: ScenarioKey) => {
  const scenario = PRESET_SCENARIOS[key]
  if (scenario) {
    Object.assign(vueDemoConfig, scenario.config)
    vueDemoConfig.prompts = scenario.prompts
    floatingMode.value = key === 'floating'
    activeScenarioKey.value = key
    message.success(`场景已切换: ${scenario.name}`)
  }
}

// 事件处理
const handleSend = (messageText: string, files?: any[]) => {
  const fileInfo = files && files.length > 0 
    ? ` (包含${files.length}个文件: ${files.map(f => f.name).join(', ')})` 
    : ''
  console.log(`发送消息: ${messageText}${fileInfo}`)
  
  if (files && files.length > 0) {
    const fileTypes = files.map(f => f.type || '未知类型').join(', ')
    message.success(`消息已发送，包含文件类型: ${fileTypes}`)
  } else {
    message.success('消息已发送')
  }
}

const handleReceive = (messageText: string) => {
  console.log('收到消息:', messageText)
}

const handleError = (error: string) => {
  console.error('聊天错误:', error)
  message.error(`聊天出错: ${error}`)
}

const handleFloatingToggle = (expanded: boolean) => {
  console.log('悬浮切换:', expanded)
  message.info(`悬浮窗口${expanded ? '展开' : '收起'}`)
}

const handlePositionChange = (pos: { x: number; y: number; side: 'left' | 'right' }) => {
  console.log('位置变化:', pos)
  message.info(`悬浮窗口移动到: ${pos.side}侧 (${pos.x}, ${pos.y})`)
}

// 添加更多高级功能
const handleApiEndpointChange = (endpoint: string) => {
  console.log('API端点已切换:', endpoint)
  message.success(`API端点已更新: ${endpoint}`)
}

const handlePlaceholderChange = (placeholder: string) => {
  console.log('占位符已更新:', placeholder)
  message.success('占位符文本已更新')
}

const handleMaxInputLengthChange = (length: number) => {
  console.log('最大输入长度已更新:', length)
  message.info(`最大输入长度已设置为: ${length} 字符`)
}

// 上传模式切换处理
const handleUploadModeChange = (e: any) => {
  const mode = e.target.value
  const modeNames = {
    none: '禁用上传',
    mock: '模拟上传',
    config: '配置上传'
  }
  message.success(`上传模式已切换为: ${modeNames[mode as keyof typeof modeNames]}`)
}

</script>

<style scoped>
/* 导入与React版本一致的样式 */
.app-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: background-color 0.3s, color 0.3s;
}

.app-header {
  text-align: center;
  margin-bottom: 30px;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.app-header h2 {
  color: #1890ff;
  margin-bottom: 16px;
}

.control-panel {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  height: fit-content;
  position: sticky;
  top: 20px;
}

.control-panel :deep(.ant-card-head) {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  border-radius: 12px 12px 0 0;
}

.control-panel :deep(.ant-card-head-title) {
  color: white;
}

.demo-container {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  overflow: hidden;
}

.demo-container :deep(.ant-card-head) {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.demo-container :deep(.ant-card-head-title) {
  color: white;
}

.demo-area {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.demo-area .ai-chat-component-wrapper {
  width: 100%;
}

.floating-mode-placeholder {
  text-align: center;
  color: #8c8c8c;
}

.floating-mode-placeholder p {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.features-showcase {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin: 30px 0;
}

.features-showcase h3 {
  color: #1890ff;
  text-align: center;
  margin-bottom: 24px;
}

.feature-card {
  text-align: center;
  border-radius: 8px;
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid #f0f0f0;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #1890ff;
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.feature-card h5 {
  color: #1890ff;
  margin-bottom: 8px;
}

.footer {
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 12px;
}

/* 深色主题适配 */
.app-container.dark {
  background-color: #141414;
  color: #f0f0f0;
}

.app-container.dark :deep(.ant-card),
.app-container.dark :deep(.ant-tabs-nav),
.app-container.dark :deep(.ant-tabs-tab),
.app-container.dark :deep(.ant-tabs-tab-active),
.app-container.dark :deep(.ant-radio-button-wrapper) {
  background: #1f1f1f !important;
  color: #f0f0f0 !important;
  border-color: #424242 !important;
}

.app-container.dark :deep(.ant-typography),
.app-container.dark :deep(.ant-radio-button-wrapper:not(.ant-radio-button-wrapper-checked)) {
  color: #f0f0f0 !important;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.control-panel,
.demo-container,
.features-showcase,
.footer {
  animation: fadeIn 0.6s ease-out;
}

.feature-card {
  animation: slideInRight 0.4s ease-out;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .control-panel {
    position: static;
    margin-bottom: 24px;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .app-header {
    padding: 20px;
  }
  
  .features-showcase {
    padding: 20px;
  }
}
</style>