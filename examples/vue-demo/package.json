{"name": "@ai-component/vue-demo", "version": "1.0.0", "private": true, "description": "Vue AI Chat Component Demo", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "serve": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.0", "@leyaoyao/ai-component-adapters": "workspace:*", "@leyaoyao/ai-component-config": "workspace:*", "@leyaoyao/ai-component-core": "workspace:*", "@leyaoyao/ai-component-ui": "workspace:*", "ant-design-vue": "^4.0.0", "vue": "^3.4.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "typescript": "^5.0.0", "vite": "^6.3.5", "vue-tsc": "^2.0.0"}}