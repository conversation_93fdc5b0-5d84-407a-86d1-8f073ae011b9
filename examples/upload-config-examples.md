# 文件上传配置使用示例

本文档展示了如何在三个平台（React/Web、Vue、Taro）中使用新的文件上传配置功能。

## 功能概述

现在支持两种文件上传方式：
1. **自定义上传函数** (`uploadFile`): 完全自定义的上传逻辑
2. **上传配置** (`uploadConfig`): 通过配置对象快速设置上传参数

## 类型定义

```typescript
interface UploadConfig {
  url: string;                              // 上传地址
  method?: 'POST' | 'PUT' | 'PATCH';       // 请求方法，默认 POST
  headers?: Record<string, string>;         // 自定义请求头
  withCredentials?: boolean;                // 是否携带凭证
  data?: Record<string, any>;              // 额外的表单数据或JSON数据
  fieldName?: string;                       // 文件字段名，默认 'file'
  timeout?: number;                         // 超时时间（毫秒）
  responseParser?: (response: any) => {     // 响应解析函数
    url: string;
    name?: string;
    [key: string]: any;
  };
  onProgress?: (percent: number, file: File) => void;   // 上传进度回调
  validateStatus?: (status: number) => boolean; // 验证响应状态
  requestType?: 'form-data' | 'json';      // 请求类型，默认 form-data
}
```

## React/Web 平台

### 1. 使用自定义上传函数

```tsx
import { ChatContent } from '@your-org/ai-component/web';

const MyComponent = () => {
  const handleUpload = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });
    
    const result = await response.json();
    return result.url; // 返回文件URL
  };

  return (
    <ChatContent
      enableFileUpload={true}
      uploadFile={handleUpload}
      onSend={(message, files) => {
        console.log('发送消息:', message, files);
      }}
    />
  );
};
```

### 2. 使用上传配置 - 基础用法

```tsx
import { ChatContent } from '@your-org/ai-component/web';

const MyComponent = () => {
  const uploadConfig = {
    url: '/api/upload',
    method: 'POST' as const,
    fieldName: 'file',
    headers: {
      'Authorization': 'Bearer your-token',
    },
  };

  return (
    <ChatContent
      enableFileUpload={true}
      uploadConfig={uploadConfig}
      onSend={(message, files) => {
        console.log('发送消息:', message, files);
      }}
    />
  );
};
```

### 3. 使用上传配置 - 高级用法

```tsx
import { ChatContent } from '@your-org/ai-component/web';

const MyComponent = () => {
  const uploadConfig = {
    url: '/api/upload',
    method: 'POST' as const,
    headers: {
      'Authorization': 'Bearer your-token',
      'X-Custom-Header': 'custom-value',
    },
    withCredentials: true,
    timeout: 30000,
    data: {
      userId: '123',
      category: 'chat',
    },
    requestType: 'form-data' as const,
    responseParser: (response: any) => {
      // 自定义响应解析
      if (response.success) {
        return {
          url: response.data.fileUrl,
          name: response.data.fileName,
          id: response.data.fileId,
        };
      }
      throw new Error(response.message || '上传失败');
    },
    onProgress: (percent: number, file: File) => {
      console.log(`上传进度: ${percent}%, 文件: ${file.name}`);
    },
    validateStatus: (status: number) => {
      return status >= 200 && status < 300;
    },
  };

  return (
    <ChatContent
      enableFileUpload={true}
      uploadConfig={uploadConfig}
      onError={(error) => {
        console.error('上传错误:', error);
      }}
    />
  );
};
```

### 4. JSON 格式上传

```tsx
const uploadConfig = {
  url: '/api/upload-json',
  method: 'POST' as const,
  requestType: 'json' as const,
  headers: {
    'Authorization': 'Bearer your-token',
  },
  data: {
    bucket: 'my-bucket',
    folder: 'chat-files',
  },
  responseParser: (response: any) => ({
    url: response.fileUrl,
    name: response.fileName,
  }),
};
```

## Vue 平台

### 1. 使用自定义上传函数

```vue
<template>
  <ChatContent
    :enableFileUpload="true"
    :uploadFile="handleUpload"
    @send="handleSend"
  />
</template>

<script setup>
import { ChatContent } from '@your-org/ai-component/vue';

const handleUpload = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
  });
  
  const result = await response.json();
  return result.url;
};

const handleSend = (message, files) => {
  console.log('发送消息:', message, files);
};
</script>
```

### 2. 使用上传配置

```vue
<template>
  <ChatContent
    :enableFileUpload="true"
    :uploadConfig="uploadConfig"
    @send="handleSend"
    @error="handleError"
  />
</template>

<script setup>
import { ref } from 'vue';
import { ChatContent } from '@your-org/ai-component/vue';

const uploadConfig = ref({
  url: '/api/upload',
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-token',
  },
  data: {
    userId: '123',
  },
  onProgress: (percent, file) => {
    console.log(`上传进度: ${percent}%, 文件: ${file.name}`);
  },
  responseParser: (response) => {
    if (response.success) {
      return {
        url: response.data.url,
        name: response.data.name,
      };
    }
    throw new Error(response.message || '上传失败');
  },
});

const handleSend = (message, files) => {
  console.log('发送消息:', message, files);
};

const handleError = (error) => {
  console.error('错误:', error);
};
</script>
```

## Taro 平台

### 1. 使用自定义上传函数

```tsx
import { TaroAIChatComponent } from '@your-org/ai-component/taro';
import Taro from '@tarojs/taro';

const MyComponent = () => {
  const handleUpload = async (filePath: string) => {
    return new Promise((resolve, reject) => {
      Taro.uploadFile({
        url: '/api/upload',
        filePath,
        name: 'file',
        header: {
          'Authorization': 'Bearer your-token',
        },
        success: (res) => {
          const data = JSON.parse(res.data);
          resolve(data.url);
        },
        fail: reject,
      });
    });
  };

  return (
    <TaroAIChatComponent
      enableFileUpload={true}
      uploadFile={handleUpload}
      onSend={(message, files) => {
        console.log('发送消息:', message, files);
      }}
    />
  );
};
```

### 2. 使用上传配置 - FormData 格式

```tsx
import { TaroAIChatComponent } from '@your-org/ai-component/taro';

const MyComponent = () => {
  const uploadConfig = {
    url: '/api/upload',
    method: 'POST' as const,
    headers: {
      'Authorization': 'Bearer your-token',
    },
    data: {
      userId: '123',
      source: 'taro-app',
    },
    fieldName: 'file',
    timeout: 30000,
    responseParser: (response: any) => {
      if (response.success) {
        return {
          url: response.data.url,
          name: response.data.name,
        };
      }
      throw new Error(response.message || '上传失败');
    },
  };

  return (
    <TaroAIChatComponent
      enableFileUpload={true}
      uploadConfig={uploadConfig}
      onError={(error) => {
        console.error('上传错误:', error);
      }}
    />
  );
};
```

### 3. 使用上传配置 - JSON 格式

```tsx
const uploadConfig = {
  url: '/api/upload-base64',
  method: 'POST' as const,
  requestType: 'json' as const,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token',
  },
  data: {
    platform: 'taro',
    appId: 'your-app-id',
  },
  fieldName: 'imageData',
  responseParser: (response: any) => ({
    url: response.imageUrl,
    name: response.imageName,
  }),
};
```

## 服务端 API 示例

### Express.js 后端示例

```javascript
const express = require('express');
const multer = require('multer');
const app = express();

// FormData 上传处理
const upload = multer({ dest: 'uploads/' });

app.post('/api/upload', upload.single('file'), (req, res) => {
  const file = req.file;
  if (!file) {
    return res.status(400).json({ success: false, message: '没有文件' });
  }
  
  // 这里处理文件保存逻辑
  const fileUrl = `https://your-domain.com/files/${file.filename}`;
  
  res.json({
    success: true,
    data: {
      url: fileUrl,
      name: file.originalname,
      size: file.size,
    }
  });
});

// JSON 格式上传处理
app.post('/api/upload-json', express.json({ limit: '10mb' }), (req, res) => {
  const { file, userId, category } = req.body;
  
  if (!file || !file.content) {
    return res.status(400).json({ success: false, message: '没有文件内容' });
  }
  
  // 处理 base64 内容
  const base64Data = file.content.replace(/^data:image\/\w+;base64,/, '');
  const buffer = Buffer.from(base64Data, 'base64');
  
  // 保存文件逻辑
  const filename = `${Date.now()}-${file.name}`;
  // ... 保存文件到磁盘或云存储
  
  res.json({
    success: true,
    data: {
      url: `https://your-domain.com/files/${filename}`,
      name: file.name,
    }
  });
});
```

## 测试用例

### 单元测试示例

```typescript
// upload.test.ts
import { uploadFileWithConfig } from '@your-org/ai-component/web/utils/upload';

describe('文件上传配置', () => {
  it('应该正确上传文件并返回URL', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const config = {
      url: '/api/upload',
      method: 'POST' as const,
      responseParser: (res: any) => ({ url: res.url }),
    };
    
    // Mock fetch
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      status: 200,
      json: async () => ({ url: 'https://example.com/test.jpg' }),
    });
    
    const result = await uploadFileWithConfig(mockFile, config);
    expect(result).toEqual({ url: 'https://example.com/test.jpg' });
  });
  
  it('应该在上传失败时抛出错误', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const config = {
      url: '/api/upload',
      method: 'POST' as const,
    };
    
    global.fetch = jest.fn().mockResolvedValue({
      ok: false,
      status: 500,
      statusText: 'Internal Server Error',
    });
    
    await expect(uploadFileWithConfig(mockFile, config)).rejects.toThrow();
  });
});
```

## 注意事项

1. **安全性**: 始终在服务端验证文件类型和大小
2. **错误处理**: 使用 `onError` 回调处理上传错误
3. **进度反馈**: 使用 `onProgress` 回调提供用户反馈
4. **跨域**: 配置 CORS 或使用 `withCredentials`
5. **文件大小**: 设置合适的 `maxFileSize` 限制
6. **超时设置**: 根据网络情况设置合适的 `timeout`

## 迁移指南

如果你之前使用 `uploadFile` 属性，现在可以：

1. **保持现有代码不变** - `uploadFile` 仍然支持
2. **迁移到配置方式** - 使用 `uploadConfig` 获得更多控制
3. **混合使用** - `uploadFile` 优先级高于 `uploadConfig`