#!/usr/bin/env node

/**
 * AI Component 主构建脚本
 * 前端开发专家级构建系统 - 支持按需打包、性能优化、Tree-shaking
 */

const fs = require('fs')
const path = require('path')
const { execSync, spawn } = require('child_process')
const {
  BUILD_CONFIG,
  getAllPackages,
  checkBuildEnvironment,
  getBuildStats,
  formatBytes
} = require('./build.config')

class AIComponentBuilder {
  constructor(options = {}) {
    this.options = {
      mode: 'production',
      watch: false,
      analyze: false,
      verbose: false,
      packages: [],
      platforms: ['web', 'vue', 'taro'],
      ...options
    }
    
    this.buildStats = []
    this.startTime = Date.now()
  }

  // 主构建流程
  async build() {
    try {
      console.log('🏗️  AI Component 构建系统启动')
      console.log(`📦 模式: ${this.options.mode}`)
      console.log(`🎯 平台: ${this.options.platforms.join(', ')}`)
      
      // 环境检查
      checkBuildEnvironment()
      
      // 获取要构建的包
      const packages = this.getPackagesToBuild()
      
      console.log(`\n📋 构建计划:`)
      packages.forEach((pkg, index) => {
        console.log(`  ${index + 1}. ${pkg.fullName} (${pkg.version})`)
      })
      
      // 清理输出目录
      await this.cleanBuildOutputs(packages)
      
      // 按依赖顺序构建
      for (const pkg of packages) {
        await this.buildPackage(pkg)
      }
      
      // 构建完成统计
      this.printBuildSummary()
      
      // 运行分析（如果启用）
      if (this.options.analyze) {
        await this.analyzeBundles()
      }
      
      console.log(`\n✅ 构建完成! 总耗时: ${this.getElapsedTime()}`)
      
    } catch (error) {
      console.error('\n❌ 构建失败:', error.message)
      if (this.options.verbose) {
        console.error(error.stack)
      }
      process.exit(1)
    }
  }

  // 获取要构建的包列表
  getPackagesToBuild() {
    const allPackages = getAllPackages()
    
    if (this.options.packages.length > 0) {
      return allPackages.filter(pkg => 
        this.options.packages.includes(pkg.name)
      )
    }
    
    return allPackages
  }

  // 清理构建输出
  async cleanBuildOutputs(packages) {
    console.log('\n🧹 清理构建输出...')
    
    for (const pkg of packages) {
      const distPath = path.join(pkg.path, 'dist')
      if (fs.existsSync(distPath)) {
        fs.rmSync(distPath, { recursive: true, force: true })
        console.log(`  ✓ 清理 ${pkg.name}/dist`)
      }
    }
  }

  // 构建单个包
  async buildPackage(pkg) {
    console.log(`\n🔨 构建 ${pkg.fullName}...`)
    
    try {
      // 检查是否需要特殊构建脚本
      if (pkg.name === 'ui') {
        await this.buildUIPackage(pkg)
      } else {
        await this.buildStandardPackage(pkg)
      }
      
      // 收集构建统计
      const stats = getBuildStats(pkg.name, path.join(pkg.path, 'dist'))
      this.buildStats.push(stats)
      
      console.log(`  ✅ ${pkg.name} 构建完成 (${stats.totalSizeFormatted})`)
      
    } catch (error) {
      console.error(`  ❌ ${pkg.name} 构建失败:`, error.message)
      throw error
    }
  }

  // 构建标准包（adapters, core, config）
  async buildStandardPackage(pkg) {
    const buildScript = path.join(__dirname, 'scripts', 'build-standard.js')
    await this.runBuildScript(buildScript, pkg)
  }

  // 构建UI包（支持多平台）
  async buildUIPackage(pkg) {
    const buildScript = path.join(__dirname, 'scripts', 'build-ui.js')
    
    // 为每个平台构建
    for (const platform of this.options.platforms) {
      console.log(`    📱 构建 ${platform} 平台...`)
      await this.runBuildScript(buildScript, pkg, { platform })
    }
  }

  // 运行构建脚本
  async runBuildScript(scriptPath, pkg, extraOptions = {}) {
    return new Promise((resolve, reject) => {
      const options = {
        mode: this.options.mode,
        watch: this.options.watch,
        verbose: this.options.verbose,
        ...extraOptions
      }
      
      const child = spawn('node', [scriptPath, pkg.path, JSON.stringify(options)], {
        stdio: this.options.verbose ? 'inherit' : 'pipe',
        cwd: __dirname
      })
      
      let output = ''
      
      if (!this.options.verbose) {
        child.stdout?.on('data', (data) => {
          output += data.toString()
        })
        
        child.stderr?.on('data', (data) => {
          output += data.toString()
        })
      }
      
      child.on('close', (code) => {
        if (code === 0) {
          resolve(output)
        } else {
          reject(new Error(`Build script failed with code ${code}\n${output}`))
        }
      })
      
      child.on('error', reject)
    })
  }

  // 打印构建摘要
  printBuildSummary() {
    console.log('\n📊 构建摘要:')
    console.log('┌─────────────────┬──────────────┬──────────────┐')
    console.log('│ 包名            │ 文件数       │ 总大小       │')
    console.log('├─────────────────┼──────────────┼──────────────┤')
    
    let totalSize = 0
    let totalFiles = 0
    
    this.buildStats.forEach(stats => {
      const fileCount = Object.keys(stats.files).length
      totalFiles += fileCount
      totalSize += stats.totalSize
      
      console.log(`│ ${stats.package.padEnd(15)} │ ${fileCount.toString().padEnd(12)} │ ${stats.totalSizeFormatted.padEnd(12)} │`)
    })
    
    console.log('├─────────────────┼──────────────┼──────────────┤')
    console.log(`│ 总计            │ ${totalFiles.toString().padEnd(12)} │ ${formatBytes(totalSize).padEnd(12)} │`)
    console.log('└─────────────────┴──────────────┴──────────────┘')
  }

  // 分析bundle
  async analyzeBundles() {
    console.log('\n📈 正在分析 bundles...')
    
    for (const stats of this.buildStats) {
      const analysisPath = path.join(__dirname, 'packages', stats.package, 'dist', 'analysis')
      
      if (fs.existsSync(analysisPath)) {
        console.log(`  📊 ${stats.package} 分析报告: ${analysisPath}/bundle-analysis.html`)
      }
    }
  }

  // 获取运行时间
  getElapsedTime() {
    const elapsed = Date.now() - this.startTime
    const seconds = Math.floor(elapsed / 1000)
    const ms = elapsed % 1000
    return `${seconds}.${ms.toString().padStart(3, '0')}s`
  }

  // 监听模式
  async watch() {
    console.log('👀 启动监听模式...')
    this.options.watch = true
    
    // 初始构建
    await this.build()
    
    // 监听文件变化
    const chokidar = require('chokidar')
    const packages = this.getPackagesToBuild()
    
    packages.forEach(pkg => {
      const srcPath = path.join(pkg.path, '**/*.{ts,tsx,vue,scss,css}')
      
      chokidar.watch(srcPath, {
        ignored: /node_modules|dist/,
        persistent: true
      }).on('change', async (filePath) => {
        console.log(`\n📝 文件变更: ${path.relative(process.cwd(), filePath)}`)
        
        try {
          await this.buildPackage(pkg)
          console.log(`✅ ${pkg.name} 重新构建完成`)
        } catch (error) {
          console.error(`❌ ${pkg.name} 重新构建失败:`, error.message)
        }
      })
    })
    
    console.log('✅ 监听模式已启动，按 Ctrl+C 退出')
  }
}

// CLI 入口
async function main() {
  const args = process.argv.slice(2)
  const options = {}
  
  // 解析命令行参数
  for (let i = 0; i < args.length; i++) {
    const arg = args[i]
    
    switch (arg) {
      case '--mode':
        options.mode = args[++i]
        break
      case '--watch':
        options.watch = true
        break
      case '--analyze':
        options.analyze = true
        break
      case '--verbose':
        options.verbose = true
        break
      case '--packages':
        options.packages = args[++i].split(',')
        break
      case '--platforms':
        options.platforms = args[++i].split(',')
        break
      case '--help':
        console.log(`
AI Component 构建系统

用法:
  node build.js [选项]

选项:
  --mode <mode>        构建模式 (development|production) [默认: production]
  --watch              监听模式
  --analyze            生成 bundle 分析报告
  --verbose            详细输出
  --packages <list>    要构建的包 (逗号分隔)
  --platforms <list>   要构建的平台 (逗号分隔) [默认: web,vue,taro]
  --help               显示帮助信息

示例:
  node build.js                                    # 构建所有包
  node build.js --mode development --watch         # 开发模式 + 监听
  node build.js --packages ui --platforms web      # 只构建UI包的web平台
  node build.js --analyze                          # 构建并分析
        `)
        process.exit(0)
        break
    }
  }
  
  const builder = new AIComponentBuilder(options)
  
  if (options.watch) {
    await builder.watch()
  } else {
    await builder.build()
  }
}

// 处理未捕获异常
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获异常:', error.message)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的 Promise 拒绝:', reason)
  process.exit(1)
})

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 构建失败:', error.message)
    process.exit(1)
  })
}

module.exports = AIComponentBuilder