#!/usr/bin/env node

/**
 * 测试 postcss 插件是否工作
 */

const { rollup } = require('rollup')
const postcss = require('rollup-plugin-postcss')
const path = require('path')

async function testPostCSS() {
  console.log('🧪 测试 PostCSS 插件...')
  
  try {
    const bundle = await rollup({
      input: path.resolve(__dirname, 'packages/ui/web/chat-component.css'),
      external: (id) => {
        // 将所有非 CSS 的外部依赖标记为外部
        if (id.endsWith('.css')) {
          return false
        }
        return !id.startsWith('.') && !id.startsWith('/')
      },
      plugins: [
        postcss({
          extract: false,
          inject: false,
          plugins: []
        })
      ]
    })
    
    console.log('✅ PostCSS 插件工作正常')
    
    const { output } = await bundle.generate({
      format: 'es'
    })
    
    console.log('📦 生成的输出:')
    output.forEach(chunk => {
      console.log(`  - ${chunk.fileName} (${chunk.type})`)
    })
    
  } catch (error) {
    console.error('❌ PostCSS 插件测试失败:', error.message)
    console.error(error.stack)
  }
}

testPostCSS()
