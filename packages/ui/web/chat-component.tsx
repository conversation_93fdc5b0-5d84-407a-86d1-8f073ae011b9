import React, { useEffect, useState, useRef, useCallback } from 'react'
import { Button } from 'antd'
import { createPortal } from 'react-dom'
import { useDragFloat } from './hooks/useDragFloat'
import { FloatingTrigger } from './components/FloatingTrigger'
import { ChatDialog } from './components/ChatDialog'
import { ChatContent } from './components/ChatContent'
import { AIChatComponentProps } from './types'

// Re-export types for use in other modules
export type { AIChatComponentProps } from './types'
// 注意：实际使用时需要确保这些依赖包已正确安装和配置
// import { useChat } from '@ai-component/core/hooks'
// import { ComponentConfig } from '@ai-component/core/types'  
// import { WebStorageAdapter, StorageManager } from '@ai-component/adapters/storage'
// import { webConfig } from '@ai-component/config/platforms'
// import { ConfigManager } from '@ai-component/core/utils'

import './chat-component.css'

/**
 * Web平台AI聊天组件 - 支持标准模式和悬浮模式
 */
export const AIChatComponent: React.FC<AIChatComponentProps> = (props) => {
  const {
    className,
    style,
    floatingMode = false,
    floatingConfig = {},
    chatDialogConfig = {},
    onFloatingToggle,
    onPositionChange,
    onReceive,
  } = props;

  const {
    isExpanded,
    isDragging,
    position,
    dragRef,
    handleClick,
    handleDragStart,
    setExpanded,
  } = useDragFloat({
    enabled: floatingMode,
    dragEnabled: true,
    rememberPosition: true,
    ...floatingConfig,
  });

  const [unreadCount, setUnreadCount] = useState(0);

  // 状态变化时触发外部回调
  useEffect(() => {
    onPositionChange?.(position);
  }, [position, onPositionChange]);

  useEffect(() => {
    onFloatingToggle?.(isExpanded);
    if (isExpanded) {
      setUnreadCount(0); // 展开时清空未读计数
    }
  }, [isExpanded, onFloatingToggle]);

  const handleNewMessage = useCallback(
    (message: string) => {
      if (floatingMode && !isExpanded) {
        setUnreadCount((prev) => prev + 1);
      }
      onReceive?.(message);
    },
    [floatingMode, isExpanded, onReceive]
  );
  
  // 传递给 ChatContent 的 props，并覆盖 onReceive
  const chatContentProps = {
    ...props,
    onReceive: handleNewMessage,
    showHistory: props.config?.showHistory,
    sessionId: props.config?.sessionId,
  };

  // 清除对话和历史记录的外部方法
  const chatContentRef = useRef<any>(null);
  const handleClearHistory = () => {
    if (chatContentRef.current && typeof chatContentRef.current.clearMessages === 'function') {
      chatContentRef.current.clearMessages();
    }
  };

  // 标准模式
  if (!floatingMode) {
    return (
      <div className={`ai-chat-component ${className || ''}`} style={style}>
        <ChatContent ref={chatContentRef} {...chatContentProps} />
        <Button onClick={handleClearHistory} style={{ margin: 8 }}>清除对话</Button>
      </div>
    );
  }

  // 悬浮模式，使用 Portal 渲染到 body
  return createPortal(
    <>
      {!isExpanded && (
        <FloatingTrigger
          dragRef={dragRef}
          config={floatingConfig}
          unreadCount={unreadCount}
          onClick={handleClick}
          onDragStart={handleDragStart}
          position={position}
          isDragging={isDragging}
        />
      )}
      <ChatDialog
        isVisible={isExpanded}
        position={position}
        config={chatDialogConfig}
        onClose={() => setExpanded(false)}
      >
        <ChatContent ref={chatContentRef} {...chatContentProps} />
        <Button onClick={handleClearHistory} style={{ margin: 8 }}>清除对话</Button>
      </ChatDialog>
    </>,
    document.body
  );
};

export default AIChatComponent 