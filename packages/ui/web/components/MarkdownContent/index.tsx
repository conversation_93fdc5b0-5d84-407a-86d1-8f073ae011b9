import "./index.css";
import { useEffect, useRef, useState, useMemo, memo } from "react";
import * as echarts from "echarts";
import markdownit from "markdown-it";
import markdownItHighlight from "markdown-it-highlightjs";
import markdownItAnchor from "markdown-it-anchor";
import markdownItTocDoneRight from "markdown-it-toc-done-right";
import markdownItKatex from "@traptitech/markdown-it-katex";
import parse, { HTMLReactParserOptions } from "html-react-parser";
import hljs from "highlight.js";
import "highlight.js/styles/github.css";
import "katex/dist/katex.min.css";

interface EChartsRendererProps {
  options: any;
  style?: React.CSSProperties;
}

/** echart渲染组件 - 使用memo优化性能 */
const EChartsRenderer = memo(({ options, style }: EChartsRendererProps) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [isError, setIsError] = useState<boolean>(false);

  useEffect(() => {
    if (!chartRef.current) return;

    try {
      // 初始化图表
      chartInstance.current = echarts.init(chartRef.current);
      let tempOption = options;
      if (tempOption.title && !tempOption.title.textStyle) {
        tempOption.title.textStyle = {
          overflow: "break",
          width: 300,
        };
      }
      if (!tempOption.grid) {
        tempOption.grid = {
          containLabel: true,
        };
      }
      // 柱状图最大宽度（避免宽度太大太丑了）
      if (!tempOption.barMaxWidth) {
        tempOption.barMaxWidth = 30;
      }

      // 给折线图设置最小Y轴,避免从零开始导致图片太窄
      if (
        tempOption.series &&
        tempOption.series[0]?.type === "line" &&
        tempOption.yAxis?.type === "value"
      ) {
        tempOption.yAxis = {
          ...tempOption.yAxis,
          min: function (value: any) {
            // 动态计算最小值
            return Math.floor((value.min * 0.9) / 10) * 10; // 向下取整并留10%余量
          },
        };
      }

      console.log(tempOption, "tempOption");
      chartInstance.current.setOption(tempOption);
    } catch (error) {
      setIsError(true);
      console.error("图表报错", error);
    }

    // 响应式调整
    const resizeHandler = () => chartInstance.current?.resize();
    window.addEventListener("resize", resizeHandler);

    return () => {
      window.removeEventListener("resize", resizeHandler);
      chartInstance.current?.dispose();
    };
  }, []);

  // 更新图表配置
  useEffect(() => {
    try {
      chartInstance.current?.setOption(options);
    } catch (error) {
      setIsError(true);
    }
  }, [options]);

  return (
    <>
      {isError ? (
        <div className="echarts-error">图表渲染失败</div>
      ) : (
        <div
          ref={chartRef}
          style={{
            width: "100%",
            height: 400,
            ...style,
          }}
        />
      )}
    </>
  );
});

/** 创建 Markdown-it 实例的工厂函数 */
const createMarkdownRenderer = () => {
  const md = new markdownit({
    html: true,
    linkify: true,
    typographer: true,
    breaks: true,
    highlight: (str: string, lang: string) => {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(str, { language: lang }).value;
        } catch (_) {}
      }
      return '';
    }
  });

  // 添加插件
  md.use(markdownItHighlight);
  md.use(markdownItKatex, {
    throwOnError: false,
    errorColor: '#cc0000'
  });
  md.use(markdownItAnchor, {
    permalink: false
  });
  md.use(markdownItTocDoneRight);

  return md;
};

const md = createMarkdownRenderer();

// 自定义代码块渲染规则
const originalFenceRule = md.renderer.rules.fence;
md.renderer.rules.fence = (tokens, idx, options, env, renderer) => {
  const token = tokens[idx];
  const [lang] = token.info.split(" ");
  const isEnd = env.isEnd || false;
  // console.log(isEnd, token);
  if (lang === "echarts") {
    try {
      const jsonStr = token.content.trim();
      // console.log(jsonStr);
      const chartOptions = JSON.parse(jsonStr);
      // console.warn(chartOptions, "chartOptions");
      const encodedOptions = encodeURIComponent(jsonStr);
      return `<div class="echarts-container" data-options="${encodedOptions}"></div>`;
    } catch (error: any) {
      if (isEnd) {
        return `<div></div>`;
      }
      return `<div class="echarts-loading">[...正在加载图表]</div>`;
    }
  }

  // 使用原始规则渲染普通代码块，并添加复制按钮
  const originalRender = originalFenceRule ? originalFenceRule(tokens, idx, options, env, renderer) : `<pre><code>${token.content}</code></pre>`;
  const uniqueId = `code-block-${Date.now()}-${idx}`;
  return `<div class="code-block-wrapper" id="${uniqueId}">
    ${originalRender}
    <button class="copy-button" data-code-id="${uniqueId}" title="复制代码">
      <svg class="copy-icon" viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor">
        <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"/>
      </svg>
    </button>
  </div>`;
};

interface MarkdownContentProps {
  content: string;
  bubbleList?: any[];
  isRequesting?: boolean;
  isCancel?: boolean;
  enableCodeCopy?: boolean;
  enableMath?: boolean;
  enableToc?: boolean;
  onContentClick?: (e: React.MouseEvent) => void;
  className?: string;
  // 新增通知回调，用于替代antd的message
  onNotify?: (type: 'success' | 'error', message: string) => void;
}

const MarkdownContent = memo<MarkdownContentProps>(({
  content,
  bubbleList = [],
  isRequesting = false,
  isCancel = false,
  enableCodeCopy = true,
  enableMath = true,
  enableToc = false,
  onContentClick,
  className = '',
  onNotify,
}) => {
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  
  // 判断是否渲染结束
  const isEnd = useMemo(() => {
    if (bubbleList.length === 0) return !isRequesting;
    return (
      (bubbleList[bubbleList.length - 1]?.message === content && !isRequesting) ||
      isCancel
    );
  }, [bubbleList, content, isRequesting, isCancel]);

  // 渲染 Markdown 内容
  const html = useMemo(() => {
    const cleanContent = content.replace("[INVALID]", "");
    return md.render(cleanContent, { isEnd });
  }, [content, isEnd]);

  // 处理代码复制
  useEffect(() => {
    if (!enableCodeCopy || !contentRef.current) return;

    const handleCopyClick = async (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const button = target.closest('.copy-button') as HTMLButtonElement;
      if (!button) return;

      const codeId = button.getAttribute('data-code-id');
      if (!codeId) return;

      const codeBlock = document.getElementById(codeId);
      const codeElement = codeBlock?.querySelector('code');
      if (!codeElement) return;

      try {
        await navigator.clipboard.writeText(codeElement.textContent || '');
        setCopiedId(codeId);
        onNotify?.('success', '代码已复制');
        setTimeout(() => setCopiedId(null), 2000);
      } catch (error) {
        onNotify?.('error', '复制失败');
      }
    };

    contentRef.current.addEventListener('click', handleCopyClick);
    return () => {
      contentRef.current?.removeEventListener('click', handleCopyClick);
    };
  }, [enableCodeCopy, onNotify]);

  const parserOptions: HTMLReactParserOptions = {
    replace: (domNode) => {
      if (domNode.type === "tag" && domNode.name === "div") {
        const classNames = domNode.attribs?.class?.split(" ") || [];
        if (classNames.includes("echarts-container")) {
          try {
            const encodedOptions = domNode.attribs["data-options"];
            const options = JSON.parse(decodeURIComponent(encodedOptions));
            return <EChartsRenderer options={options} />;
          } catch (error) {
            return <div style={{ color: "red" }}>图表渲染失败</div>;
          }
        }
      }
    },
  };

  return (
    <div 
      ref={contentRef}
      className={`markdown-body ${className}`}
      onClick={onContentClick}
    >
      {parse(html, parserOptions)}
    </div>
  );
});

MarkdownContent.displayName = 'MarkdownContent';

export default MarkdownContent;

// 导出工具函数，供其他平台使用
export { createMarkdownRenderer };