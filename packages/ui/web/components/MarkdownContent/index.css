.echarts-loading {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  padding: 20px;
  text-align: center;
}

.echarts-error {
  font-size: 14px;
  color: #ff4d4f;
  padding: 20px;
  text-align: center;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
}

.markdown-body {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
}

/* 基础样式 */
.markdown-body h1, 
.markdown-body h2, 
.markdown-body h3, 
.markdown-body h4, 
.markdown-body h5, 
.markdown-body h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-body h1 { font-size: 2em; }
.markdown-body h2 { font-size: 1.5em; }
.markdown-body h3 { font-size: 1.25em; }
.markdown-body h4 { font-size: 1em; }
.markdown-body h5 { font-size: 0.875em; }
.markdown-body h6 { font-size: 0.85em; }

.markdown-body p {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body ul, 
.markdown-body ol {
  padding-left: 2em;
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body ul > li,
.markdown-body ol > li {
  text-align: justify;
  margin-bottom: 8px;
}

.markdown-body blockquote {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  margin: 0 0 16px 0;
}

.markdown-body table {
  display: block;
  width: 100%;
  overflow: auto;
  border-collapse: collapse;
  margin-bottom: 16px;
}

.markdown-body table th, 
.markdown-body table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-body table th {
  font-weight: 600;
  background-color: #f6f8fa;
}

.markdown-body table tr {
  background-color: #fff;
}

.markdown-body table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.markdown-body code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.markdown-body pre {
  margin-bottom: 16px;
}

.markdown-body pre code {
  display: block;
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #f6f8fa;
  border-radius: 6px;
}

/* 代码块容器 */
.markdown-body .code-block-wrapper {
  position: relative;
  margin-bottom: 16px;
}

.markdown-body .code-block-wrapper:hover .copy-button {
  opacity: 1;
}

/* 复制按钮 */
.markdown-body .copy-button {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.markdown-body .copy-button:hover {
  background: #f3f4f6;
}

.markdown-body .copy-button .copy-icon {
  display: block;
  width: 14px;
  height: 14px;
}

/* 链接 */
.markdown-body a {
  color: #0969da;
  text-decoration: none;
}

.markdown-body a:hover {
  text-decoration: underline;
}

/* 图片和视频 */
.markdown-body img, 
.markdown-body video {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.markdown-body video {
  width: 100%;
}

/* 水平线 */
.markdown-body hr {
  height: 0.25em;
  padding: 0;
  margin: 24px 0;
  background-color: #e1e4e8;
  border: 0;
}

/* KaTeX 数学公式 */
.markdown-body .katex-display {
  text-align: center;
  margin: 16px 0;
}

/* 目录 */
.markdown-body .table-of-contents {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #f6f8fa;
  border-radius: 6px;
}

.markdown-body .table-of-contents ul {
  list-style: none;
  padding-left: 0;
}

.markdown-body .table-of-contents ul ul {
  padding-left: 20px;
}

.markdown-body .table-of-contents a {
  color: #0969da;
  text-decoration: none;
}

.markdown-body .table-of-contents a:hover {
  text-decoration: underline;
}