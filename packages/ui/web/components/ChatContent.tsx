import { ReactElement, useEffect, useState, useRef, useCallback, forwardRef, useImperativeHandle, useMemo } from 'react';
import { Sender, Bubble, Attachments, type AttachmentsProps } from '@ant-design/x';
import { FileImageOutlined, DeleteOutlined, UserOutlined, RobotOutlined, ClearOutlined, CloudUploadOutlined, FilePdfOutlined, FileTextOutlined, FileWordOutlined, FileExcelOutlined, FilePptOutlined, FileOutlined } from '@ant-design/icons';
import { Flex, Button, Tooltip, Avatar, Image, Tag, GetProp, message as antdMessage } from 'antd';
import type { AIChatComponentProps, Message, PromptItem, FileItem, UploadConfig, UploadResponse } from '../types';
import { SEND_CONSTANTS } from '../types';
import MarkdownContent from './MarkdownContent';
import { uploadFileWithConfig } from '../utils/upload';

// 判断是否为图片类型
const isImageFile = (fileType?: string): boolean => {
  return fileType ? SEND_CONSTANTS.SUPPORTED_TYPES.image.includes(fileType) : false;
};

// 这是一个临时的 useChat hook，用于演示
const useChatMock = (config: { showHistory?: boolean, sessionId?: string }) => {
  const SESSION_ID = config.sessionId || 'default-session';

  const initialMessages = () => {
    if (config.showHistory) {
      try {
        const saved = localStorage.getItem(`chat-history-${SESSION_ID}`);
        return saved ? JSON.parse(saved) : [];
      } catch (e) {
        return [];
      }
    }
    return [];
  };

  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [isLoading, setIsLoading] = useState(false);

  // 新增：监听 sessionId/showHistory 变化，重新加载历史
  useEffect(() => {
    if (config.showHistory) {
      try {
        const saved = localStorage.getItem(`chat-history-${SESSION_ID}`);
        setMessages(saved ? JSON.parse(saved) : []);
      } catch (e) {
        setMessages([]);
      }
    } else {
      setMessages([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [config.sessionId, config.showHistory]);

  useEffect(() => {
    if (config.showHistory) {
      try {
        // 限制存储的消息数量，防止超出localStorage配额
        const maxMessages = 50; // 最多保存50条消息
        const messagesToStore = messages.slice(-maxMessages).map(msg => ({
          ...msg,
          // 如果有图片，只保存有限的图片信息，避免存储过大
          images: msg.images?.slice(0, 3).map(img => ({
            url: img.url.substring(0, 100) + '...', // 截断过长的URL
            name: img.name
          }))
        }));
        localStorage.setItem(`chat-history-${SESSION_ID}`, JSON.stringify(messagesToStore));
      } catch (e) {
        // 如果仍然超出配额，清除历史记录
        console.warn('localStorage quota exceeded, clearing chat history');
        localStorage.removeItem(`chat-history-${SESSION_ID}`);
      }
    }
  }, [messages, config.showHistory, SESSION_ID]);

  const sendMessage = async (content: string, files?: FileItem[]) => {
    setIsLoading(true);
    const newMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content,
      createdAt: new Date(),
      images: files && files.length > 0 ? files.filter(f => isImageFile(f.type)).map(f => ({ url: f.url, name: f.name })) : undefined,
      files: files && files.length > 0 ? files.map(f => ({ uid: f.uid, url: f.url, name: f.name, type: f.type, size: f.size })) : undefined
    };
    setMessages((prev) => [...prev, newMessage]);

    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `这是对"${content}"的模拟回复。${files && files.length > 0 ? ` 我看到您发送了 ${files.length} 个文件。` : ''}`,
        createdAt: new Date(),
      };
      setMessages((prev) => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1500);
  };
  const clearMessages = () => {
      setMessages([]);
      if(config.showHistory) {
          localStorage.removeItem(`chat-history-${SESSION_ID}`);
      }
  };
  return { messages, isLoading, sendMessage, clearMessages };
};

type AttachmentsItem = GetProp<AttachmentsProps, 'items'>;

export const ChatContent = forwardRef<any, AIChatComponentProps & {
  showHistory?: boolean;
  sessionId?: string;
  uploadFile?: (file: File) => Promise<string | UploadResponse>;
  uploadConfig?: UploadConfig;
}>(({
  config: customConfig = {},
  onSend,
  onError,
  prompts = [],
  enableFileUpload = true,
  maxFiles = SEND_CONSTANTS.MAX_IMAGES,
  maxFileSize = SEND_CONSTANTS.MAX_SIZE_MB,
  placeholder = SEND_CONSTANTS.DEFAULT_PLACEHOLDER,
  disabled = false,
  avatarConfig = {},
  showHistory,
  sessionId,
  uploadFile,
  uploadConfig,
  // 自定义渲染函数
  renderHeader,
  renderEmpty,
  renderMessage,
  renderMessageActions,
  renderLoading,
  renderInputTools,
  renderFooter,
}, ref) => {
  const finalConfig = {
    showAvatar: avatarConfig.showAvatar !== false,
    placeholder: placeholder,
    maxInputLength: 1000,
    enableAutoScroll: true,
    ...customConfig,
  };

  // 优先使用 props 传入的 showHistory/sessionId
  const { messages, isLoading, sendMessage, clearMessages } = useChatMock({ 
    showHistory: showHistory ?? finalConfig.showHistory, 
    sessionId: sessionId ?? finalConfig.sessionId,
  });

  useImperativeHandle(ref, () => ({
    clearMessages,
  }));

  const contentRef = useRef<HTMLDivElement>(null);
  const messageContainerRef = useRef<HTMLDivElement>(null);
  
  const [inputValue, setInputValue] = useState('');
  const [items, setItems] = useState<AttachmentsItem>([]);
  const [uploading, setUploading] = useState(false);

  // 格式化时间的辅助函数
  const formatTime = useCallback((timestamp?: number) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }, []);

  // 获取文件图标
  const getFileIcon = useCallback((fileType?: string): React.ReactNode => {
    if (!fileType) return <FileOutlined />;
    
    if (SEND_CONSTANTS.SUPPORTED_TYPES.image.includes(fileType)) {
      return <FileImageOutlined />;
    }
    if (fileType === 'application/pdf') {
      return <FilePdfOutlined />;
    }
    if (fileType === 'text/plain') {
      return <FileTextOutlined />;
    }
    if (fileType.includes('word') || fileType.includes('document')) {
      return <FileWordOutlined />;
    }
    if (fileType.includes('excel') || fileType.includes('sheet')) {
      return <FileExcelOutlined />;
    }
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) {
      return <FilePptOutlined />;
    }
    return <FileOutlined />;
  }, []);


  // 构建自定义渲染属性
  const customRenderProps = useMemo(() => ({
    messages,
    isLoading,
    canSend: !disabled && !isLoading && (!!inputValue.trim() || items.length > 0),
    inputValue,
    fileList: items.map(item => ({
      uid: item.uid,
      name: item.name,
      url: (item as any).dataUrl || (item as any).url || '',
      originFileObj: (item as any).originFileObj
    })),
    onUploadClick: () => {},
    onClear: clearMessages,
    onSend: () => handleSendLogic(inputValue),
    disabled: disabled || isLoading,
    formatTime,
  }), [messages, isLoading, disabled, inputValue, items, clearMessages, formatTime]);

  const scrollToBottom = useCallback(() => {
    if (finalConfig.enableAutoScroll && messageContainerRef.current) {
      messageContainerRef.current.scrollTop = messageContainerRef.current.scrollHeight;
    }
  }, [finalConfig.enableAutoScroll]);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const handleFileValidation = (file: File) => {
    let error: string | null = null;

    // 检查文件数量限制
    if (items.length + 1 > maxFiles) {
      error = `最多只能上传 ${maxFiles} 个文件。`;
    }

    // 检查文件类型
    const isAllowed = SEND_CONSTANTS.ACCEPT.split(',').some(type => 
      file.type === type.trim()
    );
    if (!isAllowed) {
      const supportedList = Object.keys(SEND_CONSTANTS.TYPE_DESCRIPTIONS).map(
        type => SEND_CONSTANTS.TYPE_DESCRIPTIONS[type as keyof typeof SEND_CONSTANTS.TYPE_DESCRIPTIONS]
      ).join('、');
      error = `仅支持以下格式：${supportedList}`;
    }

    if (file.size! / 1024 / 1024 > maxFileSize) {
      error = `文件 ${file.name} 大小超过 ${maxFileSize}MB。`;
    }

    if (error) {
      onError?.(error);
      antdMessage.error(error);
      return false;
    }

    return true;
  };

  const handleFilesChange = (files: AttachmentsItem) => {
    const validFiles = files.filter((file: any) => handleFileValidation(file));

    if (validFiles.length === 0) return;

    // 将 File 对象转为 dataURL
    const processFiles = async () => {
      const newItems = await Promise.all(validFiles.map(async (file: any) => {
        if (file.originFileObj && !file.dataUrl) {
          // 只处理本地新文件
          const dataUrl = await new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target?.result as string);
            reader.readAsDataURL(file.originFileObj);
          });
          return { ...file, dataUrl, type: file.originFileObj.type, size: file.originFileObj.size };
        }
        return file;
      }));
      setItems(newItems);
    };
    processFiles();
  };
  
  const handleRemoveFile = (uid: string) => {
    setItems(current => current.filter(file => file.uid !== uid));
  };

  const uploaderPlaceholder = (type: string) => {
    if (type === 'drop') {
      return {
        title: 'Drop file here',
      }
    }
    return {
      icon: <CloudUploadOutlined />,
      title: 'Upload files',
      description: 'Click or drag files to this area to upload',
    }
  }

  const handleSendLogic = async (content: string) => {
    const trimmedContent = content.trim();

    if (!trimmedContent && items.length === 0) return;
    let fileItems: FileItem[] = [];
    
    if (items.length > 0) {
      if (uploadFile || uploadConfig) {
        // 如果提供了上传方法或上传配置，执行上传
        setUploading(true);
        try {
          const uploaded = await Promise.all(items.map(async (file: any) => {
            let res: string | UploadResponse;
            
            if (uploadFile) {
              // 优先使用自定义上传函数
              res = await uploadFile(file.originFileObj);
            } else if (uploadConfig) {
              // 使用上传配置
              res = await uploadFileWithConfig(
                file.originFileObj, 
                uploadConfig,
                onError
              );
            } else {
              throw new Error('未配置上传方式');
            }
            
            const url = typeof res === 'string' ? res : res.url;
            return {
              uid: file.uid,
              name: file.name,
              url,
              originFileObj: file.originFileObj,
              // 保留额外的响应数据
              ...(typeof res === 'object' ? res : {})
            };
          }));
          fileItems = uploaded;
        } catch (e: any) {
          antdMessage.error(e?.message || '文件上传失败');
          setUploading(false);
          return;
        }
        setUploading(false);
      } else {
        // 如果没有上传方法或配置，直接使用本地文件的dataUrl
        fileItems = items.map((file: any) => ({
          uid: file.uid,
          name: file.name,
          url: file.dataUrl || file.url || '',
          originFileObj: file.originFileObj,
          type: file.type || file.originFileObj?.type,
          size: file.size || file.originFileObj?.size,
        }));
      }
      setItems([]); // 清空本地预览
    }
    
    onSend?.(trimmedContent, fileItems);
    await sendMessage(trimmedContent, fileItems); // Using internal mock with files
    setInputValue('');
  };

  const renderAvatar = (role: 'user' | 'assistant'): ReactElement | undefined => {
    if (!finalConfig.showAvatar) return undefined;
    const roleConfig = role === 'user' ? avatarConfig.user : avatarConfig.assistant;
    const finalSize = roleConfig?.size || avatarConfig.size || 'default'; // 优先使用角色特定尺寸
    const icon = role === 'user' ? <UserOutlined /> : <RobotOutlined />;
    return <Avatar src={roleConfig?.src} icon={roleConfig?.icon || icon} size={finalSize} />;
  };

  const renderSendHeader = () => {
    if (!enableFileUpload || items.length === 0) return null;
    return (
      <div className="send-header">
        <div className="send-file-preview__container">
          {items.map((file: any) => (
            <div key={file.uid} className="send-file-preview__item">
              {isImageFile(file.type || file.originFileObj?.type) ? (
                <Image src={file.dataUrl || file.url} alt={file.name} className="send-file-preview__img" />
              ) : (
                <div className="send-file-preview__file">
                  <div className="send-file-preview__icon">
                    {getFileIcon(file.type || file.originFileObj?.type)}
                  </div>
                  <div className="send-file-preview__info">
                    <div className="send-file-preview__name" title={file.name}>
                      {file.name}
                    </div>
                    <div className="send-file-preview__type">
                      {SEND_CONSTANTS.TYPE_DESCRIPTIONS[(file.type || file.originFileObj?.type) as keyof typeof SEND_CONSTANTS.TYPE_DESCRIPTIONS] || '未知文件'}
                    </div>
                  </div>
                </div>
              )}
              <Button
                className="send-file-preview__btn"
                size="small"
                type="text"
                icon={<DeleteOutlined />}
                onClick={() => handleRemoveFile(file.uid)}
              />
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderSendFooter = (components: any) => {
    if (renderInputTools) {
      return renderInputTools(customRenderProps);
    }
    
    return (
      <Flex justify="space-between" align="center">
        <Flex gap="small">
          {enableFileUpload && (
             <Tooltip title="上传文件">
                <Attachments
                  beforeUpload={() => false}
                  disabled={disabled || isLoading || items.length >= maxFiles}
                  onChange={({ fileList }: any) => handleFilesChange(fileList)}
                  maxCount={maxFiles}
                  items={items}
                  getDropContainer={() => contentRef.current}
                  placeholder={uploaderPlaceholder}
                  accept={SEND_CONSTANTS.ACCEPT}
                >
                  <Button type="text" icon={<FileImageOutlined />} />
                </Attachments>
             </Tooltip>
          )}
          <Tooltip title="清空对话">
            <Button
              type="text"
              icon={<ClearOutlined />} 
              onClick={clearMessages}
              disabled={disabled || isLoading || messages.length === 0}
            />
          </Tooltip>
        </Flex>
        {components.SendButton && <components.SendButton />}
      </Flex>
    );
  };

  return (
    <div className="ai-chat-content" ref={contentRef}>
      {/* 自定义头部 */}
      {renderHeader && (
        <div className="chat-header-slot">
          {renderHeader(customRenderProps)}
        </div>
      )}
      
      <div ref={messageContainerRef} className="messages-container">
        {/* 自定义空状态 */}
        {messages.length === 0 && !isLoading && (
          <div className="empty-state-slot">
            {renderEmpty ? renderEmpty(customRenderProps) : (
              <div className="default-empty-state">
                <div className="empty-icon">💬</div>
                <div className="empty-text">开始与 AI 助手对话吧</div>
              </div>
            )}
          </div>
        )}
        
        {messages.map((msg) => (
          <div key={msg.id} className={`bubble-wrapper bubble-${msg.role}`}>
             <Bubble
                className='message-content'
                role={msg.role}
                content={
                  renderMessage ? renderMessage(msg, customRenderProps) : (
                    <div>
                      {msg.files && msg.files.length > 0 && (
                        <div className="message-files">
                          {msg.files.map((file, index) => (
                            <div key={file.uid || index} className="message-file-item">
                              {isImageFile(file.type) ? (
                                <img src={file.url} alt={file.name} className="message-image" />
                              ) : (
                                <div className="message-file">
                                  <div className="message-file-icon">
                                    {getFileIcon(file.type)}
                                  </div>
                                  <div className="message-file-info">
                                    <div className="message-file-name" title={file.name}>
                                      {file.name}
                                    </div>
                                    <div className="message-file-type">
                                      {SEND_CONSTANTS.TYPE_DESCRIPTIONS[file.type as keyof typeof SEND_CONSTANTS.TYPE_DESCRIPTIONS] || '未知文件'}
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                      {msg.content && (
                        msg.role === 'assistant' ? (
                          <MarkdownContent 
                            content={msg.content} 
                            isRequesting={false}
                            enableCodeCopy={true}
                            enableMath={true}
                            onNotify={(type, msg) => {
                              if (type === 'success') {
                                antdMessage.success(msg);
                              } else {
                                antdMessage.error(msg);
                              }
                            }}
                          />
                        ) : (
                          <div>{msg.content}</div>
                        )
                      )}
                      {renderMessageActions && (
                        <div className="message-actions-slot">
                          {renderMessageActions(msg, customRenderProps)}
                        </div>
                      )}
                    </div>
                  )
                }
                avatar={renderAvatar(msg.role)}
                placement={msg.role === 'user' ? 'end' : 'start'}
              />
          </div>
        ))}
        
        {isLoading && (
          <div className="bubble-wrapper bubble-assistant">
             <Bubble 
               role="assistant" 
               loading={renderLoading ? false : true}
               avatar={renderAvatar('assistant')} 
               placement='start'
               content={renderLoading ? renderLoading(customRenderProps) : undefined}
             />
          </div>
        )}
      </div>
      {prompts && prompts.length > 0 && (
        <div className="prompts-container">
          {prompts.map((prompt: PromptItem) => (
            <Tag
              key={prompt.key}
              className="prompt-tag"
              onClick={() => handleSendLogic(prompt.content!)}
            >
              {prompt.title!}
            </Tag>
          ))}
        </div>
      )}
      <div className="sender-wrapper">
        <Sender
          value={inputValue}
          onChange={setInputValue}
          placeholder={finalConfig.placeholder}
          onSubmit={(v) => { if (v && !uploading) handleSendLogic(v); }}
          disabled={disabled || isLoading || uploading}
          header={renderSendHeader()}
          footer={renderSendFooter}
        />
      </div>
      
      {/* 自定义底部 */}
      {renderFooter && (
        <div className="chat-footer-slot">
          {renderFooter(customRenderProps)}
        </div>
      )}
    </div>
  );
}); 