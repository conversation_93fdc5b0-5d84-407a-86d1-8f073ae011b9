import React from 'react';

// 基础消息类型
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp?: number;
  createdAt: Date;
  images?: Array<{
    url: string;
    name: string;
  }>;
  files?: Array<{
    uid: string;
    url: string;
    name: string;
    type?: string;
    size?: number;
  }>;
}

// 文件对象类型
export interface FileItem {
  uid: string;
  name: string;
  url: string;
  originFileObj: File;
  type?: string;
  size?: number;
}

// 提示词项类型
export interface PromptItem {
  key: string;
  label?: string;
  title?: string;
  content?: string;
  icon?: React.ReactNode;
  show?: boolean;
}

// 通用组件配置
export interface ComponentConfig {
  showAvatar?: boolean;
  placeholder?: string;
  maxInputLength?: number;
  enableAutoScroll?: boolean;
  timeout?: number;
  style?: React.CSSProperties;
  theme?: 'light' | 'dark';
  showHistory?: boolean;
  sessionId?: string;
}

// 悬浮配置接口
export interface FloatingConfig {
  enabled?: boolean;
  icon?: React.ReactNode | string;
  position?: {
    x?: number;
    y?: number;
    side?: 'left' | 'right';
  };
  dragEnabled?: boolean;
  rememberPosition?: boolean;
}

// 头像配置
export interface AvatarConfig {
  size?: number | 'small' | 'default' | 'large';
  user?: {
    src?: string;
    icon?: React.ReactNode;
    size?: number | 'small' | 'default' | 'large';
  };
  assistant?: {
    src?: string;
    icon?: React.ReactNode;
    size?: number | 'small' | 'default' | 'large';
  };
  showAvatar?: boolean;
}

// 聊天对话框配置
export interface ChatDialogConfig {
  title?: string;
  width?: number;
  height?: number;
  minWidth?: number;
  minHeight?: number;
  position?: 'auto' | 'left' | 'right' | 'center';
}

// 自定义渲染函数类型
export interface CustomRenderProps {
  messages: Message[];
  isLoading: boolean;
  canSend?: boolean;
  inputValue?: string;
  fileList?: FileItem[];
  onUploadClick?: () => void;
  onClear?: () => void;
  onSend?: () => void;
  disabled?: boolean;
  formatTime?: (timestamp?: number) => string;
}

// 文件上传配置
export interface UploadConfig {
  url: string;                              // 上传地址
  method?: 'POST' | 'PUT' | 'PATCH';      // 请求方法，默认 POST
  headers?: Record<string, string>;         // 自定义请求头
  withCredentials?: boolean;                // 是否携带凭证
  data?: Record<string, any>;              // 额外的表单数据或JSON数据
  fieldName?: string;                       // 文件字段名，默认 'file'
  timeout?: number;                         // 超时时间（毫秒）
  responseParser?: (response: any) => {     // 响应解析函数
    url: string;
    name?: string;
    [key: string]: any;
  };
  onProgress?: (percent: number, file: File) => void;   // 上传进度回调
  validateStatus?: (status: number) => boolean; // 验证响应状态
  requestType?: 'form-data' | 'json';      // 请求类型，默认 form-data
}

// 文件上传响应
export interface UploadResponse {
  url: string;
  name?: string;
  [key: string]: any;
}

// 主组件 Props
export interface AIChatComponentProps {
  config?: Partial<ComponentConfig>;
  onSend?: (message: string, files?: FileItem[]) => void;
  onReceive?: (message: string) => void;
  onError?: (error: string) => void;
  className?: string;
  style?: React.CSSProperties;
  prompts?: PromptItem[];
  enableFileUpload?: boolean;
  maxFiles?: number;
  maxFileSize?: number;
  placeholder?: string;
  disabled?: boolean;
  
  // 文件上传相关
  uploadFile?: (file: File) => Promise<string | UploadResponse>; // 自定义上传函数
  uploadConfig?: UploadConfig;             // 上传配置（当不提供uploadFile时使用）
  
  // 新增的 Props
  floatingMode?: boolean;
  floatingConfig?: Partial<FloatingConfig>;
  avatarConfig?: Partial<AvatarConfig>;
  chatDialogConfig?: Partial<ChatDialogConfig>;
  onFloatingToggle?: (expanded: boolean) => void;
  onPositionChange?: (position: { x: number; y: number; side: 'left' | 'right' }) => void;
  renderSenderHeader?: () => React.ReactNode;
  showFloatingEffect?: boolean;
  
  // 自定义插槽渲染函数
  renderHeader?: (props: CustomRenderProps) => React.ReactNode;
  renderEmpty?: (props: CustomRenderProps) => React.ReactNode;
  renderMessage?: (message: Message, props: CustomRenderProps) => React.ReactNode;
  renderMessageActions?: (message: Message, props: CustomRenderProps) => React.ReactNode;
  renderLoading?: (props: CustomRenderProps) => React.ReactNode;
  renderInputTools?: (props: CustomRenderProps) => React.ReactNode;
  renderFooter?: (props: CustomRenderProps) => React.ReactNode;
}

// 常量
export const SEND_CONSTANTS = {
  MAX_IMAGES: 9,
  MAX_SIZE_MB: 10,
  ACCEPT: 'image/jpeg,image/png,image/webp,image/jpg,application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ACCEPT_EXTENSIONS: '.jpg,.jpeg,.png,.webp,.pdf,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx',
  DEFAULT_PLACEHOLDER: '输入您想要提问的问题并点击发送',
  SUPPORTED_TYPES: {
    image: ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'],
    document: ['application/pdf', 'text/plain'],
    office: [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel', 
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ]
  },
  TYPE_DESCRIPTIONS: {
    'image/jpeg': 'JPEG图片',
    'image/png': 'PNG图片', 
    'image/webp': 'WebP图片',
    'image/jpg': 'JPG图片',
    'application/pdf': 'PDF文档',
    'text/plain': '文本文件',
    'application/msword': 'Word文档',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word文档',
    'application/vnd.ms-excel': 'Excel表格',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel表格',
    'application/vnd.ms-powerpoint': 'PowerPoint演示',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint演示'
  }
};

// 快捷提示
export interface Prompt {
  key: string;
  title: string;
  content: string;
} 