.ai-chat-component {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--bg-color);
  min-width: 320px;
}

.ai-chat-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
}

.empty-chat-container {
  flex: 1; /* Occupy all available space */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.send-header {
  padding-block-start: 12px;
  padding-inline-start: 16px;
  padding-inline-end: 12px;
}

.send-file-preview__container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  transition: height 0.3s ease-in-out;
}

.send-file-preview__item {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  background: #fafafa;
}

.send-file-preview__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.send-file-preview__file {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px;
  text-align: center;
}

.send-file-preview__icon {
  font-size: 18px;
  color: #666;
  margin-bottom: 2px;
}

.send-file-preview__info {
  flex: 1;
  overflow: hidden;
}

.send-file-preview__name {
  font-size: 10px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.send-file-preview__type {
  font-size: 9px;
  color: #999;
  margin-top: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.send-file-preview__btn {
  position: absolute;
  top: -2px;
  right: -2px;
  display: flex;
  color: #fff !important;
  background-color: rgba(0, 0, 0, 0.65) !important;
  z-index: 2;
  border-radius: 8px;
  min-width: 16px;
}

.send-file-preview__btn .anticon {
  font-size: 10px;
}

/* 消息文件样式 */
.message-files {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.message-file-item {
  position: relative;
  max-width: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.message-image {
  width: 100%;
  height: auto;
  display: block;
  cursor: pointer;
  transition: transform 0.2s;
  border-radius: 8px;
}

.message-image:hover {
  transform: scale(1.05);
}

.message-file {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 150px;
}

.message-file:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  transform: translateY(-1px);
}

.message-file-icon {
  font-size: 20px;
  color: #666;
  margin-right: 8px;
  flex-shrink: 0;
}

.message-file-info {
  flex: 1;
  overflow: hidden;
}

.message-file-name {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.message-file-type {
  font-size: 11px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 自定义插槽样式 */
.chat-header-slot {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color, #f0f0f0);
  background: var(--surface-color, #fafafa);
}

.empty-state-slot {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 16px;
}

.default-empty-state {
  text-align: center;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
}

.message-actions-slot {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--border-color, #f0f0f0);
}

.chat-footer-slot {
  padding: 12px 16px;
  border-top: 1px solid var(--border-color, #f0f0f0);
  background: var(--surface-color, #fafafa);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-chat-component {
    min-height: 300px;
  }
  
  .messages-container {
    padding: 8px;
  }
  
  .send-file-preview__item {
    width: 70px;
    height: 50px;
  }
}

/* 悬浮触发器 */
.floating-trigger {
  position: fixed;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1890ff 0%, #4ca1af 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  z-index: 1000;
  color: white;
  font-size: 24px;
}
.floating-trigger:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}
.floating-trigger.dragging {
  cursor: grabbing;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
}

/* 遮罩层 */
.chat-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.05); /* 调整为更柔和的遮罩 */
  z-index: 999;
}

/* 对话框容器 */
.chat-dialog {
  position: fixed;
  background: white;
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: dialogAppear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  will-change: left, top, width, height, border-radius, opacity, transform;
  transition: none;
}

.chat-dialog.animating {
  transition: all 350ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes dialogAppear {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.chat-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.chat-dialog-title {
  font-weight: 600;
  font-size: 16px;
}

.chat-dialog-close {
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #999;
  transition: all 0.2s;
}

.chat-dialog-close:hover {
  background: #f0f0f0;
  color: #333;
}

.chat-dialog-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

/* 消息气泡对齐 */
.bubble-wrapper {
  display: flex;
  margin-bottom: 16px;
}
.bubble-wrapper.bubble-user {
  justify-content: flex-end;
}
.bubble-wrapper.bubble-assistant {
  justify-content: flex-start;
}

/* Ant Design X Bubble 内部样式微调 */
.bubble-wrapper .ant-bubble {
  max-width: 80%;
}

/* 自定义滚动条 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.sender-wrapper {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
}

.floating-trigger .floating-default-icon {
  font-size: 24px;
  color: white;
}

.floating-trigger .floating-icon-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.floating-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4d4f;
  color: white;
  border-radius: 12px;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
  border: 2px solid white;
}

.floating-pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(118, 75, 162, 0.5);
  animation: pulse 2s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% { transform: scale(0.95); opacity: 0.7; }
  70% { transform: scale(1.4); opacity: 0; }
  100% { transform: scale(0.95); opacity: 0; }
}

/* 移除所有 .dark 的手动样式 */
.ai-chat-content.dark .ant-bubble-content,
.ai-chat-content.dark .ant-input,
.ai-chat-component.dark,
.chat-dialog.dark,
.chat-dialog-header.dark,
.chat-dialog-title.dark,
.chat-dialog-close.dark:hover,
.floating-trigger.dark,
.ai-chat-content.dark .sender-wrapper {
  /* This block will be removed */
}

.prompts-container {
  padding: 0 16px 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  border-bottom: 1px solid var(--ant-color-border-secondary, #f0f0f0);
  margin-bottom: 8px;
}

.prompt-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.prompt-tag:hover {
  transform: translateY(-2px);
  opacity: 0.85;
}

.message-content .ant-bubble-content {
  padding: 8px 12px;
  border-radius: 8px;
  max-width: 85%;
  height: fit-content;
  font-size: 14px;
  line-height: 30px;
  text-align: left;
  white-space: pre-wrap;
  word-break: break-word;
}

.message-content[role="user"] .ant-bubble-content {
  background-color: skyblue;
}

.ai-chat-component.floating {
  position: fixed;
  align-items: flex-start;
} 