import { useState, useRef, useCallback, useEffect } from 'react'
import type { FloatingConfig } from '../types'

// 拖拽状态接口
interface DragFloatState {
  isDragging: boolean
  position: { x: number; y: number; side: 'left' | 'right' }
}

export const useDragFloat = (config: FloatingConfig) => {
  // 计算默认右下角位置
  const getDefaultPosition = useCallback(() => {
    const viewport = { width: window.innerWidth, height: window.innerHeight };
    const elementWidth = 60;
    const elementHeight = 60;
    const margin = 20;
    
    return {
      x: viewport.width - elementWidth - margin,
      y: viewport.height - elementHeight - margin - 100, // 留出一些空间避免被底部工具栏遮挡
      side: 'right' as 'left' | 'right'
    };
  }, []);

  const [isExpanded, setExpanded] = useState(false)
  const [state, setState] = useState<DragFloatState>(() => {
    const defaultPos = getDefaultPosition();
    return {
      isDragging: false,
      position: {
        x: config.position?.x ?? defaultPos.x,
        y: config.position?.y ?? defaultPos.y,
        side: config.position?.side ?? 'right'
      },
    };
  })

  const dragRef = useRef<HTMLDivElement>(null)
  const dragStartPos = useRef({ x: 0, y: 0 })
  const dragOffset = useRef({ x: 0, y: 0 }) // 记录鼠标在元素内的偏移
  const dragged = useRef(false)
  const lastClickTime = useRef(0)
  const clickDebounceDelay = 100 // 防抖延迟

  // 边界检测和吸附
  const snapToEdge = useCallback((x: number, y: number) => {
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    }
    
    const elementWidth = 60 // 悬浮图标宽度
    const elementHeight = 60 // 悬浮图标高度
    const margin = 20 // 边距
    
    // 限制在视窗内
    const clampedY = Math.max(
      margin, 
      Math.min(y, viewport.height - elementHeight - margin)
    )
    
    // 判断靠左还是靠右
    const centerX = viewport.width / 2
    const side: 'left' | 'right' = x < centerX ? 'left' : 'right'
    const finalX = side === 'left' ? margin : viewport.width - elementWidth - margin
    
    return {
      x: finalX,
      y: clampedY,
      side
    }
  }, [])

  // 拖拽开始
  const handleDragStart = useCallback((e: React.MouseEvent) => {
    if (!config.dragEnabled) return
    dragged.current = false
    dragStartPos.current = { x: e.clientX, y: e.clientY }

    const rect = dragRef.current?.getBoundingClientRect()
    if (rect) {
      dragOffset.current = { x: e.clientX - rect.left, y: e.clientY - rect.top }
    }

    const handleMouseMove = (event: MouseEvent) => {
      const dx = event.clientX - dragStartPos.current.x
      const dy = event.clientY - dragStartPos.current.y
      
      // If moved more than a threshold, it's a drag
      if (!dragged.current && (Math.abs(dx) > 5 || Math.abs(dy) > 5)) {
        dragged.current = true
        setState(prev => ({ ...prev, isDragging: true }))
      }
      
      if (dragged.current) {
        const newX = event.clientX - dragOffset.current.x
        const newY = event.clientY - dragOffset.current.y
        setState(prev => ({ ...prev, position: { ...prev.position, x: newX, y: newY } }))
      }
    }

    const handleMouseUp = () => {
      if (dragged.current) {
        setState(prev => {
          const finalPosition = snapToEdge(prev.position.x, prev.position.y)
          if (config.rememberPosition) {
            localStorage.setItem('chat-floating-position', JSON.stringify(finalPosition))
          }
          return { isDragging: false, position: finalPosition }
        })
      }
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [config.dragEnabled, config.rememberPosition, snapToEdge])
  
  const handleClick = useCallback((e?: React.MouseEvent) => {
    // 防抖处理，防止双重触发
    const now = Date.now()
    if (now - lastClickTime.current < clickDebounceDelay) {
      return
    }
    lastClickTime.current = now
    
    // 防止事件冒泡
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }
    
    // 只有在非拖拽状态下才处理点击
    if (!dragged.current) {
      setExpanded(prev => !prev)
    }
    
    // 延迟重置拖拽状态
    setTimeout(() => {
      dragged.current = false
    }, 10)
  }, [])

  // 加载保存的位置
  useEffect(() => {
    // 修复：当悬浮模式启用时，应该加载保存的位置
    if (config.rememberPosition && config.enabled) {
      const savedPosition = localStorage.getItem('chat-floating-position')
      if (savedPosition) {
        try {
          const parsed = JSON.parse(savedPosition)
          setState(prev => ({ ...prev, position: snapToEdge(parsed.x, parsed.y) }))
        } catch (e) {
          console.warn('Failed to parse saved position for React:', e);
          // 如果解析失败，使用默认位置
          setState(prev => ({ ...prev, position: getDefaultPosition() }));
        }
      } else {
        // 如果没有保存的位置，使用默认右下角
        setState(prev => ({ ...prev, position: getDefaultPosition() }));
      }
    }
  }, [config.rememberPosition, config.enabled, snapToEdge, getDefaultPosition])

  return {
    isExpanded,
    ...state,
    dragRef,
    handleDragStart,
    handleClick,
    setExpanded,
  }
} 