# @leyaoyao/ai-component-ui

## 多端产物用法说明

### Vue 端
```ts
import { VueAIChatComponent } from '@leyaoyao/ai-component-ui'
```

### React/Web 端
```ts
import { WebAIChatComponent } from '@leyaoyao/ai-component-ui'
```

### Taro 端
```ts
import { TaroAIChatComponent } from '@leyaoyao/ai-component-ui'
```

### 样式引入
```ts
import '@leyaoyao/ai-component-ui/dist/style/index.css'
```

## 导出结构说明
- 主入口导出所有端组件和工具函数
- 各端子路径支持按需引入
- 类型声明完整，支持 IDE 智能提示 