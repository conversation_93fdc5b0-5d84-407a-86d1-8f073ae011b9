{"name": "@leyaoyao/ai-component-ui", "version": "0.0.6", "description": "AI组件UI层 - 跨平台UI组件和样式", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "scripts": {"build": "node build/index.js", "clean": "<PERSON><PERSON><PERSON> dist", "test": "echo \"Warning: no tests specified\" && exit 0", "dev": "node build/index.js && echo 'Build completed for development'", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build:types": "node build/build-types.js"}, "keywords": ["ai", "chat", "ui", "component", "react", "vue", "taro", "typescript"], "author": "", "license": "MIT", "dependencies": {"@leyaoyao/ai-component-adapters": "^0.0.6", "@leyaoyao/ai-component-config": "^0.0.6", "@leyaoyao/ai-component-core": "^0.0.6"}, "peerDependencies": {"@ant-design/icons": ">=5.0.0", "@ant-design/icons-vue": ">=7.0.0", "@ant-design/x": ">=1.0.0", "@nutui/nutui-react-taro": "^3.0.0", "@tarojs/components": ">=3.0.0", "@tarojs/taro": ">=3.0.0", "ant-design-vue": ">=4.0.0", "antd": ">=5.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0", "vue": ">=3.0.0"}, "peerDependenciesMeta": {"vue": {"optional": true}, "react": {"optional": true}, "react-dom": {"optional": true}, "@tarojs/taro": {"optional": true}, "@tarojs/components": {"optional": true}, "@nutui/nutui-react-taro": {"optional": true}}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "@storybook/react": "^7.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-vue": "^5.2.4", "jest": "^29.0.0", "rimraf": "^5.0.0", "rollup": "^4.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "sass-embedded": "^1.89.2", "typescript": "^5.0.0", "vite": "^6.3.5", "vue": "^3.4.0", "vue-tsc": "^2.0.0", "@vue/typescript-plugin": "^2.0.0"}, "files": ["dist/", "web/", "vue/", "taro/", "native/", "shared/", "index.ts"], "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.cjs.js", "types": "./dist/index.d.ts"}, "./web": {"import": "./dist/web/index.esm.js", "require": "./dist/web/index.cjs.js", "types": "./dist/web/index.d.ts"}, "./vue": {"import": "./dist/vue/index.esm.js", "require": "./dist/vue/index.cjs.js", "types": "./dist/vue/index.d.ts"}, "./taro": {"import": "./dist/taro/index.esm.js", "require": "./dist/taro/index.cjs.js", "types": "./dist/taro/index.d.ts"}, "./style": {"import": "./dist/style/index.css", "require": "./dist/style/index.css"}, "./shared": "./shared/index.ts", "./native": "./native/index.ts"}}