/// <reference path="../taro-env.d.ts" />
import React, { useEffect, useState, useRef, useMemo } from 'react'
import './MarkdownContent.scss'

// 环境检测工具
const detectTaroEnvironment = () => {
  if (typeof process !== 'undefined' && process.env.TARO_ENV) {
    return true
  }
  if (typeof window !== 'undefined' && ((window as any).__taroAppConfig || (window as any).Taro)) {
    return true
  }
  try {
    require.resolve('@tarojs/taro')
    return true
  } catch {
    return false
  }
}

const IS_TARO_ENV = detectTaroEnvironment()

// 条件导入 Taro 组件
let TaroComponents: any = {}
let TaroAPI: any = {}

if (IS_TARO_ENV) {
  try {
    TaroComponents = require('@tarojs/components')
    TaroAPI = require('@tarojs/taro')
  } catch (error) {
    console.warn('Taro 组件加载失败:', error)
  }
}

const { View = 'div', Text = 'span', Image = 'img', Button = 'button' } = TaroComponents
const Taro = TaroAPI.default || TaroAPI || {
  showToast: (options: any) => console.warn('Taro.showToast 仅支持 Taro 环境:', options?.title),
  setClipboardData: (options: any) => {
    if (navigator.clipboard) {
      return navigator.clipboard.writeText(options.data)
    }
    return Promise.reject(new Error('仅支持 Taro 环境'))
  }
}

interface MarkdownContentProps {
  content: string
  isRequesting?: boolean
  enableCodeCopy?: boolean
  className?: string
}

// 简化的 Markdown 解析器（适配小程序环境）
class SimpleMarkdownParser {
  parse(text: string, isRequesting: boolean = false) {
    const lines = text.split('\n')
    const elements: any[] = []
    let currentCodeBlock: string[] | null = null
    let currentCodeLang = ''
    let inList = false
    let listItems: string[] = []

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]

      // 代码块
      if (line.startsWith('```')) {
        if (currentCodeBlock === null) {
          currentCodeLang = line.slice(3).trim()
          currentCodeBlock = []
        } else {
          elements.push({
            type: 'code',
            lang: currentCodeLang,
            content: currentCodeBlock.join('\n'),
            key: `code-${i}`
          })
          currentCodeBlock = null
          currentCodeLang = ''
        }
        continue
      }

      if (currentCodeBlock !== null) {
        currentCodeBlock.push(line)
        continue
      }

      // 标题
      const headingMatch = line.match(/^(#{1,6})\s+(.+)/)
      if (headingMatch) {
        if (inList && listItems.length > 0) {
          elements.push({ type: 'list', items: listItems, key: `list-${i}` })
          listItems = []
          inList = false
        }
        elements.push({
          type: `h${headingMatch[1].length}`,
          content: headingMatch[2],
          key: `heading-${i}`
        })
        continue
      }

      // 列表项
      const listMatch = line.match(/^[-*+]\s+(.+)/)
      if (listMatch) {
        inList = true
        listItems.push(listMatch[1])
        continue
      } else if (inList && line.trim() === '') {
        elements.push({ type: 'list', items: listItems, key: `list-${i}` })
        listItems = []
        inList = false
        continue
      }

      // 粗体
      const boldProcessed = line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      
      // 斜体
      const italicProcessed = boldProcessed.replace(/\*(.*?)\*/g, '<em>$1</em>')
      
      // 行内代码
      const codeProcessed = italicProcessed.replace(/`([^`]+)`/g, '<code>$1</code>')

      // 链接
      const linkProcessed = codeProcessed.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')

      // 普通段落
      if (line.trim()) {
        if (inList && listItems.length > 0) {
          elements.push({ type: 'list', items: listItems, key: `list-${i}` })
          listItems = []
          inList = false
        }
        elements.push({
          type: 'paragraph',
          content: linkProcessed,
          key: `para-${i}`
        })
      }
    }

    // 处理未结束的列表
    if (inList && listItems.length > 0) {
      elements.push({ type: 'list', items: listItems, key: `list-end` })
    }

    // 处理未结束的代码块
    if (currentCodeBlock !== null && !isRequesting) {
      elements.push({
        type: 'code',
        lang: currentCodeLang,
        content: currentCodeBlock.join('\n'),
        key: `code-end`
      })
    }

    return elements
  }
}

const parser = new SimpleMarkdownParser()

export const TaroMarkdownContent: React.FC<MarkdownContentProps> = ({
  content,
  isRequesting = false,
  enableCodeCopy = true,
  className = ''
}) => {
  const [copiedId, setCopiedId] = useState<string | null>(null)
  const parsedElements = useMemo(() => parser.parse(content, isRequesting), [content, isRequesting])

  const handleCopyCode = async (code: string, id: string) => {
    if (!enableCodeCopy) return

    try {
      await Taro.setClipboardData({ data: code })
      setCopiedId(id)
      Taro.showToast({ title: '代码已复制', icon: 'success' })
      setTimeout(() => setCopiedId(null), 2000)
    } catch (error) {
      Taro.showToast({ title: '复制失败', icon: 'error' })
    }
  }

  const renderElement = (element: any) => {
    switch (element.type) {
      case 'h1':
      case 'h2':
      case 'h3':
      case 'h4':
      case 'h5':
      case 'h6':
        return (
          <View key={element.key} className={`markdown-${element.type}`}>
            <Text>{element.content}</Text>
          </View>
        )

      case 'paragraph':
        return (
          <View key={element.key} className="markdown-paragraph">
            <Text dangerouslySetInnerHTML={{ __html: element.content }} />
          </View>
        )

      case 'list':
        return (
          <View key={element.key} className="markdown-list">
            {element.items.map((item: string, index: number) => (
              <View key={`${element.key}-item-${index}`} className="markdown-list-item">
                <Text className="list-marker">• </Text>
                <Text dangerouslySetInnerHTML={{ __html: item }} />
              </View>
            ))}
          </View>
        )

      case 'code':
        const isCopied = copiedId === element.key
        return (
          <View key={element.key} className="markdown-code-block">
            <View className="code-header">
              <Text className="code-lang">{element.lang || 'plaintext'}</Text>
              {enableCodeCopy && (
                <Button
                  className="copy-button"
                  onClick={() => handleCopyCode(element.content, element.key)}
                >
                  <Text>{isCopied ? '✓ 已复制' : '复制'}</Text>
                </Button>
              )}
            </View>
            <View className="code-content">
              <Text className="code-text">{element.content}</Text>
            </View>
          </View>
        )

      default:
        return null
    }
  }

  return (
    <View className={`taro-markdown-content ${className}`}>
      {parsedElements.map(element => renderElement(element))}
    </View>
  )
}

export default TaroMarkdownContent