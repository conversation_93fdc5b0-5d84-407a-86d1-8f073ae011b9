.taro-markdown-content {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;

  // 标题样式
  .markdown-h1,
  .markdown-h2,
  .markdown-h3,
  .markdown-h4,
  .markdown-h5,
  .markdown-h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
  }

  .markdown-h1 {
    font-size: 28px;
  }

  .markdown-h2 {
    font-size: 24px;
  }

  .markdown-h3 {
    font-size: 20px;
  }

  .markdown-h4 {
    font-size: 16px;
  }

  .markdown-h5 {
    font-size: 14px;
  }

  .markdown-h6 {
    font-size: 12px;
  }

  // 段落样式
  .markdown-paragraph {
    margin-top: 0;
    margin-bottom: 16px;
    
    // 行内样式
    :global {
      strong {
        font-weight: 600;
      }

      em {
        font-style: italic;
      }

      code {
        padding: 2px 4px;
        margin: 0 2px;
        font-size: 85%;
        background-color: rgba(27, 31, 35, 0.05);
        border-radius: 3px;
        font-family: Con<PERSON>as, Monaco, 'Courier New', monospace;
      }

      a {
        color: #0969da;
        text-decoration: none;
      }
    }
  }

  // 列表样式
  .markdown-list {
    padding-left: 0;
    margin-top: 0;
    margin-bottom: 16px;
  }

  .markdown-list-item {
    display: flex;
    margin-bottom: 8px;

    .list-marker {
      margin-right: 8px;
      flex-shrink: 0;
    }
  }

  // 代码块样式
  .markdown-code-block {
    margin-bottom: 16px;
    border-radius: 6px;
    overflow: hidden;
    background-color: #f6f8fa;
    border: 1px solid #e1e4e8;

    .code-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background-color: #f0f2f5;
      border-bottom: 1px solid #e1e4e8;

      .code-lang {
        font-size: 12px;
        color: #666;
        font-family: Consolas, Monaco, 'Courier New', monospace;
      }

      .copy-button {
        padding: 4px 8px;
        font-size: 12px;
        color: #666;
        background: #fff;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;

        &:active {
          background: #f3f4f6;
        }
      }
    }

    .code-content {
      padding: 16px;
      overflow-x: auto;

      .code-text {
        font-size: 13px;
        line-height: 1.45;
        font-family: Consolas, Monaco, 'Courier New', monospace;
        white-space: pre;
      }
    }
  }

  // 暗色模式支持
  &.dark {
    color: #e0e0e0;

    .markdown-code-block {
      background-color: #2f2f2f;
      border-color: #434343;

      .code-header {
        background-color: #262626;
        border-bottom-color: #434343;

        .code-lang {
          color: #aaa;
        }

        .copy-button {
          color: #e0e0e0;
          background: #434343;
          border-color: #666;
        }
      }
    }

    .markdown-paragraph {
      :global {
        code {
          background-color: rgba(255, 255, 255, 0.1);
          color: #f8f8f2;
        }

        a {
          color: #69b7ff;
        }
      }
    }
  }
}

// 在小程序环境中，某些样式可能需要调整
@supports (display: -webkit-box) {
  .taro-markdown-content {
    .markdown-paragraph {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}