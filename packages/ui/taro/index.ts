// Taro专用导出入口
export { 
  TaroAIChatComponent,
  default
} from './chat-component'

export { default as TaroMarkdownContent } from './components/MarkdownContent'

export type { TaroAIChatComponentProps } from './chat-component'
export type { UploadConfig, UploadResponse } from './utils/upload'

// 重新导出一些有用的工具
export const isTaroEnvironment = () => {
  return typeof process !== 'undefined' && !!process.env.TARO_ENV
} 