// 文件上传配置接口
export interface UploadConfig {
  url: string;                              // 上传地址
  method?: 'POST' | 'PUT' | 'PATCH';       // 请求方法，默认 POST
  headers?: Record<string, string>;         // 自定义请求头
  withCredentials?: boolean;                // 是否携带凭证
  data?: Record<string, any>;              // 额外的表单数据或JSON数据
  fieldName?: string;                       // 文件字段名，默认 'file'
  timeout?: number;                         // 超时时间（毫秒）
  responseParser?: (response: any) => {     // 响应解析函数
    url: string;
    name?: string;
    [key: string]: any;
  };
  onProgress?: (percent: number, file: File) => void;   // 上传进度回调
  validateStatus?: (status: number) => boolean; // 验证响应状态
  requestType?: 'form-data' | 'json';      // 请求类型，默认 form-data
}

// 文件上传响应
export interface UploadResponse {
  url: string;
  name?: string;
  [key: string]: any;
}

// 环境检测
const detectTaroEnvironment = () => {
  if (typeof process !== 'undefined' && process.env.TARO_ENV) {
    return true;
  }
  if (typeof window !== 'undefined') {
    if ((window as any).__taroAppConfig || (window as any).Taro) {
      return true;
    }
  }
  try {
    require.resolve('@tarojs/taro');
    return true;
  } catch {
    return false;
  }
};

const IS_TARO_ENV = detectTaroEnvironment();

// 条件导入 Taro
let TaroAPI: any = {};
if (IS_TARO_ENV) {
  try {
    TaroAPI = require('@tarojs/taro');
  } catch (error) {
    console.warn('Taro API 加载失败:', error);
  }
}

const Taro = TaroAPI.default || TaroAPI || {
  uploadFile: () => Promise.reject(new Error('仅支持 Taro 环境')),
  request: () => Promise.reject(new Error('仅支持 Taro 环境')),
  getFileSystemManager: () => null,
  showToast: (options: any) => console.warn('Taro.showToast 仅支持 Taro 环境:', options?.title),
};

/**
 * 默认的响应解析函数
 */
const defaultResponseParser = (response: any): UploadResponse => {
  // 尝试常见的响应格式
  if (response.data?.url) {
    return { url: response.data.url, ...response.data };
  }
  if (response.url) {
    return { url: response.url, ...response };
  }
  if (response.data?.data?.url) {
    return { url: response.data.data.url, ...response.data.data };
  }
  if (typeof response === 'string') {
    return { url: response };
  }
  
  throw new Error('无法解析上传响应，请提供 responseParser 函数');
};

/**
 * 默认的状态验证函数
 */
const defaultValidateStatus = (status: number): boolean => {
  return status >= 200 && status < 300;
};

/**
 * 将文件转换为base64
 */
const fileToBase64 = (tempFilePath: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (IS_TARO_ENV && Taro.getFileSystemManager) {
      try {
        const fs = Taro.getFileSystemManager();
        const base64 = fs.readFileSync(tempFilePath, 'base64');
        // 根据文件扩展名确定MIME类型
        const ext = tempFilePath.split('.').pop()?.toLowerCase();
        const mimeType = ext === 'png' ? 'image/png' : 
                        ext === 'jpg' || ext === 'jpeg' ? 'image/jpeg' : 
                        ext === 'webp' ? 'image/webp' : 'image/jpeg';
        resolve(`data:${mimeType};base64,${base64}`);
      } catch (error) {
        reject(error);
      }
    } else {
      reject(new Error('仅支持 Taro 环境'));
    }
  });
};

/**
 * 使用 Taro.uploadFile 执行文件上传
 */
export const uploadFileWithConfig = async (
  filePath: string,
  config: UploadConfig,
  onError?: (error: string) => void
): Promise<string | UploadResponse> => {
  if (!IS_TARO_ENV) {
    throw new Error('仅支持 Taro 环境');
  }

  const {
    url,
    method = 'POST',
    headers = {},
    data = {},
    fieldName = 'file',
    timeout = 60000,
    responseParser = defaultResponseParser,
    onProgress,
    validateStatus = defaultValidateStatus,
    requestType = 'form-data'
  } = config;

  try {
    if (requestType === 'json') {
      // JSON格式上传：先转换文件为base64，然后通过request发送
      const base64Content = await fileToBase64(filePath);
      const fileName = filePath.split('/').pop() || 'file';
      
      const jsonData = {
        ...data,
        [fieldName]: {
          name: fileName,
          content: base64Content
        }
      };

      const response = await Taro.request({
        url,
        method: method as any,
        data: jsonData,
        header: {
          'Content-Type': 'application/json',
          ...headers
        },
        timeout
      });

      if (validateStatus(response.statusCode)) {
        const parsed = responseParser(response.data);
        return parsed;
      } else {
        throw new Error(`上传失败: ${response.statusCode}`);
      }
    } else {
      // FormData格式上传：使用Taro.uploadFile
      const uploadTask = Taro.uploadFile({
        url,
        filePath,
        name: fieldName,
        formData: data,
        header: headers,
        timeout,
        success: (res: any) => {
          // 成功回调在promise中处理
        },
        fail: (err: any) => {
          // 失败回调在promise中处理
        }
      });

      // 监听上传进度
      if (onProgress) {
        uploadTask.progress((res: any) => {
          const percent = res.progress;
          // 创建一个模拟的File对象用于回调
          const mockFile = { name: filePath.split('/').pop() || 'file' } as File;
          onProgress(percent, mockFile);
        });
      }

      // 包装为Promise
      return new Promise((resolve, reject) => {
        uploadTask.then((res: any) => {
          if (validateStatus(res.statusCode)) {
            try {
              let responseData = res.data;
              try {
                responseData = JSON.parse(res.data);
              } catch (e) {
                // 如果不是JSON，保持原始文本
              }
              
              const parsed = responseParser(responseData);
              resolve(parsed);
            } catch (error) {
              reject(new Error(`解析响应失败: ${(error as Error).message}`));
            }
          } else {
            reject(new Error(`上传失败: ${res.statusCode}`));
          }
        }).catch((err: any) => {
          reject(new Error(`网络错误: ${err.errMsg || '未知错误'}`));
        });
      });
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传失败';
    onError?.(errorMessage);
    throw error;
  }
};

/**
 * 创建默认的上传函数
 */
export const createDefaultUploadFunction = (
  config: UploadConfig,
  onError?: (error: string) => void
) => {
  return (filePath: string) => uploadFileWithConfig(filePath, config, onError);
};