/// <reference path="./taro-env.d.ts" />
import React, { useEffect, useRef, useState } from 'react'
import './chat-component.scss'
import TaroMarkdownContent from './components/MarkdownContent'
import { uploadFileWithConfig, type UploadConfig, type UploadResponse } from './utils/upload'

// 环境检测工具
const detectTaroEnvironment = () => {
  // 检测多种 Taro 环境标识
  if (typeof process !== 'undefined') {
    // Node.js 环境下检测 TARO_ENV
    if (process.env.TARO_ENV) {
      return true
    }
  }
  
  // 浏览器环境下检测全局变量
  if (typeof window !== 'undefined') {
    // 检测 Taro 运行时全局变量
    if ((window as any).__taroAppConfig || (window as any).Taro) {
      return true
    }
  }
  
  // 检测 Taro 模块是否可用
  try {
    require.resolve('@tarojs/taro')
    return true
  } catch {
    return false
  }
}

// 环境变量
const IS_TARO_ENV = detectTaroEnvironment()

// 条件导入 Taro 组件和 API
let TaroComponents: any = {}
let TaroAPI: any = {}

if (IS_TARO_ENV) {
  try {
    // 动态导入 Taro 组件
    TaroComponents = require('@tarojs/components')
    TaroAPI = require('@tarojs/taro')
  } catch (error) {
    console.warn('Taro 组件加载失败:', error)
  }
}

// 组件别名，支持条件渲染
const { 
  View = 'div', 
  ScrollView = 'div', 
  Textarea = 'textarea', 
  Button = 'button', 
  Text = 'span', 
  Image = 'img' 
} = TaroComponents

// Taro API 别名和兜底实现
const Taro = TaroAPI.default || TaroAPI || {
  // 兜底 API，避免在非 Taro 环境下报错
  chooseImage: (options: any) => {
    console.warn('Taro.chooseImage 仅支持 Taro 环境')
    return Promise.reject(new Error('仅支持 Taro 环境'))
  },
  showToast: (options: any) => {
    console.warn('Taro.showToast 仅支持 Taro 环境:', options?.title)
  },
  nextTick: (fn: () => void) => {
    // 使用标准的异步调度
    if (typeof requestAnimationFrame !== 'undefined') {
      requestAnimationFrame(() => fn())
    } else {
      setTimeout(fn, 16) // 约 60fps
    }
  },
  // 其他可能用到的 Taro API 兜底
  getSystemInfo: () => Promise.resolve({
    platform: 'web',
    system: navigator?.userAgent || 'unknown'
  }),
  navigateTo: () => console.warn('Taro.navigateTo 仅支持 Taro 环境'),
  redirectTo: () => console.warn('Taro.redirectTo 仅支持 Taro 环境'),
  getFileSystemManager: null, // 文件系统管理器仅在小程序环境可用
}

// 文件类型定义
interface FileItem {
  uid: string
  name: string
  url: string
  size: number
  type: string
  tempFilePath?: string
}

// 提示词类型定义
interface PromptItem {
  key: string
  label?: string
  content?: string
  show?: boolean
  [key: string]: any
}

// 组件配置类型
interface ComponentConfig {
  showAvatar?: boolean
  placeholder?: string
  maxInputLength?: number
  enableAutoScroll?: boolean
  timeout?: number
  style?: React.CSSProperties
}

// 消息类型
interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp?: number
  images?: Array<{
    url: string
    name: string
  }>
}

// 自定义渲染函数参数类型
interface TaroCustomRenderProps {
  messages: Message[]
  isLoading: boolean
  canSend?: boolean
  inputValue?: string
  fileList?: FileItem[]
  onUploadClick?: () => void
  onClear?: () => void
  onSend?: () => void
  disabled?: boolean
  formatTime?: (timestamp?: number) => string
}

export interface TaroAIChatComponentProps {
  config?: ComponentConfig
  prompts?: PromptItem[]
  enableFileUpload?: boolean
  maxFiles?: number
  maxFileSize?: number
  placeholder?: string
  disabled?: boolean
  darkMode?: boolean
  onSend?: (message: string, files?: FileItem[]) => void
  onReceive?: (message: string) => void
  onError?: (error: string) => void
  onClear?: () => void
  className?: string
  style?: React.CSSProperties
  
  // 文件上传相关
  uploadFile?: (filePath: string) => Promise<string | UploadResponse>
  uploadConfig?: UploadConfig
  
  // 自定义渲染函数
  renderHeader?: (props: TaroCustomRenderProps) => React.ReactNode
  renderEmpty?: (props: TaroCustomRenderProps) => React.ReactNode
  renderMessage?: (message: Message, props: TaroCustomRenderProps) => React.ReactNode
  renderMessageActions?: (message: Message, props: TaroCustomRenderProps) => React.ReactNode
  renderLoading?: (props: TaroCustomRenderProps) => React.ReactNode
  renderInputTools?: (props: TaroCustomRenderProps) => React.ReactNode
  renderFooter?: (props: TaroCustomRenderProps) => React.ReactNode
}

// 常量定义
const TARO_CONSTANTS = {
  MAX_IMAGES: 9,
  MAX_SIZE_MB: 3,
  ACCEPT: ['jpg', 'jpeg', 'png', 'webp'],
}

// Taro API 响应类型
interface TaroChooseImageResponse {
  tempFilePaths: string[]
  tempFiles: Array<{
    path: string
    size: number
  }>
}

// 模拟聊天Hook
const useTaroChat = (config: ComponentConfig) => {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentChatId] = useState<string>('')

  const sendMessage = async (content: string, files?: FileItem[]) => {
    setIsLoading(true)
    setError(null)
    
    try {
      // 添加用户消息（包含图片）
      const userMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content,
        timestamp: Date.now(),
        images: files && files.length > 0 ? files.map(f => ({ url: f.url, name: f.name })) : undefined
      }
      setMessages(prev => [...prev, userMessage])
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 添加AI回复
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `AI回复: ${content}${files && files.length > 0 ? ` 我看到您发送了 ${files.length} 张图片。` : ''}`,
        timestamp: Date.now()
      }
      setMessages(prev => [...prev, aiMessage])
    } catch (err) {
      setError(err instanceof Error ? err.message : '发送失败')
    } finally {
      setIsLoading(false)
    }
  }

  const clearMessages = () => {
    setMessages([])
    setError(null)
  }

  const abortRequest = () => {
    setIsLoading(false)
  }

  const loadMessages = () => {
    // 加载历史消息的逻辑
  }

  return {
    messages,
    isLoading,
    error,
    currentChatId,
    sendMessage,
    clearMessages,
    abortRequest,
    loadMessages,
  }
}

// 非 Taro 环境的兜底组件
const NonTaroFallback: React.FC<{ message?: string }> = ({ 
  message = '此组件仅支持 Taro 环境（微信小程序、支付宝小程序、H5等）' 
}) => {
  return React.createElement('div', {
    style: {
      padding: '20px',
      textAlign: 'center',
      backgroundColor: '#f5f5f5',
      border: '1px dashed #d9d9d9',
      borderRadius: '8px',
      color: '#666',
      fontSize: '14px',
      lineHeight: '1.5'
    }
  }, [
    React.createElement('div', { 
      key: 'icon',
      style: { fontSize: '24px', marginBottom: '8px' } 
    }, '⚠️'),
    React.createElement('div', { key: 'message' }, message),
    React.createElement('div', { 
      key: 'env-info',
      style: { marginTop: '8px', fontSize: '12px', color: '#999' } 
    }, `当前环境: ${typeof window !== 'undefined' ? 'Browser' : 'Node.js'}`)
  ])
}

/**
 * TaroJS平台AI聊天组件
 * 支持微信小程序、支付宝小程序、H5、React Native等多端
 * 基于@ant-design/x风格设计，提供统一的用户体验
 */
export const TaroAIChatComponent: React.FC<TaroAIChatComponentProps> = ({
  config = {},
  prompts = [],
  enableFileUpload = true,
  maxFiles = 9,
  maxFileSize = 3,
  placeholder = '输入您想要提问的问题并点击发送',
  disabled = false,
  darkMode = false,
  onSend,
  onReceive,
  onError,
  onClear,
  className = '',
  style,
  uploadFile,
  uploadConfig,
  // 自定义渲染函数
  renderHeader,
  renderEmpty,
  renderMessage,
  renderMessageActions,
  renderLoading,
  renderInputTools,
  renderFooter,
}) => {
  // 环境检测：非 Taro 环境直接返回兜底组件
  if (!IS_TARO_ENV) {
    return React.createElement(NonTaroFallback, {
      message: '当前组件需要在 Taro 环境中运行，请在小程序或 Taro H5 项目中使用'
    })
  }
  // 合并配置
  const finalConfig: ComponentConfig = {
    showAvatar: true,
    placeholder,
    maxInputLength: 1000,
    enableAutoScroll: true,
    timeout: 30000,
    style: {},
    ...config
  }

  // 使用聊天Hook
  const {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages: clearMessagesHook,
    abortRequest,
    currentChatId,
    loadMessages,
  } = useTaroChat(finalConfig)

  // 本地状态
  const [inputValue, setInputValue] = useState('')
  const [fileList, setFileList] = useState<FileItem[]>([])
  const [uploading, setUploading] = useState(false)
  const scrollViewRef = useRef<any>()

  // 计算属性
  const showMenu = prompts.filter(item => item.show)
  const canSend = (inputValue.trim().length > 0 || fileList.length > 0) && !disabled && !isLoading && !uploading

  // 加载历史消息
  useEffect(() => {
    loadMessages()
  }, [])

  // 校验文件
  const validateFiles = (files: any[]) => {
    const valid: any[] = []
    let errorMsg = ''
    
    if (fileList.length + files.length > maxFiles) {
      errorMsg = `最多只能上传${maxFiles}张图片`
    }
    
    files.forEach(file => {
      const isAllowed = TARO_CONSTANTS.ACCEPT.some(type => 
        file.path?.toLowerCase().includes(`.${type}`)
      )
      if (!isAllowed) {
        errorMsg = '仅支持 jpg/png/webp 格式图片'
      }
      if (file.size && file.size / 1024 / 1024 > maxFileSize) {
        errorMsg = `单张图片不能超过${maxFileSize}MB`
      }
      if (!errorMsg) {
        valid.push(file)
      }
    })
    
    return { valid, errorMsg }
  }

  // 将图片路径转换为base64 dataUrl
  const convertToDataUrl = async (tempFilePath: string): Promise<string> => {
    // 在小程序环境中，可以使用getFileSystemManager读取文件
    if (IS_TARO_ENV && Taro.getFileSystemManager) {
      try {
        const fs = Taro.getFileSystemManager()
        const base64 = fs.readFileSync(tempFilePath, 'base64')
        // 根据文件扩展名确定MIME类型
        const ext = tempFilePath.split('.').pop()?.toLowerCase()
        const mimeType = ext === 'png' ? 'image/png' : 
                        ext === 'jpg' || ext === 'jpeg' ? 'image/jpeg' : 
                        ext === 'webp' ? 'image/webp' : 'image/jpeg'
        return `data:${mimeType};base64,${base64}`
      } catch (error) {
        console.warn('转换base64失败，使用原路径:', error)
        return tempFilePath
      }
    }
    // 如果不是小程序环境或转换失败，直接返回原路径
    return tempFilePath
  }

  // 处理文件上传
  const handleChooseImage = async () => {
    if (disabled || isLoading) return
    
    Taro.chooseImage({
      count: maxFiles - fileList.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res: TaroChooseImageResponse) => {
        const { valid, errorMsg } = validateFiles(res.tempFiles || [])
        if (errorMsg) {
          Taro.showToast({ title: errorMsg, icon: 'error' })
          return
        }
        
        // 转换图片为dataUrl
        const newFiles: FileItem[] = []
        for (const file of valid) {
          const dataUrl = await convertToDataUrl(file.path)
          newFiles.push({
            uid: `${Date.now()}_${Math.random()}`,
            name: file.path?.split('/').pop() || 'image',
            url: dataUrl, // 使用dataUrl而不是临时路径
            size: file.size || 0,
            type: 'image',
            tempFilePath: file.path,
          })
        }
        
        setFileList(prev => [...prev, ...newFiles])
      },
      fail: (err: any) => {
        console.error('选择图片失败:', err)
      }
    })
  }

  // 删除文件
  const handleRemoveFile = (uid: string) => {
    setFileList(prev => prev.filter(f => f.uid !== uid))
  }

  // 处理发送消息
  const handleSend = async () => {
    const content = inputValue.trim()
    if (!content && fileList.length === 0) return

    let processedFiles: FileItem[] = []
    
    // 处理文件上传
    if (fileList.length > 0) {
      if (uploadFile || uploadConfig) {
        // 如果提供了上传方法或上传配置，执行上传
        setUploading(true)
        try {
          const uploaded = await Promise.all(fileList.map(async (file) => {
            let res: string | UploadResponse
            
            if (uploadFile) {
              // 优先使用自定义上传函数
              res = await uploadFile(file.tempFilePath || file.url)
            } else if (uploadConfig) {
              // 使用上传配置
              res = await uploadFileWithConfig(
                file.tempFilePath || file.url, 
                uploadConfig,
                onError
              )
            } else {
              throw new Error('未配置上传方式')
            }
            
            const url = typeof res === 'string' ? res : res.url
            return {
              uid: file.uid,
              name: file.name,
              url,
              size: file.size,
              type: file.type,
              tempFilePath: file.tempFilePath,
              // 保留额外的响应数据
              ...(typeof res === 'object' ? res : {})
            }
          }))
          processedFiles = uploaded
        } catch (e: any) {
          const errorMsg = e?.message || '文件上传失败'
          Taro.showToast({ title: errorMsg, icon: 'error' })
          onError?.(errorMsg)
          setUploading(false)
          return
        }
        setUploading(false)
      } else {
        // 如果没有上传方法或配置，直接使用本地文件
        processedFiles = [...fileList]
      }
    }

    onSend?.(content, processedFiles)
    await sendMessage(content, processedFiles)
    setInputValue('')
    setFileList([])
    
    // 滚动到底部
    scrollToBottom()
  }

  // 滚动到底部
  const scrollToBottom = () => {
    if (scrollViewRef.current) {
      // Taro自动滚动到底部
      Taro.nextTick(() => {
        scrollViewRef.current?.scrollToEnd?.()
      })
    }
  }

  // 监听消息变化
  useEffect(() => {
    const lastMessage = messages[messages.length - 1]
    if (lastMessage && lastMessage.role === 'assistant') {
      onReceive?.(lastMessage.content)
    }
    
    // 自动滚动
    if (finalConfig.enableAutoScroll) {
      scrollToBottom()
    }
  }, [messages, onReceive, finalConfig.enableAutoScroll])

  // 监听错误
  useEffect(() => {
    if (error) {
      onError?.(error)
      // 显示Taro原生错误提示
      Taro.showToast({
        title: error,
        icon: 'error',
        duration: 2000,
      })
    }
  }, [error, onError])

  // 格式化时间
  const formatTime = (timestamp?: number) => {
    if (!timestamp) return ''
    return new Date(timestamp).toLocaleTimeString()
  }

  // 清空消息
  const handleClear = () => {
    clearMessagesHook()
    onClear?.()
  }

  // 构建自定义渲染属性
  const customRenderProps: TaroCustomRenderProps = {
    messages,
    isLoading,
    canSend,
    inputValue,
    fileList,
    onUploadClick: handleChooseImage,
    onClear: handleClear,
    onSend: handleSend,
    disabled: disabled || isLoading,
    formatTime,
  }

  // 处理输入变化
  const handleInputChange = (e: any) => {
    setInputValue(e.detail.value)
  }

  // 处理键盘确认
  const handleConfirm = () => {
    handleSend()
  }

  // 处理提示词点击
  const handlePromptClick = (prompt: PromptItem) => {
    if (prompt.content) {
      setInputValue(prompt.content)
    }
  }

  return (
    <View 
      className={`taro-ai-chat-component ${className} ${darkMode ? 'dark' : ''}`} 
      style={{ ...finalConfig.style, ...style }}
    >
      {/* 自定义头部插槽 */}
      {renderHeader && (
        <View className="chat-header-slot">
          {renderHeader(customRenderProps)}
        </View>
      )}
      
      {/* 消息列表 */}
      <ScrollView
        ref={scrollViewRef}
        className="messages-container"
        scrollY
        scrollIntoView={`message-${messages.length - 1}`}
        enhanced
        showScrollbar={false}
      >
        {/* 自定义空状态插槽 */}
        {messages.length === 0 && !isLoading && (
          <View className="empty-state-slot">
            {renderEmpty ? renderEmpty(customRenderProps) : (
              <View className="default-empty-state">
                <View className="empty-icon">💬</View>
                <Text className="empty-text">开始与 AI 助手对话吧</Text>
              </View>
            )}
          </View>
        )}
        
        {messages.map((message, index) => (
          <View 
            key={message.id} 
            id={`message-${index}`}
            className={`message-bubble message-${message.role}`}
          >
            {finalConfig.showAvatar && (
              <View className="message-avatar">
                <Text>{message.role === 'user' ? '👤' : '🤖'}</Text>
              </View>
            )}
            
            {/* 自定义消息内容渲染 */}
            {renderMessage ? renderMessage(message, customRenderProps) : (
              <View className="message-content">
                {/* 图片显示 */}
                {message.images && message.images.length > 0 && (
                  <View className="message-images">
                    {message.images.map((image, imgIndex) => (
                      <View key={imgIndex} className="message-image-item">
                        <Image 
                          src={image.url} 
                          className="message-image"
                          mode="aspectFill"
                        />
                      </View>
                    ))}
                  </View>
                )}
                {/* 文本内容 */}
                {message.content && (
                  message.role === 'assistant' ? (
                    <TaroMarkdownContent 
                      content={message.content}
                      isRequesting={false}
                      enableCodeCopy={true}
                      className={darkMode ? 'dark' : ''}
                    />
                  ) : (
                    <Text>{message.content}</Text>
                  )
                )}
                <View className="message-timestamp">
                  <Text className="timestamp-text">
                    {formatTime(message.timestamp)}
                  </Text>
                </View>
                
                {/* 自定义消息操作插槽 */}
                {renderMessageActions && (
                  <View className="message-actions-slot">
                    {renderMessageActions(message, customRenderProps)}
                  </View>
                )}
              </View>
            )}
          </View>
        ))}
        
        {/* 自定义加载状态 */}
        {isLoading && (
          <View className="loading-state-slot">
            {renderLoading ? renderLoading(customRenderProps) : (
              <View className="default-loading-state">
                <Text>AI正在思考中...</Text>
              </View>
            )}
          </View>
        )}
      </ScrollView>

      {/* 发送区域 */}
      <View className="sender-container">
        {/* 文件预览 */}
        {fileList.length > 0 && enableFileUpload && (
          <View className="file-preview-container">
            {fileList.map((file) => (
              <View key={file.uid} className="file-preview-item">
                <Image 
                  src={file.url} 
                  className="file-preview-image"
                  mode="aspectFill"
                />
                <View 
                  className="file-remove-btn"
                  onClick={() => handleRemoveFile(file.uid)}
                >
                  <Text>×</Text>
                </View>
              </View>
            ))}
          </View>
        )}

        {/* 输入框 */}
        <View className="input-wrapper">
          <Textarea
            className="message-input"
            placeholder={finalConfig.placeholder}
            maxlength={finalConfig.maxInputLength}
            disabled={disabled || isLoading}
            value={inputValue}
            onInput={handleInputChange}
            onConfirm={handleConfirm}
            autoHeight
            showConfirmBar={false}
          />
        </View>
        
        {/* 自定义输入工具栏或默认操作栏 */}
        {renderInputTools ? (
          <View className="input-tools-slot">
            {renderInputTools(customRenderProps)}
          </View>
        ) : (
          <View className="action-bar">
            <View className="action-left">
              {enableFileUpload && (
                <Button 
                  size="mini"
                  className="upload-btn"
                  onClick={handleChooseImage}
                  disabled={disabled || isLoading || uploading}
                >
                  📷
                </Button>
              )}
              
              <Button 
                size="mini"
                className="clear-btn"
                onClick={handleClear}
                disabled={isLoading || uploading || messages.length === 0}
              >
                清空
              </Button>
            </View>
            
            <View className="action-right">
              <Button 
                size="mini"
                type="primary"
                className="send-btn"
                onClick={handleSend}
                disabled={!canSend}
                loading={isLoading || uploading}
              >
                {uploading ? '上传中...' : isLoading ? '发送中' : '发送'}
              </Button>
              
              {(isLoading || uploading) && (
                <Button 
                  size="mini"
                  onClick={abortRequest}
                  className="abort-btn"
                >
                  取消
                </Button>
              )}
            </View>
          </View>
        )}

        {/* 提示词 */}
        {showMenu.length > 0 && !disabled && (
          <View className="prompts-container">
            {showMenu.map((prompt) => (
              <View 
                key={prompt.key}
                className="prompt-item"
                onClick={() => handlePromptClick(prompt)}
              >
                <Text>{prompt.label}</Text>
              </View>
            ))}
          </View>
        )}
      </View>

      {/* 错误显示 */}
      {error && (
        <View className="error-message">
          <Text className="error-text">{error}</Text>
        </View>
      )}

      {/* 状态信息 */}
      <View className="status-info">
        <Text className="status-text">
          {currentChatId && `会话ID: ${currentChatId}`}
          {isLoading && ' • 发送中...'}
        </Text>
      </View>
      
      {/* 自定义底部插槽 */}
      {renderFooter && (
        <View className="chat-footer-slot">
          {renderFooter(customRenderProps)}
        </View>
      )}
    </View>
  )
}

export default TaroAIChatComponent 