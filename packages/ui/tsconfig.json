{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist",
    "rootDir": ".",
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": "../../",
    "paths": {
      "@ai-component/core": ["packages/core"],
      "@ai-component/config": ["packages/config"],
      "@ai-component/adapters": ["packages/adapters"],
      "@shared/*": ["src/shared/*"]
    }
  },
  "include": [
    "./**/*.ts",
    "./**/*.tsx",
    "./**/*.d.ts"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
} 