// React组件导出
export { AIChatComponent as ReactAIChatComponent } from './web/chat-component'
export { MarkdownContent as ReactMarkdownContent } from './web'

// Vue组件导出 - 请直接从 './vue' 导入
// import { VueAIChatComponent } from '@ai-component/ui/vue'

// 类型导出
export type { AIChatComponentProps } from './web/chat-component'
export type { AIChatComponentProps as VueAIChatComponentProps } from './vue/types'

// 平台检测工具
export const detectPlatform = (): 'web' | 'taro' | 'vue' => {
  // 检测是否在Taro环境
  if (typeof process !== 'undefined' && process.env.TARO_ENV) {
    return 'taro'
  }
  
  // 检测是否在浏览器环境中的Taro
  if (typeof window !== 'undefined' && (window as any).Taro) {
    return 'taro'
  }
  
  // 检测是否在Vue环境（通过Vue实例）
  if (typeof window !== 'undefined' && 
      ((window as any).Vue || (window as any).__VUE__)) {
    return 'vue'
  }
  
  // 默认为web环境
  return 'web'
}

// Taro组件条件导出工厂函数
export const getTaroAIChatComponent = async () => {
  const platform = detectPlatform()
  
  if (platform !== 'taro') {
    throw new Error('Taro组件仅支持在Taro环境中使用')
  }
  
  try {
    // 动态导入，避免在非Taro环境下解析依赖
    const taroModule = await import('./taro/chat-component')
    return {
      TaroAIChatComponent: taroModule.TaroAIChatComponent,
      TaroAIChatComponentDefault: taroModule.default
    }
  } catch (error) {
    console.error('加载Taro组件失败:', error)
    throw new Error('Taro组件加载失败，请确保在正确的Taro环境中使用')
  }
}

// Taro组件类型条件导出
export type { TaroAIChatComponentProps } from './taro/chat-component'

// 默认导出React组件（避免跨框架冲突）
import { AIChatComponent as WebAIChatComponent } from './web/chat-component'
export const AIChatComponent = WebAIChatComponent
export default WebAIChatComponent

// 使用说明注释
/**
 * 跨平台AI聊天组件使用指南:
 * 
 * 1. React/Web环境:
 *    import { AIChatComponent } from '@ai-component/ui'
 *    或
 *    import { ReactAIChatComponent } from '@ai-component/ui'
 * 
 * 2. Vue环境:
 *    import { VueAIChatComponent } from '@ai-component/ui/vue'
 *    import { useVueChat } from '@ai-component/ui/vue'
 * 
 * 3. Taro环境:
 *    const { TaroAIChatComponent } = await getTaroAIChatComponent()
 *    或直接导入:
 *    import { TaroAIChatComponent } from '@ai-component/ui/taro'
 * 
 * 4. 平台检测:
 *    const platform = detectPlatform() // 'web' | 'taro' | 'vue'
 */ 