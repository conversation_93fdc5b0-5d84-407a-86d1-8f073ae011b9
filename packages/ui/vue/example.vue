<template>
  <div class="vue-example">
    <h1>Vue AI Chat Component Examples</h1>
    
    <!-- 基础用法示例 -->
    <section class="example-section">
      <h2>基础用法</h2>
      <div class="example-container">
        <AIChatComponent
          :config="basicConfig"
          @send="handleSend"
          @receive="handleReceive"
          @error="handleError"
        />
      </div>
    </section>
    
    <!-- 高级配置示例 -->
    <section class="example-section">
      <h2>高级配置（带提示词和文件上传）</h2>
      <div class="example-container">
        <AIChatComponent
          :config="advancedConfig"
          :prompts="promptItems"
          :enable-file-upload="true"
          :max-files="6"
          :max-file-size="5"
          placeholder="请输入您的问题..."
          class-name="advanced-chat"
          @send="handleAdvancedSend"
          @receive="handleReceive"
          @error="handleError"
        />
      </div>
    </section>
    
    <!-- 暗色主题示例 -->
    <section class="example-section">
      <h2>暗色主题</h2>
      <div class="example-container dark-theme">
        <AIChatComponent
          :config="darkConfig"
          :prompts="promptItems"
          :enable-file-upload="true"
          placeholder="暗色主题聊天..."
          @send="handleSend"
          @receive="handleReceive"
          @error="handleError"
        />
      </div>
    </section>
    
    <!-- 悬浮模式示例 -->
    <section class="example-section">
      <h2>悬浮模式</h2>
      <div class="example-container">
        <div class="floating-demo-area">
          <p>悬浮模式聊天组件将显示在页面右下角</p>
          <AIChatComponent
            :floating-mode="true"
            :floating-config="floatingConfig"
            :chat-dialog-config="chatDialogConfig"
            :config="floatingModeConfig"
            :prompts="promptItems"
            :enable-file-upload="true"
            placeholder="悬浮模式聊天..."
            @send="handleSend"
            @receive="handleReceive"
            @error="handleError"
            @floating-toggle="handleFloatingToggle"
            @position-change="handlePositionChange"
          />
        </div>
      </div>
    </section>
    
    <!-- 禁用状态示例 -->
    <section class="example-section">
      <h2>禁用状态</h2>
      <div class="example-container">
        <AIChatComponent
          :config="basicConfig"
          :disabled="true"
          placeholder="组件已禁用..."
          @send="handleSend"
          @receive="handleReceive"
          @error="handleError"
        />
      </div>
    </section>
    
    <!-- 事件日志 -->
    <section class="example-section">
      <h2>事件日志</h2>
      <div class="event-log">
        <div 
          v-for="(log, index) in eventLogs" 
          :key="index"
          :class="['log-item', `log-${log.type}`]"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-type">{{ log.type.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
// 在实际项目中，应该这样导入：
// import { VueAIChatComponent as AIChatComponent } from '@ai-component/ui'
// 这里使用相对路径是因为这是包内部的示例文件
import AIChatComponent from './chat-component.vue'

// 事件日志类型
interface EventLog {
  time: string
  type: 'send' | 'receive' | 'error' | 'clear'
  message: string
}

// 响应式数据
const eventLogs = ref<EventLog[]>([])

// 基础配置
const basicConfig = reactive({
  showAvatar: true,
  placeholder: '请输入您的消息...',
  enableAutoScroll: true,
  style: {
    height: '400px',
    border: '1px solid #e8e8e8',
    borderRadius: '8px'
  }
})

// 高级配置
const advancedConfig = reactive({
  showAvatar: true,
  placeholder: '输入问题或选择下方提示词...',
  maxInputLength: 2000,
  enableAutoScroll: true,
  timeout: 60000,
  style: {
    height: '500px',
    border: '2px solid #1890ff',
    borderRadius: '12px',
    boxShadow: '0 4px 12px rgba(24, 144, 255, 0.15)'
  }
})

// 暗色主题配置
const darkConfig = reactive({
  showAvatar: true,
  placeholder: '暗色主题消息...',
  enableAutoScroll: true,
  theme: 'dark',
  style: {
    height: '400px',
    borderRadius: '8px'
  }
})

// 悬浮模式配置
const floatingModeConfig = reactive({
  showAvatar: true,
  enableAutoScroll: true,
  sessionId: 'floating-demo',
  style: {
    height: '100%'
  }
})

// 悬浮配置
const floatingConfig = reactive({
  enabled: true,
  icon: '💬',
  position: {
    x: 20,
    y: 100,
    side: 'right' as 'left' | 'right'
  },
  dragEnabled: true,
  rememberPosition: true
})

// 聊天对话框配置
const chatDialogConfig = reactive({
  title: '智能助手',
  width: 380,
  height: 500,
  position: 'auto' as 'auto' | 'left' | 'right' | 'center'
})

// 提示词配置
const promptItems = ref([
  {
    key: 'greeting',
    label: '问候',
    content: '你好，很高兴见到你！',
    show: true
  },
  {
    key: 'help',
    label: '帮助',
    content: '请问有什么可以帮助您的吗？',
    show: true
  },
  {
    key: 'technical',
    label: '技术问题',
    content: '我遇到了一个技术问题，能帮我解决吗？',
    show: true
  },
  {
    key: 'feedback',
    label: '反馈建议',
    content: '我想提供一些产品使用反馈',
    show: true
  },
  {
    key: 'explain',
    label: '解释说明',
    content: '请详细解释这个概念',
    show: true
  }
])

// 工具函数
const addLog = (type: EventLog['type'], message: string) => {
  const now = new Date()
  eventLogs.value.unshift({
    time: now.toLocaleTimeString(),
    type,
    message
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

// 事件处理函数
const handleSend = (message: string, files?: any[]) => {
  const fileInfo = files && files.length > 0 
    ? ` (包含${files.length}个文件)` 
    : ''
  addLog('send', `发送消息: ${message}${fileInfo}`)
}

const handleAdvancedSend = (message: string, files?: any[]) => {
  const fileInfo = files && files.length > 0 
    ? ` (包含${files.length}个文件: ${files.map(f => f.name).join(', ')})` 
    : ''
  addLog('send', `[高级] 发送消息: ${message}${fileInfo}`)
}

const handleReceive = (message: string) => {
  addLog('receive', `收到回复: ${message}`)
}

const handleError = (error: string) => {
  addLog('error', `错误: ${error}`)
}

const handleFloatingToggle = (expanded: boolean) => {
  addLog('send', `悬浮窗口${expanded ? '展开' : '收起'}`)
}

const handlePositionChange = (position: { x: number; y: number; side: 'left' | 'right' }) => {
  addLog('send', `悬浮位置变更: ${position.side}侧 (${position.x}, ${position.y})`)
}

// 初始化示例日志
addLog('send', '这是一条示例发送消息')
addLog('receive', '这是一条示例AI回复')
addLog('error', '这是一条示例错误消息')
</script>

<style scoped>
.vue-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.vue-example h1 {
  color: #262626;
  margin-bottom: 32px;
  text-align: center;
  font-size: 28px;
  font-weight: 600;
}

.example-section {
  margin-bottom: 48px;
}

.example-section h2 {
  color: #1890ff;
  margin-bottom: 16px;
  font-size: 20px;
  font-weight: 500;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.example-container {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.example-container.dark-theme {
  background: #141414;
  border-color: #434343;
}

.floating-demo-area {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  color: #666;
  font-size: 14px;
  position: relative;
}

/* 事件日志样式 */
.event-log {
  max-height: 300px;
  overflow-y: auto;
  background: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
}

.log-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #999;
  font-family: monospace;
  min-width: 70px;
}

.log-type {
  font-weight: 600;
  min-width: 60px;
  text-align: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

.log-send .log-type {
  background: #e6f7ff;
  color: #1890ff;
}

.log-receive .log-type {
  background: #f6ffed;
  color: #52c41a;
}

.log-error .log-type {
  background: #fff2f0;
  color: #ff4d4f;
}

.log-clear .log-type {
  background: #f0f0f0;
  color: #666;
}

.log-message {
  flex: 1;
  color: #333;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vue-example {
    padding: 16px;
  }
  
  .vue-example h1 {
    font-size: 24px;
  }
  
  .example-container {
    padding: 16px;
  }
  
  .log-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .log-time,
  .log-type {
    min-width: auto;
  }
}

/* 滚动条样式 */
.event-log::-webkit-scrollbar {
  width: 6px;
}

.event-log::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.event-log::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.event-log::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 