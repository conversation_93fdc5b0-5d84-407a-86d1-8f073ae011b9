<template>
  <!-- 标准模式 -->
  <div v-if="!props.floatingMode" :class="['ai-chat-component-wrapper', props.className]" :style="props.style">
    <ChatContent v-bind="props" />
  </div>

  <!-- 悬浮模式，使用 Teleport 挂载到 body -->
  <Teleport to="body" v-else>
    <!-- 悬浮触发器 -->
    <FloatingTrigger
      v-if="!isExpanded"
      ref="triggerRef"
      :config="props.floatingConfig || {}"
      :unread-count="unreadCount"
      :position="position"
      :is-dragging="isDragging"
      @click="handleClick"
      @drag-start="handleDragStart"
    >
      <template #icon>
        <slot name="floating-icon">
          <div class="floating-default-icon">💬</div>
        </slot>
      </template>
    </FloatingTrigger>

    <!-- 聊天对话框 -->
    <ChatDialog
      :is-visible="isExpanded"
      :position="position"
      :config="props.chatDialogConfig"
      @close="setExpanded(false)"
    >
      <ChatContent v-bind="chatContentProps" />
    </ChatDialog>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, Teleport, defineAsyncComponent, type PropType } from 'vue';
import { useDragFloat } from './composables/useDragFloat';
import type { AIChatComponentProps, FloatingConfig, AvatarConfig, ChatDialogConfig, UploadConfig, UploadResponse, PromptItem } from './types';

import '../shared/ui/style/normalize.css'

const ChatContent = defineAsyncComponent(() => import('./components/ChatContent.vue'));
const FloatingTrigger = defineAsyncComponent(() => import('./components/FloatingTrigger.vue'));
const ChatDialog = defineAsyncComponent(() => import('./components/ChatDialog.vue'));

const props = defineProps({
  config: { type: Object as PropType<Partial<AIChatComponentProps['config']>>, default: () => ({}) },
  className: { type: String, default: '' },
  style: { type: Object as PropType<Record<string, any>>, default: () => ({}) },
  prompts: { type: Array as PropType<PromptItem[]>, default: () => [] },
  placeholder: { type: String, default: '请输入您的消息...' },
  disabled: { type: Boolean, default: false },
  enableFileUpload: { type: Boolean, default: false },
  maxFiles: { type: Number, default: 9 },
  maxFileSize: { type: Number, default: 10 },
  floatingMode: { type: Boolean, default: false },
  floatingConfig: { type: Object as PropType<Partial<FloatingConfig>>, default: () => ({}) },
  avatarConfig: { type: Object as PropType<Partial<AvatarConfig>>, default: () => ({}) },
  chatDialogConfig: { type: Object as PropType<Partial<ChatDialogConfig>>, default: () => ({}) },
  showFloatingEffect: { type: Boolean, default: false },
  uploadFile: { type: [Function, undefined] as PropType<((file: File) => Promise<string | UploadResponse>) | undefined>, default: undefined },
  uploadConfig: { type: [Object, undefined] as PropType<UploadConfig | undefined>, default: undefined },
  onSend: { type: [Function, undefined] as PropType<((message: string, files?: any[]) => void) | undefined>, default: undefined },
  onReceive: { type: [Function, undefined] as PropType<((message: string) => void) | undefined>, default: undefined },
  onError: { type: [Function, undefined] as PropType<((error: string) => void) | undefined>, default: undefined },
  onFloatingToggle: { type: [Function, undefined] as PropType<((expanded: boolean) => void) | undefined>, default: undefined },
  onPositionChange: { type: [Function, undefined] as PropType<((position: { x: number; y: number; side: 'left' | 'right' }) => void) | undefined>, default: undefined },
  renderSenderHeader: { type: [Function, undefined] as PropType<(() => any) | undefined>, default: undefined },
  renderHeader: { type: [Function, undefined] as PropType<(() => any) | undefined>, default: undefined },
  renderEmpty: { type: [Function, undefined] as PropType<(() => any) | undefined>, default: undefined },
  renderMessage: { type: [Function, undefined] as PropType<(() => any) | undefined>, default: undefined },
  renderMessageActions: { type: [Function, undefined] as PropType<(() => any) | undefined>, default: undefined },
  renderLoading: { type: [Function, undefined] as PropType<(() => any) | undefined>, default: undefined },
  renderInputTools: { type: [Function, undefined] as PropType<(() => any) | undefined>, default: undefined },
  renderFooter: { type: [Function, undefined] as PropType<(() => any) | undefined>, default: undefined }
});

const emit = defineEmits(['floating-toggle', 'position-change']);

const triggerRef = ref()

const {
  isDragging,
  isExpanded,
  position,
  dragRef,
  handleDragStart,
  handleClick,
  toggleExpanded,
  setExpanded,
} = useDragFloat({
  enabled: props.floatingMode,
  dragEnabled: props.floatingConfig?.dragEnabled !== false, // 默认启用，除非明确禁用
  rememberPosition: props.floatingConfig?.rememberPosition !== false, // 默认启用
  ...props.floatingConfig,
});

// 将 triggerRef 赋值给 dragRef，确保拖拽功能正常工作
watch(() => [props.floatingMode, triggerRef.value], async ([enabled, triggerRefValue]: [boolean, any]) => {
  console.log('floatingMode changed:', enabled);

  if (!enabled || !triggerRefValue) return;

  const r = triggerRefValue

  if (r && r.triggerRef) {
    dragRef.value = r.triggerRef
  }
}, { immediate: true });

const unreadCount = ref(0);

watch(isExpanded, (val: boolean) => {
  emit('floating-toggle', val);
  props.onFloatingToggle?.(val);
  if (val) {
    unreadCount.value = 0;
  }
});

watch(position, (val: { x: number; y: number; side: 'left' | 'right' }) => {
  emit('position-change', val);
  props.onPositionChange?.(val);
});

const handleNewMessage = (message: string) => {
  if (props.floatingMode && !isExpanded.value) {
    unreadCount.value++;
  }
  props.onReceive?.(message);
};

const chatContentProps = computed(() => ({
  ...props,
  onReceive: handleNewMessage,
  uploadFile: props.uploadFile,
  uploadConfig: props.uploadConfig
}));

// 明确暴露模板中使用的变量
defineExpose({
  isExpanded,
  unreadCount,
  position,
  isDragging,
  handleClick,
  handleDragStart,
  setExpanded,
  chatContentProps,
  triggerRef
});
</script>

<style scoped>
@import './chat-component.css';

/* Transition anmations */
.chat-dialog-fade-enter-active,
.chat-dialog-fade-leave-active {
  transition: opacity 0.3s ease;
}
.chat-dialog-fade-enter-from,
.chat-dialog-fade-leave-to {
  opacity: 0;
}
</style> 