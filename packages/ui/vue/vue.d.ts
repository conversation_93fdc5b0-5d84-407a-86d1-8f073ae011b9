import { VNode } from 'vue'

// Vue 全局类型声明
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 补充Vue类型声明，确保所有Vue API都被正确导出
declare module 'vue' {
  export interface ComponentPublicInstance {
    $el: Element
  }
  
  // 确保 ref、reactive 等常用 API 的类型正确导出
  export function ref<T>(value: T): Ref<T>
  export function reactive<T extends object>(target: T): T
  export function computed<T>(getter: () => T): ComputedRef<T>
  export function watch<T>(
    source: T | (() => T),
    callback: (newValue: T, oldValue: T) => void,
    options?: WatchOptions
  ): WatchStopHandle
  export function watchEffect(
    effect: () => void,
    options?: WatchEffectOptions
  ): WatchStopHandle
  export function onMounted(hook: () => void): void
  export function onUnmounted(hook: () => void): void
  export function nextTick(fn?: () => void): Promise<void>
  export function defineComponent(options: any): any
  export function defineProps<T>(): T
  export function defineProps(props: any): any
  export function defineEmits<T>(): T
  export function defineEmits(emits: any): any
  export function defineExpose(exposed: any): void
  export function defineAsyncComponent(loader: () => Promise<any>): any
  
  // PropType 和其他类型工具
  export interface PropType<T = any> {
    new (...args: any[]): T & {}
    __propType?: T
  }
  
  // 基础类型
  export interface Ref<T = any> {
    value: T
  }
  
  export interface ComputedRef<T = any> {
    readonly value: T
  }
  
  export interface WatchOptions {
    immediate?: boolean
    deep?: boolean
    flush?: 'pre' | 'post' | 'sync'
  }
  
  export interface WatchEffectOptions {
    flush?: 'pre' | 'post' | 'sync'
    onTrack?: (event: any) => void
    onTrigger?: (event: any) => void
  }
  
  export type WatchStopHandle = () => void
  
  // Teleport 组件
  export const Teleport: DefineComponent<{
    to: string | Element
    disabled?: boolean
  }>
  
  // VNode 类型
  export interface VNode {
    type: any
    props: any
    children: any
    key: string | number | symbol | null
  }
  
  // 组件类型
  export type Component = DefineComponent<any, any, any>
  
  // 插槽类型
  export interface Slot {
    (...args: any[]): VNode[]
  }
  
  export interface Slots {
    [name: string]: Slot | undefined
  }
}

// 全局类型增强
declare global {
  namespace JSX {
    interface Element extends VNode {}
    interface ElementClass {
      $props: {}
    }
    interface ElementAttributesProperty {
      $props: {}
    }
    interface IntrinsicElements {
      [elem: string]: any
    }
  }
}

export {}