<template>
  <div :class="['ai-chat-content', { dark: isDarkMode }]" @dragenter.prevent="handleDragEnter" @dragover.prevent="handleDragOver" @dragleave.prevent="handleDragLeave" @drop.prevent="handleDrop">
    <!-- 自定义头部插槽 -->
    <div v-if="$slots.header || renderHeader" class="chat-header-slot">
      <component v-if="renderHeader" :is="renderHeader(customRenderProps)" />
      <slot v-else name="header" :messages="messages" :isLoading="isLoading" />
    </div>
    
    <div v-if="dragActive" class="send-drag-overlay">
      <div class="send-drag-icon">📁</div>
      <div class="send-drag-title">拖拽文件到这里</div>
      <div class="send-drag-desc">支持图片、文档、Office文件，最大{{ maxFileSize }}MB</div>
    </div>
    <input
      ref="fileInputRef"
      type="file"
      multiple
      :accept="SEND_CONSTANTS.ACCEPT"
      style="display: none"
      @change="onFileSelected"
    />
    
    <!-- Error message -->
    <div v-if="error" class="error-message">
      {{ error }}
    </div>
    
    <!-- Messages container -->
    <div ref="messagesContainer" class="messages-container">
       <!-- 空状态插槽 -->
       <div v-if="messages.length === 0 && !isLoading" class="empty-state-slot">
         <component v-if="renderEmpty" :is="renderEmpty(customRenderProps)" />
         <slot v-else-if="$slots.empty" name="empty" />
         <div v-else class="default-empty-state">
           <div class="empty-icon">💬</div>
           <div class="empty-text">开始与 AI 助手对话吧</div>
         </div>
       </div>
       
       <div v-for="message in messages" :key="message.id" :class="['bubble-wrapper', `bubble-${message.role}`]">
          <a-avatar v-if="finalConfig.showAvatar && message.role === 'user'" :src="avatarConfig?.user?.src" :icon="avatarConfig?.user?.icon">
            <template #icon><user-outlined /></template>
          </a-avatar>
          <a-avatar v-if="finalConfig.showAvatar && message.role === 'assistant'" :src="avatarConfig?.assistant?.src" :icon="avatarConfig?.assistant?.icon">
            <template #icon><robot-outlined /></template>
          </a-avatar>
          <div class="bubble-content">
            <!-- 自定义消息内容插槽 -->
            <component 
              v-if="renderMessage" 
              :is="renderMessage(message, customRenderProps)" 
            />
            <slot 
              v-else-if="$slots.message" 
              name="message" 
              :message="message" 
              :isLoading="isLoading"
              :formatTime="formatTime"
            />
            <div v-else>
              <!-- 文件显示 -->
              <div v-if="message.files && message.files.length > 0" class="message-files">
                <div v-for="file in message.files" :key="file.uid" class="message-file-item">
                  <img v-if="isImageFile(file.type)" :src="file.url" :alt="file.name" class="message-image" />
                  <div v-else class="message-file">
                    <div class="message-file-icon">
                      <component :is="getFileIcon(file.type)" />
                    </div>
                    <div class="message-file-info">
                      <div class="message-file-name" :title="file.name">
                        {{ file.name }}
                      </div>
                      <div class="message-file-type">
                        {{ SEND_CONSTANTS.TYPE_DESCRIPTIONS[file.type as keyof typeof SEND_CONSTANTS.TYPE_DESCRIPTIONS] || '未知文件' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 文本内容 -->
              <div v-if="message.content" class="bubble-text">
                <MarkdownContent 
                  v-if="message.role === 'assistant'"
                  :content="message.content" 
                  :isRequesting="false"
                  :enableCodeCopy="true"
                  :enableMath="true"
                />
                <template v-else>{{ message.content }}</template>
              </div>
              <div class="message-timestamp">{{ formatTime(message.timestamp) }}</div>
            </div>
            
            <!-- 消息操作插槽 -->
            <div v-if="$slots.messageActions || renderMessageActions" class="message-actions-slot">
              <component v-if="renderMessageActions" :is="renderMessageActions(message, customRenderProps)" />
              <slot v-else name="messageActions" :message="message" :isLoading="isLoading" />
            </div>
          </div>
       </div>
       <div v-if="isLoading" class="bubble-wrapper bubble-assistant">
          <a-avatar v-if="finalConfig.showAvatar">
            <template #icon><robot-outlined /></template>
          </a-avatar>
          <div class="bubble-content">
            <!-- 自定义加载状态插槽 -->
            <component v-if="renderLoading" :is="renderLoading(customRenderProps)" />
            <slot v-else-if="$slots.loading" name="loading" />
            <div v-else class="bubble-loading">
              <span></span><span></span><span></span>
            </div>
          </div>
       </div>
    </div>
    
    <!-- Prompts section -->
    <div v-if="prompts && prompts.length > 0 && !isLoading" class="prompts-container">
      <div class="prompts-list">
        <div
          v-for="prompt in visiblePrompts"
          :key="prompt.key"
          class="prompt-item"
          @click="handlePromptClick(prompt)"
        >
          {{ prompt.title || prompt.content }}
        </div>
      </div>
    </div>
    
    <!-- Sender container -->
    <div class="sender-container">
      <!-- File preview header -->
      <div v-if="enableFileUpload && fileList.length > 0" class="send-header">
        <div class="send-file-preview__container">
          <div v-for="file in fileList" :key="file.uid" class="send-file-preview__item">
            <a-image v-if="isImageFile(file.type)" :src="file.url" :alt="file.name" class="send-file-preview__img" />
            <div v-else class="send-file-preview__file">
              <div class="send-file-preview__icon">
                <component :is="getFileIcon(file.type)" />
              </div>
              <div class="send-file-preview__info">
                <div class="send-file-preview__name" :title="file.name">
                  {{ file.name }}
                </div>
                <div class="send-file-preview__type">
                  {{ SEND_CONSTANTS.TYPE_DESCRIPTIONS[file.type as keyof typeof SEND_CONSTANTS.TYPE_DESCRIPTIONS] || '未知文件' }}
                </div>
              </div>
            </div>
            <a-button
              class="send-file-preview__btn"
              shape="circle"
              size="small"
              danger
              @click="handleRemoveFile(file.uid)"
            >
              ×
            </a-button>
          </div>
        </div>
      </div>
      
      <!-- Input area -->
      <div class="sender-input-wrapper">
        <a-textarea
          v-model:value="inputValue"
          :placeholder="finalConfig.placeholder || placeholder"
          :disabled="disabled || isLoading"
          :rows="1"
          :auto-size="{ minRows: 1, maxRows: 6 }"
          @press-enter="handlePressEnter"
        />
        <div class="sender-actions">
          <!-- 自定义输入工具插槽 -->
          <div v-if="$slots.inputTools || renderInputTools" class="input-tools-slot">
            <component v-if="renderInputTools" :is="renderInputTools(customRenderProps)" />
            <slot 
              v-else
              name="inputTools" 
              :fileList="fileList"
              :canSend="canSend"
              :isLoading="isLoading"
              :disabled="disabled"
              :onUploadClick="() => fileInputRef?.click()"
              :onClear="handleClear"
              :onSend="handleSend"
            />
          </div>
          <a-space v-else>
            <a-tooltip v-if="enableFileUpload" title="上传文件">
              <a-button
                type="text"
                :disabled="isFileUploadDisabled || uploading"
                @click="() => fileInputRef?.click()"
              >
                <template #icon><file-image-outlined /></template>
              </a-button>
            </a-tooltip>
            <a-tooltip title="清空对话">
              <a-button
                type="text"
                :disabled="disabled || isLoading || messages.length === 0"
                @click="handleClear"
              >
                <template #icon><clear-outlined /></template>
              </a-button>
            </a-tooltip>
            <a-button
              type="primary"
              :loading="isLoading || uploading"
              :disabled="!canSend"
              @click="handleSend"
            >
              {{ uploading ? '上传中...' : '发送' }}
            </a-button>
          </a-space>
        </div>
      </div>
    </div>
    
    <!-- 自定义底部插槽 -->
    <div v-if="$slots.footer || renderFooter" class="chat-footer-slot">
      <component v-if="renderFooter" :is="renderFooter(customRenderProps)" />
      <slot 
        v-else
        name="footer" 
        :messages="messages" 
        :isLoading="isLoading" 
        :canSend="canSend"
        :inputValue="inputValue"
        :fileList="fileList"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick, type VNode, type PropType, defineProps, defineEmits } from 'vue';
import { Avatar, Button, Tooltip, Space, Input, message } from 'ant-design-vue';
import { UserOutlined, RobotOutlined, FileImageOutlined, ClearOutlined, FilePdfOutlined, FileTextOutlined, FileWordOutlined, FileExcelOutlined, FilePptOutlined, FileOutlined } from '@ant-design/icons-vue';
import type { AvatarConfig, FileItem, PromptItem, Message, CustomRenderProps, UploadConfig, UploadResponse } from '../../types';
import MarkdownContent from './MarkdownContent.vue';
import { uploadFileWithConfig } from '../utils/upload';

const { TextArea } = Input;

const SEND_CONSTANTS = {
  MAX_IMAGES: 9,
  MAX_SIZE_MB: 10,
  ACCEPT: 'image/jpeg,image/png,image/webp,image/jpg,application/pdf,text/plain,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ACCEPT_EXTENSIONS: '.jpg,.jpeg,.png,.webp,.pdf,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx',
  DEFAULT_PLACEHOLDER: '输入您想要提问的问题并点击发送',
  SUPPORTED_TYPES: {
    image: ['image/jpeg', 'image/png', 'image/webp', 'image/jpg'],
    document: ['application/pdf', 'text/plain'],
    office: [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel', 
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ]
  },
  TYPE_DESCRIPTIONS: {
    'image/jpeg': 'JPEG图片',
    'image/png': 'PNG图片', 
    'image/webp': 'WebP图片',
    'image/jpg': 'JPG图片',
    'application/pdf': 'PDF文档',
    'text/plain': '文本文件',
    'application/msword': 'Word文档',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word文档',
    'application/vnd.ms-excel': 'Excel表格',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel表格',
    'application/vnd.ms-powerpoint': 'PowerPoint演示',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint演示'
  }
};

// Message interface
interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp?: number;
  images?: Array<{
    url: string;
    name: string;
  }>;
}

// 临时的 useVueChat hook - 支持历史记录功能
const useVueChatMock = (config: any, showHistory?: boolean, sessionId?: string) => {
  const SESSION_ID = sessionId || config?.sessionId || 'default-session';
  
  const initialMessages = () => {
    const enableHistory = showHistory ?? config?.showHistory;
    if (enableHistory) {
      try {
        const saved = localStorage.getItem(`chat-history-${SESSION_ID}`);
        return saved ? JSON.parse(saved) : [];
      } catch (e) {
        return [];
      }
    }
    return [];
  };

  const messages = ref<Message[]>(initialMessages());
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  
  // 监听历史配置变化，重新加载消息
  watch(() => [showHistory ?? config?.showHistory, sessionId ?? config?.sessionId], () => {
    const enableHistory = showHistory ?? config?.showHistory;
    if (enableHistory) {
      try {
        const saved = localStorage.getItem(`chat-history-${SESSION_ID}`);
        messages.value = saved ? JSON.parse(saved) : [];
      } catch (e) {
        messages.value = [];
      }
    } else {
      messages.value = [];
    }
  });

  // 监听消息变化，自动保存历史记录
  watch(messages, (newMessages) => {
    const enableHistory = showHistory ?? config?.showHistory;
    if (enableHistory) {
      localStorage.setItem(`chat-history-${SESSION_ID}`, JSON.stringify(newMessages));
    }
  }, { deep: true });
  
  const sendMessage = async (content: string, files?: FileItem[]) => {
    if (!content.trim() && (!files || files.length === 0)) return;
    
    isLoading.value = true;
    error.value = null;
    
    // Add user message with files
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content,
      timestamp: Date.now(),
      images: files && files.length > 0 ? files.filter(f => isImageFile(f.type)).map(f => ({ url: f.url, name: f.name })) : undefined,
      files: files && files.length > 0 ? files.map(f => ({ uid: f.uid, url: f.url, name: f.name, type: f.type, size: f.size })) : undefined
    };
    messages.value.push(userMessage);
    
    await nextTick();
    
    // Simulate AI response
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `这是AI的回复。您发送了: "${content}"${files && files.length > 0 ? ` 以及 ${files.length} 个文件` : ''}`,
        timestamp: Date.now()
      };
      messages.value.push(aiMessage);
      isLoading.value = false;
    }, 1000);
  };
  
  const clearMessages = () => {
    messages.value = [];
    error.value = null;
    const enableHistory = showHistory ?? config?.showHistory;
    if (enableHistory) {
      localStorage.removeItem(`chat-history-${SESSION_ID}`);
    }
  };
  
  return { messages, isLoading, error, sendMessage, clearMessages };
};

interface ChatContentProps {
  messages: Message[];
  isLoading: boolean;
  canSend?: boolean;
  inputValue?: string;
  fileList?: FileItem[];
  prompts?: PromptItem[];
  avatarConfig?: AvatarConfig;
  placeholder?: string;
  disabled?: boolean;
  renderHeader?: (props: CustomRenderProps) => any;
  renderEmpty?: (props: CustomRenderProps) => any;
  renderMessage?: (message: Message, props: CustomRenderProps) => any;
  renderMessageActions?: (message: Message, props: CustomRenderProps) => any;
  renderLoading?: (props: CustomRenderProps) => any;
  renderInputTools?: (props: CustomRenderProps) => any;
  renderFooter?: (props: CustomRenderProps) => any;
}

const props = defineProps({
  messages: { type: Array as PropType<Message[]>, default: () => [] },
  isLoading: { type: Boolean, default: false },
  canSend: { type: Boolean, default: true },
  inputValue: { type: String, default: '' },
  fileList: { type: Array as PropType<FileItem[]>, default: () => [] },
  prompts: { type: Array as PropType<PromptItem[]>, default: () => [] },
  avatarConfig: { type: Object as PropType<AvatarConfig>, default: () => ({}) },
  placeholder: { type: String, default: '请输入您的消息...' },
  disabled: { type: Boolean, default: false },
  renderHeader: { type: [Function, undefined] as PropType<((props: CustomRenderProps) => any) | undefined>, default: undefined },
  renderEmpty: { type: [Function, undefined] as PropType<((props: CustomRenderProps) => any) | undefined>, default: undefined },
  renderMessage: { type: [Function, undefined] as PropType<((message: Message, props: CustomRenderProps) => any) | undefined>, default: undefined },
  renderMessageActions: { type: [Function, undefined] as PropType<((message: Message, props: CustomRenderProps) => any) | undefined>, default: undefined },
  renderLoading: { type: [Function, undefined] as PropType<((props: CustomRenderProps) => any) | undefined>, default: undefined },
  renderInputTools: { type: [Function, undefined] as PropType<((props: CustomRenderProps) => any) | undefined>, default: undefined },
  renderFooter: { type: [Function, undefined] as PropType<((props: CustomRenderProps) => any) | undefined>, default: undefined },
  // 添加缺少的 props
  config: { type: [Object, undefined] as PropType<any>, default: () => ({}) },
  showHistory: { type: Boolean, default: false },
  sessionId: { type: String, default: 'default' },
  enableFileUpload: { type: Boolean, default: false },
  maxFileSize: { type: Number, default: 10 },
  maxFiles: { type: Number, default: 9 },
  uploadFile: { type: [Function, undefined] as PropType<((file: File) => Promise<string | UploadResponse>) | undefined>, default: undefined },
  uploadConfig: { type: [Object, undefined] as PropType<UploadConfig | undefined>, default: undefined },
  onError: { type: [Function, undefined] as PropType<((error: string) => void) | undefined>, default: undefined },
  onSend: { type: [Function, undefined] as PropType<((message: string, files?: FileItem[]) => void) | undefined>, default: undefined },
  onReceive: { type: [Function, undefined] as PropType<((message: string) => void) | undefined>, default: undefined },
  className: { type: String, default: '' }
});

const emit = defineEmits(['send', 'remove-file', 'error', 'clear']);

const { messages, isLoading, error, sendMessage, clearMessages } = useVueChatMock(props.config, props.showHistory, props.sessionId);
const messagesContainer = ref<HTMLDivElement | null>(null);
const fileInputRef = ref<HTMLInputElement | null>(null);
const inputValue = ref('');
const fileList = ref<FileItem[]>([]);
const dragActive = ref(false);
const uploading = ref(false);
const dragCounter = ref(0); // 用于跟踪拖拽状态，防止闪烁

const isDarkMode = computed(() => props.config?.theme === 'dark' || props.className?.includes('dark'));
const finalConfig = computed(() => ({
  showAvatar: props.config?.showAvatar !== false,
  placeholder: props.config?.placeholder || props.placeholder || SEND_CONSTANTS.DEFAULT_PLACEHOLDER,
  enableAutoScroll: props.config?.enableAutoScroll !== false,
  ...props.config,
}));

const visiblePrompts = computed(() => {
  if (!props.prompts) return [];
  return props.prompts.filter(p => p.show !== false);
});

const canSend = computed(() => {
  return !props.disabled && !isLoading.value && !uploading.value && (inputValue.value.trim() || fileList.value.length > 0);
});

const maxFileSize = computed(() => props.maxFileSize || SEND_CONSTANTS.MAX_SIZE_MB);
const maxFiles = computed(() => props.maxFiles || SEND_CONSTANTS.MAX_IMAGES);

const handleSend = async () => {
  if (!canSend.value) return;
  
  const messageContent = inputValue.value.trim();
  let processedFiles: FileItem[] = [];
  
  // 处理文件上传
  if (fileList.value.length > 0) {
    if (props.uploadFile || props.uploadConfig) {
      // 如果提供了上传方法或上传配置，执行上传
      uploading.value = true;
      try {
        const uploaded = await Promise.all(fileList.value.map(async (file) => {
          let res: string | UploadResponse;
          
          if (props.uploadFile) {
            // 优先使用自定义上传函数
            res = await props.uploadFile(file.originFileObj);
          } else if (props.uploadConfig) {
            // 使用上传配置
            res = await uploadFileWithConfig(
              file.originFileObj, 
              props.uploadConfig,
              props.onError
            );
          } else {
            throw new Error('未配置上传方式');
          }
          
          const url = typeof res === 'string' ? res : res.url;
          return {
            uid: file.uid,
            name: file.name,
            url,
            originFileObj: file.originFileObj,
            type: file.type,
            size: file.size,
            // 保留额外的响应数据
            ...(typeof res === 'object' ? res : {})
          };
        }));
        processedFiles = uploaded;
      } catch (e: any) {
        message.error(e?.message || '文件上传失败');
        props.onError?.(e?.message || '文件上传失败');
        uploading.value = false;
        return;
      }
      uploading.value = false;
    } else {
      // 如果没有上传方法或配置，直接使用本地文件的dataUrl
      processedFiles = [...fileList.value];
    }
  }
  
  // Call user's onSend callback
  props.onSend?.(messageContent, processedFiles);
  emit('send', messageContent, processedFiles);
  
  // Send message through chat system
  await sendMessage(messageContent, processedFiles);
  
  // Clear input and files
  inputValue.value = '';
  fileList.value = [];
  
  // Simulate receive callback
  setTimeout(() => {
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage && lastMessage.role === 'assistant') {
      props.onReceive?.(lastMessage.content);
      emit('receive', lastMessage.content);
    }
  }, 1100);
};

const handlePressEnter = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault();
    handleSend();
  }
};

const handleClear = () => {
  clearMessages();
  props.onClear?.();
  emit('clear');
};

const handlePromptClick = (prompt: PromptItem) => {
  if (props.disabled || isLoading.value) return;
  inputValue.value = prompt.content || prompt.label || '';
  handleSend();
};

const handleFileValidation = (files: FileList | File[]): { validFiles: File[], error: string | null } => {
  const filesArray = Array.from(files);
  const validFiles: File[] = [];
  
  // Check total file count
  if (fileList.value.length + filesArray.length > maxFiles.value) {
    return { validFiles: [], error: `最多只能上传${maxFiles.value}个文件` };
  }
  
  // Validate each file
  for (const file of filesArray) {
    // Check file type
    const isAllowed = SEND_CONSTANTS.ACCEPT.split(',').some(type => 
      file.type === type.trim()
    );
    if (!isAllowed) {
      const supportedList = Object.keys(SEND_CONSTANTS.TYPE_DESCRIPTIONS).map(
        type => SEND_CONSTANTS.TYPE_DESCRIPTIONS[type as keyof typeof SEND_CONSTANTS.TYPE_DESCRIPTIONS]
      ).join('、');
      return { validFiles: [], error: `仅支持以下格式：${supportedList}` };
    }
    
    // Check file size
    if (file.size > maxFileSize.value * 1024 * 1024) {
      return { validFiles: [], error: `文件大小不能超过${maxFileSize.value}MB` };
    }
    
    validFiles.push(file);
  }
  
  return { validFiles, error: null };
};

const processFiles = async (files: File[]) => {
  const newFiles: FileItem[] = [];
  
  for (const file of files) {
    // 使用FileReader读取文件为dataUrl，这样即使没有uploadFile也能显示图片
    const reader = new FileReader();
    const dataUrl = await new Promise<string>((resolve) => {
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.readAsDataURL(file);
    });
    
    newFiles.push({
      uid: Date.now() + Math.random() + '',
      name: file.name,
      url: dataUrl, // 使用dataUrl作为图片URL
      originFileObj: file,
      type: file.type,
      size: file.size
    });
  }
  
  fileList.value = [...fileList.value, ...newFiles];
};

const onFileSelected = async (e: Event) => {
  const target = e.target as HTMLInputElement;
  if (!target.files || target.files.length === 0) return;
  
  const { validFiles, error: validationError } = handleFileValidation(target.files);
  
  if (validationError) {
    message.error(validationError);
    props.onError?.(validationError);
    emit('error', validationError);
    return;
  }
  
  if (validFiles.length > 0) {
    await processFiles(validFiles);
  }
  
  // Reset input
  target.value = '';
};

const handleDragEnter = (e: DragEvent) => {
  if (!props.enableFileUpload || props.disabled) {
    return;
  }
  e.preventDefault();
  dragCounter.value++;
  dragActive.value = true;
};

const handleDragOver = (e: DragEvent) => {
  if (!props.enableFileUpload || props.disabled) {
    return;
  }
  e.preventDefault();
};

const handleDragLeave = (e: DragEvent) => {
  if (!props.enableFileUpload || props.disabled) {
    return;
  }
  e.preventDefault();
  dragCounter.value--;
  if (dragCounter.value === 0) {
    dragActive.value = false;
  }
};

const handleDrop = async (e: DragEvent) => {
  e.preventDefault();
  dragActive.value = false;
  dragCounter.value = 0; // 重置计数器
  
  if (!props.enableFileUpload || props.disabled || !e.dataTransfer?.files) return;
  
  const { validFiles, error: validationError } = handleFileValidation(e.dataTransfer.files);
  
  if (validationError) {
    message.error(validationError);
    props.onError?.(validationError);
    emit('error', validationError);
    return;
  }
  
  if (validFiles.length > 0) {
    await processFiles(validFiles);
  }
};

const handleRemoveFile = (uid: string) => {
  // 由于使用dataUrl，不需要revokeObjectURL
  fileList.value = fileList.value.filter(f => f.uid !== uid);
};

const isFileUploadDisabled = computed(() => {
  return props.disabled || isLoading.value || uploading.value || fileList.value.length >= maxFiles.value;
});

const formatTime = (timestamp?: number) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
};

// 获取文件图标
const getFileIcon = (fileType?: string) => {
  if (!fileType) return FileOutlined;
  
  if (SEND_CONSTANTS.SUPPORTED_TYPES.image.includes(fileType)) {
    return FileImageOutlined;
  }
  if (fileType === 'application/pdf') {
    return FilePdfOutlined;
  }
  if (fileType === 'text/plain') {
    return FileTextOutlined;
  }
  if (fileType.includes('word') || fileType.includes('document')) {
    return FileWordOutlined;
  }
  if (fileType.includes('excel') || fileType.includes('sheet')) {
    return FileExcelOutlined;
  }
  if (fileType.includes('powerpoint') || fileType.includes('presentation')) {
    return FilePptOutlined;
  }
  return FileOutlined;
};

// 判断是否为图片类型
const isImageFile = (fileType?: string) => {
  return fileType && SEND_CONSTANTS.SUPPORTED_TYPES.image.includes(fileType);
};

// 构建自定义渲染属性
const customRenderProps = computed<CustomRenderProps>(() => ({
  messages: messages.value,
  isLoading: isLoading.value,
  canSend: canSend.value,
  inputValue: inputValue.value,
  fileList: fileList.value,
  onUploadClick: () => fileInputRef.value?.click(),
  onClear: handleClear,
  onSend: handleSend,
  disabled: props.disabled || isLoading.value,
  formatTime,
}));

// Auto scroll to bottom when new messages arrive
watch(messages, () => {
  if (finalConfig.value.enableAutoScroll) {
    nextTick(() => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
      }
    });
  }
}, { deep: true });

// Cleanup on unmount (dataUrl不需要特殊清理)
onMounted(() => {
  return () => {
    // 使用dataUrl时不需要手动清理
    fileList.value = [];
  };
});
</script>

<style scoped>
/* Import base styles */
@import '../chat-component.css';

/* Additional Vue-specific styles */
.ai-chat-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  position: relative;
}

.ai-chat-content.dark {
  background: #1f1f1f;
}

/* Bubble styles */
.bubble-wrapper {
  display: flex;
  gap: 12px;
  max-width: 70%;
  animation: fadeInUp 0.3s ease-out;
}

.bubble-wrapper.bubble-user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.bubble-wrapper.bubble-assistant {
  align-self: flex-start;
}

.bubble-content {
  position: relative;
  flex: 1;
}

.bubble-text {
  padding: 10px 16px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.5;
  word-break: break-word;
}

.bubble-user .bubble-text {
  background-color: #1890ff;
  color: white;
  border-top-right-radius: 4px;
}

.bubble-assistant .bubble-text {
  background-color: #f0f2f5;
  color: #333;
  border-top-left-radius: 4px;
}

.bubble-assistant .message-timestamp {
  text-align: left;
}

.dark .bubble-assistant .bubble-text {
  background-color: #2f2f2f;
  color: #e0e0e0;
}

/* Loading animation */
.bubble-loading {
  padding: 10px 16px;
  background-color: #f0f2f5;
  border-radius: 18px;
  border-top-left-radius: 4px;
  display: flex;
  gap: 4px;
  align-items: center;
}

.dark .bubble-loading {
  background-color: #2f2f2f;
}

.bubble-loading span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #999;
  animation: loading 1.4s infinite;
}

.bubble-loading span:nth-child(1) {
  animation-delay: 0s;
}

.bubble-loading span:nth-child(2) {
  animation-delay: 0.2s;
}

.bubble-loading span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.5);
    opacity: 1;
  }
}

/* Prompts styles */
.prompts-container {
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
}

.dark .prompts-container {
  border-top-color: #434343;
}

.prompts-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.prompt-item {
  padding: 6px 12px;
  border-radius: 16px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
}

.prompt-item:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.dark .prompt-item {
  background: #262626;
  border-color: #434343;
  color: #ffffff;
}

.dark .prompt-item:hover {
  background: #003a8c;
  border-color: #1890ff;
  color: #91d5ff;
}

/* Sender input wrapper */
.sender-input-wrapper {
  position: relative;
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.sender-input-wrapper .ant-input-textarea {
  flex: 1;
}

.sender-actions {
  flex-shrink: 0;
  padding-bottom: 4px;
}

/* Override ant-design-vue textarea styles */
.sender-input-wrapper .ant-input-textarea-affix-wrapper {
  padding: 8px 12px;
  border-radius: 20px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.sender-input-wrapper .ant-input-textarea-affix-wrapper:hover {
  border-color: #40a9ff;
}

.sender-input-wrapper .ant-input-textarea-affix-wrapper:focus-within {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.dark .sender-input-wrapper .ant-input-textarea-affix-wrapper {
  background: #262626;
  border-color: #434343;
  color: #ffffff;
}

.dark .sender-input-wrapper .ant-input-textarea-affix-wrapper:hover {
  border-color: #177ddc;
}

.dark .sender-input-wrapper .ant-input-textarea-affix-wrapper:focus-within {
  border-color: #177ddc;
  box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
}

.sender-input-wrapper textarea {
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  color: inherit;
}
</style> 