<template>
  <div :class="['ai-chat-content', { dark: isDarkMode }]" @dragenter.prevent="handleDragEnter" @dragover.prevent="handleDragOver" @dragleave.prevent="handleDragLeave">
    <!-- 自定义头部插槽 -->
    <div v-if="$slots.header || renderHeader" class="chat-header-slot">
      <component v-if="renderHeader" :is="renderHeader(customRenderProps)" />
      <slot v-else name="header" :messages="messages" :isLoading="isLoading" />
    </div>
    
    <div v-if="dragActive" :key="`drag-${dragActive}-${dragCounter}`" class="send-drag-overlay" @drop.prevent="handleDrop">
      <div class="send-drag-icon">
        <cloud-upload-outlined />
      </div>
      <div class="send-drag-title">拖拽文件到这里</div>
      <div class="send-drag-desc">支持图片、文档、Office文件，最大{{ maxFileSize }}MB</div>
    </div>
    <input
      ref="fileInputRef"
      type="file"
      multiple
      :accept="SEND_CONSTANTS.ACCEPT"
      style="display: none"
      @change="handleFileSelect"
    />
    
    <!-- Error message handled by message component -->
    
    <!-- Messages container -->
    <div ref="messagesContainer" class="messages-container">
       <!-- 空状态插槽 -->
       <div v-if="messages.length === 0 && !isLoading" class="empty-state-slot">
         <component v-if="renderEmpty" :is="renderEmpty(customRenderProps)" />
         <slot v-else-if="$slots.empty" name="empty" />
       </div>
       
       <div v-for="message in messages" :key="message.id" :class="['bubble-wrapper', `bubble-${message.role}`]">
          <a-avatar v-if="finalConfig.showAvatar && message.role === 'user'" :src="avatarConfig?.user?.src" :icon="avatarConfig?.user?.icon">
            <template #icon><user-outlined /></template>
          </a-avatar>
          <a-avatar v-if="finalConfig.showAvatar && message.role === 'assistant'" :src="avatarConfig?.assistant?.src" :icon="avatarConfig?.assistant?.icon">
            <template #icon><robot-outlined /></template>
          </a-avatar>
          <div class="bubble-content">
            <!-- 自定义消息内容插槽 -->
            <component 
              v-if="renderMessage" 
              :is="renderMessage(message, customRenderProps)" 
            />
            <slot 
              v-else-if="$slots.message" 
              name="message" 
              :message="message" 
              :isLoading="isLoading"
              :formatTime="formatTime"
            />
            <div v-else>
              <!-- 文件显示 -->
              <div v-if="message.files && message.files.length > 0" class="message-files">
                <div v-for="file in message.files" :key="file.uid" class="message-file-item">
                  <img v-if="isImageFile(file.type)" :src="file.url" :alt="file.name" class="message-image" />
                  <div v-else class="message-file">
                    <div class="message-file-icon">
                      <component :is="getFileIcon(file.type)" />
                    </div>
                    <div class="message-file-info">
                      <div class="message-file-name" :title="file.name">
                        {{ file.name }}
                      </div>
                      <div class="message-file-type">
                        {{ SEND_CONSTANTS.TYPE_DESCRIPTIONS[file.type] || '未知文件' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 文本内容 -->
              <div v-if="message.content || (message.role === 'assistant' && isCurrentStreamingMessage(message))" class="bubble-text">
                
                <!-- 助手消息 -->
                <template v-if="message.role === 'assistant'">
                  <!-- 修复2：优化思考状态显示逻辑 -->
                  <div v-if="isStreamMode && isCurrentStreamingMessage(message) && (!message.content || message.content.trim() === '')" class="thinking-indicator">
                    <span class="thinking-dots">
                      <span></span><span></span><span></span>
                    </span>
                    <span class="thinking-text">思考中</span>
                  </div>
                  <!-- 有内容时显示MarkdownContent -->
                  <MarkdownContent 
                    v-else
                    :content="message.content || ''" 
                    :isRequesting="isStreamMode && isLoading && isCurrentStreamingMessage(message)"
                    :bubbleList="messages"
                    :enableCodeCopy="true"
                    :enableMath="true"
                  />
                </template>
                
                <!-- 用户消息 -->
                <template v-else>{{ message.content }}</template>
              </div>
              <div class="message-timestamp">{{ formatTime(message.timestamp) }}</div>
            </div>
            
            <!-- 消息操作插槽 -->
            <div v-if="$slots.messageActions || renderMessageActions" class="message-actions-slot">
              <component v-if="renderMessageActions" :is="renderMessageActions(message, customRenderProps)" />
              <slot v-else name="messageActions" :message="message" :isLoading="isLoading" />
            </div>
          </div>
       </div>
       <!-- 只有在非流式模式下才显示loading -->
       <div v-if="isLoading && !isStreamMode" class="bubble-wrapper bubble-assistant">
          <a-avatar v-if="finalConfig.showAvatar">
            <template #icon><robot-outlined /></template>
          </a-avatar>
          <div class="bubble-content">
            <!-- 自定义加载状态插槽 -->
            <component v-if="renderLoading" :is="renderLoading(customRenderProps)" />
            <slot v-else-if="$slots.loading" name="loading" />
            <div v-else class="bubble-loading">
              <span></span><span></span><span></span>
            </div>
          </div>
       </div>
    </div>
    
    <!-- Prompts section -->
    <div v-if="prompts && prompts.length > 0 && !isLoading" class="prompts-container">
      <div class="prompts-list">
        <div
          v-for="prompt in visiblePrompts"
          :key="prompt.key"
          class="prompt-item"
          @click="handlePromptClick(prompt)"
        >
          {{ prompt.title || prompt.content }}
        </div>
      </div>
    </div>
    
    <!-- Sender container -->
    <div class="sender-container">
      <!-- File preview header -->
      <div v-if="enableFileUpload && fileList && fileList.length > 0" class="send-header">
        <div class="send-file-preview__container">
          <div v-for="file in (fileList || [])" :key="file.uid" class="send-file-preview__item">
            <a-image v-if="isImageFile(file.type)" :src="file.url" :alt="file.name" class="send-file-preview__img" />
            <div v-else class="send-file-preview__file">
              <div class="send-file-preview__icon">
                <component :is="getFileIcon(file.type)" />
              </div>
              <div class="send-file-preview__info">
                <div class="send-file-preview__name" :title="file.name">
                  {{ file.name }}
                </div>
                <div class="send-file-preview__type">
                  {{ SEND_CONSTANTS.TYPE_DESCRIPTIONS[file.type] || '未知文件' }}
                </div>
              </div>
            </div>
            <a-button
              class="send-file-preview__btn"
              shape="circle"
              size="small"
              danger
              @click="handleRemoveFile(file.uid)"
            >
              <template #icon><close-outlined /></template>
            </a-button>
          </div>
        </div>
      </div>
      
      <!-- Input area -->
      <div class="sender-input-wrapper">
        <a-textarea
          v-model:value="localInputValue"
          :placeholder="finalConfig.placeholder || placeholder"
          :disabled="disabled || isLoading"
          :rows="1"
          :auto-size="{ minRows: 1, maxRows: 6 }"
          :maxlength="finalConfig.maxInputLength"
          @press-enter="handlePressEnter"
        />
        <div class="sender-actions">
          <!-- 自定义输入工具插槽 -->
          <div v-if="$slots.inputTools || renderInputTools" class="input-tools-slot">
            <component v-if="renderInputTools" :is="renderInputTools(customRenderProps)" />
            <slot 
              v-else
              name="inputTools" 
              :fileList="fileList"
              :canSend="canSend"
              :isLoading="isLoading"
              :disabled="disabled"
              :onUploadClick="() => fileInputRef?.click()"
              :onClear="handleClear"
              :onSend="handleSend"
            />
          </div>
          <a-space v-else>
            <a-tooltip v-if="enableFileUpload" title="上传文件">
              <a-button
                type="text"
                :disabled="isFileUploadDisabled || uploading"
                @click="() => fileInputRef?.click()"
              >
                <template #icon><file-image-outlined /></template>
              </a-button>
            </a-tooltip>
            <a-tooltip title="清空对话">
              <a-button
                type="text"
                :disabled="disabled || isLoading || messages.length === 0"
                @click="handleClear"
              >
                <template #icon><clear-outlined /></template>
              </a-button>
            </a-tooltip>
            <a-button
              type="primary"
              :loading="uploading"
              :disabled="!canSend && !isLoading"
              @click="isLoading ? handlePause() : handleSend()"
            >
              <template #icon>
                <pause-circle-outlined v-if="isLoading" />
                <send-outlined v-else />
              </template>
            </a-button>
          </a-space>
        </div>
      </div>
    </div>
    
    <!-- 自定义底部插槽 -->
    <div v-if="$slots.footer || renderFooter" class="chat-footer-slot">
      <component v-if="renderFooter" :is="renderFooter(customRenderProps)" />
      <slot 
        v-else
        name="footer" 
        :messages="messages" 
        :isLoading="isLoading" 
        :canSend="canSend"
        :inputValue="localInputValue"
        :fileList="fileHandler.fileList.value"
      />
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch, nextTick, defineComponent } from 'vue'
import { Avatar, Button, Tooltip, Space, Input, message, Image } from 'ant-design-vue'
import { UserOutlined, RobotOutlined, FileImageOutlined, ClearOutlined, CloseOutlined, SendOutlined, PauseCircleOutlined } from '@ant-design/icons-vue'
import MarkdownContent from './MarkdownContent.vue'
import { uploadFileWithConfig } from '../utils/upload'
import { useVueChat } from '../composables/useVueChat'
import { useFileHandler } from '../composables/useFileHandler'
import { useInputHandler } from '../composables/useInputHandler'

const { TextArea } = Input

export default defineComponent({
  name: 'ChatContent',
  components: {
    'a-avatar': Avatar,
    'a-button': Button,
    'a-tooltip': Tooltip,
    'a-space': Space,
    'a-textarea': TextArea,
    'a-image': Image,
    'user-outlined': UserOutlined,
    'robot-outlined': RobotOutlined,
    'file-image-outlined': FileImageOutlined,
    'clear-outlined': ClearOutlined,
    'close-outlined': CloseOutlined,
    'send-outlined': SendOutlined,
    'pause-circle-outlined': PauseCircleOutlined,
    MarkdownContent
  },
  props: {
    messages: { type: Array, default: () => [] },
    isLoading: { type: Boolean, default: false },
    canSend: { type: Boolean, default: true },
    inputValue: { type: String, default: '' },
    fileList: { type: Array, default: () => [] },
    prompts: { type: Array, default: () => [] },
    avatarConfig: { type: Object, default: () => ({}) },
    placeholder: { type: String, default: '请输入您的消息...' },
    disabled: { type: Boolean, default: false },
    renderHeader: { type: Function, default: undefined },
    renderEmpty: { type: Function, default: undefined },
    renderMessage: { type: Function, default: undefined },
    renderMessageActions: { type: Function, default: undefined },
    renderLoading: { type: Function, default: undefined },
    renderInputTools: { type: Function, default: undefined },
    renderFooter: { type: Function, default: undefined },
    config: { type: Object, default: () => ({}) },
    baseUrl: { type: String, default: undefined },
    showHistory: { type: Boolean, default: false },
    sessionId: { type: String, default: 'default' },
    enableFileUpload: { type: Boolean, default: false },
    maxFileSize: { type: Number, default: 10 },
    maxFiles: { type: Number, default: 9 },
    uploadFile: { type: Function, default: undefined },
    uploadConfig: { type: Object, default: undefined },
    onError: { type: Function, default: undefined },
    onSend: { type: Function, default: undefined },
    onReceive: { type: Function, default: undefined },
    onClear: { type: Function, default: undefined },
    className: { type: String, default: '' }
  },
  emits: ['send', 'remove-file', 'error', 'clear', 'receive'],
  setup(props, { emit }) {
    // 初始化真实的 useVueChat hook
    const chatOptions = {
      apiEndpoint: props.config?.apiEndpoint || '/api/chat',
      baseUrl: props.baseUrl || props.config?.baseUrl, // 新增：支持baseUrl
      headers: props.config?.headers || {},
      timeout: props.config?.timeout || 120000,
      showHistory: props.showHistory ?? props.config?.showHistory ?? false,
      sessionId: props.sessionId || props.config?.sessionId || 'default-session',
      autoSave: props.config?.autoSave ?? true,
      maxMessages: props.config?.maxMessages || 100,
      // 重要：传递 requestMapping 配置
      requestMapping: props.config?.requestMapping,
      // 新增：传递 stream 配置
      stream: props.config?.stream ?? true, // 默认启用流式渲染
      // 优化：通过chat engine回调处理事件
      onSend: (content, files) => {
        // 触发组件事件
        if (props.onSend) props.onSend(content, files)
        emit('send', content, files)
      },
      onReceive: (content, message) => {
        // 触发组件接收事件
        if (props.onReceive) props.onReceive(content)
        emit('receive', content)
      },
      onError: (error) => {
        // 触发组件错误事件
        if (props.onError) props.onError(error)
        emit('error', error)
      }
    }
    
    const { messages, isLoading, error, sendMessage, clearMessages, abortRequest } = useVueChat(chatOptions)
    
    // 监听错误变化，使用 message 提示
    watch(error, (newError) => {
      if (newError) {
        message.error(newError)
      }
    })
    const messagesContainer = ref(null)
    const fileInputRef = ref(null)
    // 修复：确保响应式状态正确初始化
    const localInputValue = ref('')
    const localFileList = ref([])
    const uploading = ref(false)

    // 修复：初始化本地状态
    if (props.inputValue) {
      localInputValue.value = props.inputValue
    }
    if (props.fileList && props.fileList.length > 0) {
      localFileList.value = [...props.fileList]
    }

    // 使用文件处理composable
    const fileHandler = useFileHandler(localFileList, {
      maxFiles: props.maxFiles,
      maxFileSize: props.maxFileSize,
      onError: (error) => {
        if (props.onError) props.onError(error)
        emit('error', error)
      }
    })

    // handleSend需要先声明，因为inputHandler需要引用它
    const handleSend = async () => {      
      const messageContent = localInputValue.value.trim()
      let processedFiles = []
      const fileList = fileHandler.fileList
      
      // 修复：如果既没有文本也没有文件，不执行发送
      if (!messageContent && (!fileList.value || fileList.value.length === 0)) {
        return
      }
      
      // 处理文件上传逻辑
      if (fileList.value && fileList.value.length > 0) {
        if (props.uploadFile || props.uploadConfig) {
          // 先上传文件，再发送消息
          uploading.value = true
          try {
            const uploaded = await Promise.all(fileList.value.map(async (file) => {
              let res
              
              if (props.uploadFile) {
                // 优先使用自定义上传函数
                res = await props.uploadFile(file.originFileObj)
              } else if (props.uploadConfig) {
                // 使用上传配置，支持baseUrl
                const uploadConfigWithBaseUrl = {
                  ...props.uploadConfig,
                  baseUrl: props.baseUrl || props.config?.baseUrl // 传递baseUrl
                }
                res = await uploadFileWithConfig(
                  file.originFileObj, 
                  uploadConfigWithBaseUrl,
                  props.onError
                )
              } else {
                throw new Error('未配置上传方式')
              }
              
              const url = typeof res === 'string' ? res : res.url
              return {
                uid: file.uid,
                name: file.name,
                url,
                originFileObj: file.originFileObj,
                type: file.type,
                size: file.size,
                // 保留额外的响应数据
                ...(typeof res === 'object' ? res : {})
              }
            }))
            processedFiles = uploaded
          } catch (e) {
            message.error(e?.message || '文件上传失败')
            if (props.onError) props.onError(e?.message || '文件上传失败')
            uploading.value = false
            return
          }
          uploading.value = false
        } else {
          // 如果没有上传方法或配置，直接使用本地文件的dataUrl
          processedFiles = [...fileList.value]
        }
      }
      
      // 触发组件事件
      if (props.onSend) props.onSend(messageContent, processedFiles)
      emit('send', messageContent, processedFiles)
      
      try {
        // 清空输入框和文件列表（在发送消息前清空，避免用户重复点击）
        localInputValue.value = ''
        fileHandler.fileList.value = []
        
        // 使用真实的聊天引擎发送消息 (支持流式传输)
        await sendMessage(messageContent, processedFiles)
        
      } catch (e) {
        // 发送失败的错误处理已经在 useVueChat 中处理
        console.error('发送消息失败:', e)
        // 发送失败时恢复输入内容
        localInputValue.value = messageContent
      }
    }

    // 封装拖拽事件处理方法
    const handleDragEnter = (e) => {
      fileHandler.handleDragEnter(e, props.enableFileUpload, props.disabled)
    }
    
    const handleDragOver = (e) => {
      fileHandler.handleDragOver(e, props.enableFileUpload, props.disabled)
    }
    
    const handleDragLeave = (e) => {
      fileHandler.handleDragLeave(e, props.enableFileUpload, props.disabled)
    }
    
    const handleDrop = (e) => {
      fileHandler.handleDrop(e, props.enableFileUpload, props.disabled)
    }

    // 使用输入处理composable
    const inputHandler = useInputHandler(localInputValue, {
      onSend: handleSend
    })

    // 监听props变化，同步到本地状态
    watch(() => props.inputValue, (newValue) => {
      if (newValue !== undefined) {
        localInputValue.value = newValue
      }
    }, { immediate: true })
    
    watch(() => props.fileList, (newValue) => {
      if (newValue !== undefined) {
        localFileList.value = newValue || []
      }
    }, { immediate: true })

    const isDarkMode = computed(() => props.config?.theme === 'dark' || props.className?.includes('dark'))
    const finalConfig = computed(() => ({
      showAvatar: props.config?.showAvatar !== false,
      placeholder: props.config?.placeholder || props.placeholder || '请输入您的消息...',
      enableAutoScroll: props.config?.enableAutoScroll !== false,
      ...props.config,
    }))
    
    // 添加流式渲染模式的计算属性
    const isStreamMode = computed(() => props.config?.stream ?? true)
    
    // 修复2：优化当前流式消息的判断逻辑
    const isCurrentStreamingMessage = (message) => {
      if (!isStreamMode.value || message.role !== 'assistant') {
        return false
      }
      
      // 找到最后一条助手消息
      const assistantMessages = messages.value.filter(m => m.role === 'assistant')
      const lastAssistantMessage = assistantMessages[assistantMessages.length - 1]
      
      // 如果是最后一条助手消息，且正在加载中，或者消息内容为空
      return lastAssistantMessage && lastAssistantMessage.id === message.id && 
             (isLoading.value || !message.content || message.content.trim() === '')
    }

    const visiblePrompts = computed(() => {
      if (!props.prompts) return []
      return props.prompts.filter(p => p.show !== false)
    })

    const canSend = computed(() => {
      const hasTextInput = localInputValue.value && localInputValue.value.trim().length > 0
      const hasFiles = fileHandler.fileList.value && fileHandler.fileList.value.length > 0
      const isNotDisabled = !props.disabled && !isLoading.value && !uploading.value
      const result = isNotDisabled && (hasTextInput || hasFiles)

      return result
    })

    const maxFileSize = computed(() => props.maxFileSize || fileHandler.SEND_CONSTANTS.MAX_SIZE_MB)
    const maxFiles = computed(() => props.maxFiles || fileHandler.SEND_CONSTANTS.MAX_IMAGES)

    const handlePause = () => {
      // 暂停当前请求，但保留已返回的内容
      abortRequest()
      // 注意：这里不清空消息，保留已返回的内容
    }

    const handleClear = () => {
      clearMessages()
      if (props.onClear) props.onClear()
      emit('clear')
    }

    const handlePromptClick = (prompt) => {
      if (props.disabled || isLoading.value) return
      localInputValue.value = prompt.content || prompt.label || ''
      handleSend()
    }

    const isFileUploadDisabled = computed(() => {
      return props.disabled || isLoading.value || uploading.value || (fileHandler.fileList.value && fileHandler.fileList.value.length >= maxFiles.value)
    })

    const customRenderProps = computed(() => ({
      messages: messages.value,
      isLoading: isLoading.value,
      canSend: canSend.value,
      inputValue: localInputValue.value,
      fileList: fileHandler.fileList.value,
      onUploadClick: () => fileInputRef.value?.click(),
      onClear: handleClear,
      onSend: handleSend,
      disabled: props.disabled || isLoading.value,
      formatTime: (timestamp) => {
        if (!timestamp) return ''
        const date = new Date(timestamp)
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
      },
    }))

    watch(messages, () => {
      if (finalConfig.value.enableAutoScroll) {
        nextTick(() => {
          if (messagesContainer.value) {
            messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
          }
        })
      }
    }, { deep: true })

    onMounted(() => {
      // 确保组件卸载时清理文件列表
      return () => {
        if (localFileList.value) {
          localFileList.value = []
        }
      }
    })

    return {
      // Data
      messages,
      isLoading,
      error,
      messagesContainer,
      fileInputRef,
      localInputValue,
      fileList: fileHandler.fileList,
      // 解构 fileHandler 以确保响应性
      dragActive: fileHandler.dragActive,
      dragCounter: fileHandler.dragCounter,
      uploading,
      
      // Computed
      isDarkMode,
      finalConfig,
      visiblePrompts,
      canSend,
      maxFileSize,
      maxFiles,
      isFileUploadDisabled,
      customRenderProps,
      isStreamMode, // 添加流式渲染模式
      
      // Methods
      handleSend,
      handlePause,
      handlePressEnter: inputHandler.handlePressEnter,
      handleClear,
      handlePromptClick,
      handleFileValidation: fileHandler.validateFiles,
      processFiles: fileHandler.processFiles,
      handleFileSelect: fileHandler.handleFileSelect,
      handleDragEnter,
      handleDragOver,
      handleDragLeave,
      handleDrop,
      handleRemoveFile: fileHandler.removeFile,
      formatTime: (timestamp) => {
        if (!timestamp) return ''
        const date = new Date(timestamp)
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        return `${hours}:${minutes}`
      },
      getFileIcon: fileHandler.getFileIcon,
      isImageFile: fileHandler.isImageFile,
      isCurrentStreamingMessage: (message) => {
        if (!isStreamMode.value || message.role !== 'assistant') {
          return false
        }
        
        // 找到最后一条助手消息
        const assistantMessages = messages.value.filter(m => m.role === 'assistant')
        const lastAssistantMessage = assistantMessages[assistantMessages.length - 1]
        
        // 如果是最后一条助手消息，且正在加载中，或者消息内容为空
        return lastAssistantMessage && lastAssistantMessage.id === message.id && 
               (isLoading.value || !message.content || message.content.trim() === '')
      },
      abortRequest, // 新增：取消请求的方法
      
      // Constants
      SEND_CONSTANTS: fileHandler.SEND_CONSTANTS
    }
  }
})
</script>

<style scoped>
/* Additional Vue-specific styles */
.ai-chat-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  position: relative;
}

.ai-chat-content.dark {
  background: #1f1f1f;
}

/* Bubble styles */
.bubble-wrapper {
  display: flex;
  gap: 12px;
  max-width: 70%;
  animation: fadeInUp 0.3s ease-out;
}

.bubble-wrapper.bubble-user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.bubble-wrapper.bubble-assistant {
  align-self: flex-start;
}

.bubble-content {
  position: relative;
  flex: 1;
}

.bubble-text {
  padding: 10px 16px;
  border-radius: 18px;
  font-size: 15px;
  line-height: 1.5;
  word-break: break-word;
}

.bubble-user .bubble-text {
  background-color: #1890ff;
  color: white;
  border-top-right-radius: 4px;
}

.bubble-assistant .bubble-text {
  background-color: #f0f2f5;
  color: #333;
  border-top-left-radius: 4px;
}

.bubble-assistant .message-timestamp {
  text-align: left;
}

.dark .bubble-assistant .bubble-text {
  background-color: #2f2f2f;
  color: #e0e0e0;
}

/* Loading animation */
.bubble-loading {
  padding: 10px 16px;
  background-color: #f0f2f5;
  border-radius: 18px;
  border-top-left-radius: 4px;
  display: flex;
  gap: 4px;
  align-items: center;
}

.dark .bubble-loading {
  background-color: #2f2f2f;
}

.bubble-loading span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #999;
  animation: loading 1.4s infinite;
}

.bubble-loading span:nth-child(1) {
  animation-delay: 0s;
}

.bubble-loading span:nth-child(2) {
  animation-delay: 0.2s;
}

.bubble-loading span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes loading {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.5);
    opacity: 1;
  }
}

/* Prompts styles */
.prompts-container {
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
}

.dark .prompts-container {
  border-top-color: #434343;
}

.prompts-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.prompt-item {
  padding: 6px 12px;
  border-radius: 16px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none;
}

.prompt-item:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.dark .prompt-item {
  background: #262626;
  border-color: #434343;
  color: #ffffff;
}

.dark .prompt-item:hover {
  background: #003a8c;
  border-color: #1890ff;
  color: #91d5ff;
}

/* Sender input wrapper */
.sender-input-wrapper {
  position: relative;
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.sender-input-wrapper .ant-input-textarea {
  flex: 1;
}

.sender-actions {
  flex-shrink: 0;
  padding-bottom: 4px;
}

/* 思考指示器样式 */
.thinking-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  color: #666;
  font-size: 14px;
}

.thinking-dots {
  display: flex;
  gap: 4px;
}

.thinking-dots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #999;
  animation: thinking 1.4s infinite;
}

.thinking-dots span:nth-child(1) {
  animation-delay: 0s;
}

.thinking-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.thinking-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

.thinking-text {
  font-size: 13px;
  opacity: 0.7;
}

@keyframes thinking {
  0%, 60%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.3);
    opacity: 1;
  }
}

.dark .thinking-indicator {
  color: #ccc;
}

.dark .thinking-dots span {
  background-color: #666;
}

/* Override ant-design-vue textarea styles */
.sender-input-wrapper .ant-input-textarea-affix-wrapper {
  padding: 8px 12px;
  border-radius: 20px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.sender-input-wrapper .ant-input-textarea-affix-wrapper:hover {
  border-color: #40a9ff;
}

.sender-input-wrapper .ant-input-textarea-affix-wrapper:focus-within {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.dark .sender-input-wrapper .ant-input-textarea-affix-wrapper {
  background: #262626;
  border-color: #434343;
  color: #ffffff;
}

.dark .sender-input-wrapper .ant-input-textarea-affix-wrapper:hover {
  border-color: #177ddc;
}

.dark .sender-input-wrapper .ant-input-textarea-affix-wrapper:focus-within {
  border-color: #177ddc;
  box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
}

.sender-input-wrapper textarea {
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  color: inherit;
}
</style> 