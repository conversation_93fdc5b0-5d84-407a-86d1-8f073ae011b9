<template>
  <transition
    name="chat-dialog"
    @enter="onEnter"
    @leave="onLeave"
    @after-enter="onAfterEnter"
    @after-leave="onAfterLeave"
  >
    <div v-if="isVisible" class="chat-dialog-overlay" :class="className">
      <div
        ref="dialogRef"
        class="chat-dialog"
        :class="{ animating }"
        :style="dialogStyle"
        @click.stop
      >
        <!-- 对话框头部 -->
        <div class="chat-dialog-header">
          <span class="chat-dialog-title">{{ config?.title || 'AI助手' }}</span>
          <button class="chat-dialog-close" @click="onClose">
            <svg viewBox="0 0 1024 1024" width="16" height="16">
              <path d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3.1-3.6-7.6-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 0 0 203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3.1 3.6 7.6 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"/>
            </svg>
          </button>
        </div>
        
        <!-- 对话框内容 -->
        <div class="chat-dialog-content">
          <slot />
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, type PropType } from 'vue';
import type { ChatDialogConfig } from '../../types';

const props = defineProps({
  isVisible: { type: Boolean, default: false },
  position: { type: [Object, undefined] as PropType<{ x: number; y: number; side: 'left' | 'right' } | undefined>, default: () => ({ x: 0, y: 0, side: 'left' }) },
  config: { type: [Object, undefined] as PropType<Partial<ChatDialogConfig> | undefined>, default: () => ({}) },
  className: { type: String, default: '' }
});

const emit = defineEmits(['close']);

const dialogRef = ref<HTMLDivElement>();
const animating = ref(false);

const TRIGGER_SIZE = 60;
const ANIMATION_DURATION = 350;

// 计算对话框位置和尺寸
const dialogPosition = computed(() => {
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  };
  const dialogWidth = props.config?.width || 380;
  const dialogHeight = props.config?.height || 600;
  const margin = 20;
  
  let x = props.position.x;
  let y = props.position.y;
  
  if (props.position.side === 'left') {
    x = props.position.x + TRIGGER_SIZE + margin;
  } else {
    x = props.position.x - dialogWidth - margin;
  }
  
  // 边界约束
  if (x < margin) x = margin;
  if (x + dialogWidth > viewport.width - margin) x = viewport.width - dialogWidth - margin;
  if (y + dialogHeight > viewport.height - margin) y = viewport.height - dialogHeight - margin;
  if (y < margin) y = margin;
  
  return { x, y, width: dialogWidth, height: dialogHeight };
});

// 触发器位置
const triggerRect = computed(() => ({
  x: props.position.x,
  y: props.position.y,
  width: TRIGGER_SIZE,
  height: TRIGGER_SIZE,
}));

const dialogStyle = ref<any>({});

// 动画控制
const onEnter = (el: Element) => {
  animating.value = true;
  const element = el as HTMLElement;
  
  // 初始状态：从触发器位置开始
  element.style.left = `${triggerRect.value.x}px`;
  element.style.top = `${triggerRect.value.y}px`;
  element.style.width = `${triggerRect.value.width}px`;
  element.style.height = `${triggerRect.value.height}px`;
  element.style.borderRadius = '50%';
  element.style.opacity = '1';
  element.style.transform = 'scale(1)';
  element.style.transition = 'none';
  
  nextTick(() => {
    // 动画到最终位置
    element.style.left = `${dialogPosition.value.x}px`;
    element.style.top = `${dialogPosition.value.y}px`;
    element.style.width = `${dialogPosition.value.width}px`;
    element.style.height = `${dialogPosition.value.height}px`;
    element.style.borderRadius = '16px';
    element.style.opacity = '1';
    element.style.transform = 'scale(1)';
    element.style.transition = `all ${ANIMATION_DURATION}ms cubic-bezier(0.34, 1.56, 0.64, 1)`;
  });
};

const onAfterEnter = () => {
  animating.value = false;
};

const onLeave = (el: Element) => {
  animating.value = true;
  const element = el as HTMLElement;
  
  // 当前位置
  element.style.left = `${dialogPosition.value.x}px`;
  element.style.top = `${dialogPosition.value.y}px`;
  element.style.width = `${dialogPosition.value.width}px`;
  element.style.height = `${dialogPosition.value.height}px`;
  element.style.borderRadius = '16px';
  element.style.opacity = '1';
  element.style.transform = 'scale(1)';
  element.style.transition = 'none';
  
  nextTick(() => {
    // 收缩到触发器位置
    element.style.left = `${triggerRect.value.x}px`;
    element.style.top = `${triggerRect.value.y}px`;
    element.style.width = `${triggerRect.value.width}px`;
    element.style.height = `${triggerRect.value.height}px`;
    element.style.borderRadius = '50%';
    element.style.opacity = '0.7';
    element.style.transform = 'scale(0.95)';
    element.style.transition = `all ${ANIMATION_DURATION}ms cubic-bezier(0.34, 1.56, 0.64, 1)`;
  });
};

const onAfterLeave = () => {
  animating.value = false;
};

const onClose = () => {
  emit('close');
};
</script>

<style scoped>
.chat-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9998;
  pointer-events: none;
}

.chat-dialog {
  position: absolute;
  inset: 0;
  background: white;
  border-radius: 16px;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.18);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  pointer-events: auto;
}

.chat-dialog.animating {
  pointer-events: none;
}

.chat-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.chat-dialog-title {
  font-weight: 600;
  font-size: 16px;
}

.chat-dialog-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.chat-dialog-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.chat-dialog-close svg {
  fill: currentColor;
}

.chat-dialog-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 过渡动画 */
.chat-dialog-enter-active,
.chat-dialog-leave-active {
  transition: opacity 0.3s ease;
}

.chat-dialog-enter-from,
.chat-dialog-leave-to {
  opacity: 0;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .chat-dialog {
    background: #1f1f1f;
    color: #fff;
  }
  
  .chat-dialog-header {
    border-bottom-color: #333;
  }
}
</style>