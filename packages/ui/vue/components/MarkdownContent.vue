<template>
  <div 
    ref="contentRef"
    :class="['markdown-body', className]"
    @click="onContentClick"
  >
    <div v-html="parsedContent"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick, type PropType } from 'vue';
import type { MarkdownContentProps } from '../types';
import MarkdownIt from 'markdown-it';
import markdownItHighlight from 'markdown-it-highlightjs';
import markdownItAnchor from 'markdown-it-anchor';
import markdownItTocDoneRight from 'markdown-it-toc-done-right';
import markdownItKatex from '@traptitech/markdown-it-katex';
import * as echarts from 'echarts';
import hljs from 'highlight.js';
import { message } from 'ant-design-vue';
import 'highlight.js/styles/github.css';
import 'katex/dist/katex.min.css';

const props = defineProps({
  content: { type: String, default: '' },
  bubbleList: { type: Array as PropType<any[]>, default: () => [] },
  isRequesting: { type: Boolean, default: false },
  isCancel: { type: Boolean, default: false },
  enableCodeCopy: { type: Boolean, default: true },
  enableMath: { type: Boolean, default: true },
  enableToc: { type: Boolean, default: false },
  onContentClick: { type: [Function, undefined] as PropType<((e: MouseEvent) => void) | undefined>, default: undefined },
  className: { type: String, default: '' }
});

const contentRef = ref<HTMLDivElement>();
const copiedId = ref<string | null>(null);
const chartInstances = ref<Map<string, echarts.ECharts>>(new Map());

// 创建 Markdown-it 实例
const createMarkdownRenderer = () => {
  const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    breaks: true,
    highlight: (str: string, lang: string) => {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(str, { language: lang }).value;
        } catch (_) {}
      }
      return '';
    }
  });

  // 添加插件
  md.use(markdownItHighlight);
  md.use(markdownItKatex, {
    throwOnError: false,
    errorColor: '#cc0000'
  });
  md.use(markdownItAnchor, {
    permalink: false
  });
  md.use(markdownItTocDoneRight);

  return md;
};

const md = createMarkdownRenderer();

// 自定义代码块渲染规则
const originalFenceRule = md.renderer.rules.fence;
md.renderer.rules.fence = (tokens, idx, options, env, renderer) => {
  const token = tokens[idx];
  const [lang] = token.info.split(' ');
  const isEnd = env.isEnd || false;
  
  if (lang === 'echarts') {
    try {
      const jsonStr = token.content.trim();
      const chartOptions = JSON.parse(jsonStr);
      const encodedOptions = encodeURIComponent(jsonStr);
      const uniqueId = `echarts-${Date.now()}-${idx}`;
      return `<div class="echarts-container" id="${uniqueId}" data-options="${encodedOptions}"></div>`;
    } catch (error: any) {
      if (isEnd) {
        return `<div class="echarts-error">图表渲染失败</div>`;
      }
      return `<div class="echarts-loading">[...正在加载图表]</div>`;
    }
  }

  // 使用原始规则渲染普通代码块，并添加复制按钮
  const originalRender = originalFenceRule ? originalFenceRule(tokens, idx, options, env, renderer) : `<pre><code>${token.content}</code></pre>`;
  const uniqueId = `code-block-${Date.now()}-${idx}`;
  return `<div class="code-block-wrapper" id="${uniqueId}">
    ${originalRender}
    <button class="copy-button" data-code-id="${uniqueId}" title="复制代码">
      <svg class="copy-icon" viewBox="64 64 896 896" width="1em" height="1em" fill="currentColor">
        <path d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"/>
      </svg>
    </button>
  </div>`;
};

// 判断是否渲染结束
const isEnd = computed(() => {
  if (props.bubbleList.length === 0) return !props.isRequesting;
  return (
    (props.bubbleList[props.bubbleList.length - 1]?.message === props.content && !props.isRequesting) ||
    props.isCancel
  );
});

// 渲染 Markdown 内容
const parsedContent = computed(() => {
  const cleanContent = props.content.replace('[INVALID]', '');
  return md.render(cleanContent, { isEnd: isEnd.value });
});

// 处理代码复制
const handleCopyClick = async (e: MouseEvent) => {
  if (!props.enableCodeCopy) return;

  const target = e.target as HTMLElement;
  const button = target.closest('.copy-button') as HTMLButtonElement;
  if (!button) return;

  const codeId = button.getAttribute('data-code-id');
  if (!codeId) return;

  const codeBlock = document.getElementById(codeId);
  const codeElement = codeBlock?.querySelector('code');
  if (!codeElement) return;

  try {
    await navigator.clipboard.writeText(codeElement.textContent || '');
    copiedId.value = codeId;
    message.success('代码已复制');
    setTimeout(() => {
      copiedId.value = null;
    }, 2000);
  } catch (error) {
    message.error('复制失败');
  }
};

// 渲染ECharts图表
const renderECharts = () => {
  if (!contentRef.value) return;

  const containers = contentRef.value.querySelectorAll('.echarts-container');
  containers.forEach((container) => {
    const chartId = container.id;
    const encodedOptions = container.getAttribute('data-options');
    
    if (!encodedOptions || chartInstances.value.has(chartId)) return;

    try {
      const options = JSON.parse(decodeURIComponent(encodedOptions));
      const chartInstance = echarts.init(container as HTMLDivElement);
      
      // 设置默认配置
      let tempOption = options;
      if (tempOption.title && !tempOption.title.textStyle) {
        tempOption.title.textStyle = {
          overflow: 'break',
          width: 300,
        };
      }
      if (!tempOption.grid) {
        tempOption.grid = {
          containLabel: true,
        };
      }
      if (!tempOption.barMaxWidth) {
        tempOption.barMaxWidth = 30;
      }

      if (
        tempOption.series &&
        tempOption.series[0]?.type === 'line' &&
        tempOption.yAxis?.type === 'value'
      ) {
        tempOption.yAxis = {
          ...tempOption.yAxis,
          min: function (value: any) {
            return Math.floor((value.min * 0.9) / 10) * 10;
          },
        };
      }

      chartInstance.setOption(tempOption);
      chartInstances.value.set(chartId, chartInstance);

      // 响应式调整
      const resizeHandler = () => chartInstance.resize();
      window.addEventListener('resize', resizeHandler);
      
      // 存储清理函数
      (container as any)._resizeHandler = resizeHandler;
    } catch (error) {
      console.error('图表渲染失败', error);
      container.innerHTML = '<div class="echarts-error">图表渲染失败</div>';
    }
  });
};

// 监听内容变化，重新渲染图表
watch(() => parsedContent.value, () => {
  // 清理旧的图表实例
  chartInstances.value.forEach((instance, id) => {
    const container = document.getElementById(id);
    if (container && (container as any)._resizeHandler) {
      window.removeEventListener('resize', (container as any)._resizeHandler);
    }
    instance.dispose();
  });
  chartInstances.value.clear();

  // 使用nextTick确保DOM更新后再渲染图表
  nextTick(() => {
    renderECharts();
  });
});

// 组件挂载时绑定事件
onMounted(() => {
  if (contentRef.value && props.enableCodeCopy) {
    contentRef.value.addEventListener('click', handleCopyClick);
  }
  renderECharts();
});

// 组件卸载时清理
onUnmounted(() => {
  if (contentRef.value && props.enableCodeCopy) {
    contentRef.value.removeEventListener('click', handleCopyClick);
  }
  
  // 清理所有图表实例
  chartInstances.value.forEach((instance, id) => {
    const container = document.getElementById(id);
    if (container && (container as any)._resizeHandler) {
      window.removeEventListener('resize', (container as any)._resizeHandler);
    }
    instance.dispose();
  });
  chartInstances.value.clear();
});
</script>

<style scoped>
@import '../../../../src/shared/ui/components/markdown-content/index.less';

/* Vue特定的样式覆盖 */
.markdown-body :deep(.echarts-container) {
  width: 100%;
  height: 400px;
  margin: 16px 0;
}

.markdown-body :deep(.code-block-wrapper) {
  position: relative;
  margin-bottom: 16px;
}

.markdown-body :deep(.code-block-wrapper:hover .copy-button) {
  opacity: 1;
}

.markdown-body :deep(.copy-button) {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.markdown-body :deep(.copy-button:hover) {
  background: #f3f4f6;
}

.markdown-body :deep(.copy-icon) {
  display: block;
  width: 14px;
  height: 14px;
}
</style>