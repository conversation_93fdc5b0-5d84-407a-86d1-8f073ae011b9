<template>
  <div
    ref="triggerRef"
    :class="['floating-trigger', { dragging: isDragging }, className]"
    :style="triggerStyle"
    @click="onClick"
    @mousedown.prevent="onDragStart"
  >
    <!-- 自定义图标插槽 -->
    <slot name="icon">
      <img v-if="typeof config.icon === 'string'" :src="config.icon" alt="Chat" class="floating-icon-img" />
      <component v-else-if="config.icon" :is="config.icon" />
      <div v-else class="floating-default-icon">💬</div>
    </slot>
    
    <!-- 未读消息徽章 -->
    <div v-if="unreadCount > 0" class="floating-badge">
      {{ unreadCount > 99 ? '99+' : unreadCount }}
    </div>
    
    <!-- 脉冲动画 -->
    <div class="floating-pulse"></div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, type PropType } from 'vue';
import type { FloatingConfig } from '../types';

const props = defineProps({
  config: { type: [Object, undefined] as PropType<Partial<FloatingConfig> | undefined>, default: () => ({}) },
  unreadCount: { type: Number, default: 0 },
  position: { type: [Object, undefined] as PropType<{ x: number; y: number; side: 'left' | 'right' } | undefined>, default: () => ({ x: 0, y: 0, side: 'left' }) },
  isDragging: { type: Boolean, default: false },
  className: { type: String, default: '' }
});

const emit = defineEmits(['click', 'dragStart']);

const triggerRef = ref<HTMLDivElement>();

const triggerStyle = computed(() => ({
  left: `${props.position.x}px`,
  top: `${props.position.y}px`,
  transform: props.isDragging ? 'scale(1.1)' : 'scale(1)'
}));

const onClick = (e: Event) => {
  console.log('=== FloatingTrigger onClick ===');
  console.log('isDragging:', props.isDragging);
  
  // 如果正在拖拽中，忽略点击事件
  if (props.isDragging) {
    console.log('Click ignored: currently dragging');
    e.preventDefault();
    e.stopPropagation();
    return;
  }
  
  // 防止事件冒泡，避免重复触发
  e.preventDefault();
  e.stopPropagation();
  
  emit('click', e);
  console.log('click event emitted');
};

const onDragStart = (e: MouseEvent) => {
  console.log('=== FloatingTrigger onDragStart ===');
  console.log('Mouse event:', e);
  console.log('triggerRef DOM element:', triggerRef.value);
  
  emit('dragStart', e);
  console.log('dragStart event emitted');
};

defineExpose({
  triggerRef: triggerRef
});
</script>

<style scoped>
.floating-trigger {
  position: fixed;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.floating-trigger:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.floating-trigger.dragging {
  transition: none;
}

.floating-default-icon {
  font-size: 24px;
  line-height: 1;
}

.floating-icon-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.floating-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4d4f;
  color: white;
  border-radius: 10px;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  padding: 0 6px;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
}

.floating-pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.6);
  animation: pulse 2s infinite;
  pointer-events: none;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}
</style>