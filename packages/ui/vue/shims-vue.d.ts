// Vue SFC 文件声明
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 确保 Vue 模块类型正确
declare module 'vue' {
  export * from '@vue/runtime-core'
  export * from '@vue/reactivity'
  export * from '@vue/runtime-dom'
  
  // 补充可能缺失的类型
  export interface PropType<T = any> {
    new (...args: any[]): T & {}
    __propType?: T
  }
} 