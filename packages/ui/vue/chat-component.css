/* Vue Chat Component Styles */
.ai-chat-component {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 12px;
  background: #fff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

.ai-chat-component.dark {
  background: #1f1f1f;
  border-color: #434343;
  color: #ffffff;
}

/* 拖拽遮罩层样式 */
.send-drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(24, 144, 255, 0.05);
  border: 2px dashed #1890ff;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.send-drag-icon {
  font-size: 48px;
  color: #1890ff;
  margin-bottom: 16px;
}

.send-drag-title {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
}

.send-drag-desc {
  font-size: 14px;
  color: #666;
  text-align: center;
}

/* 消息容器样式 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 200px;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 消息时间戳样式 */
.message-timestamp {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-top: 4px;
}

/* Sender 容器样式 */
.sender-container {
  border-top: 1px solid #f0f0f0;
  padding: 16px;
  background: #fff;
}

.ai-chat-component.dark .sender-container {
  background: #1f1f1f;
  border-top-color: #434343;
}

/* Sender 头部样式（图片预览） */
.send-header {
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 12px;
}

.send-file-preview__container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 120px;
  overflow-y: auto;
}

.send-file-preview__item {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  background: #fafafa;
}

.send-file-preview__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.send-file-preview__file {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px;
  text-align: center;
}

.send-file-preview__icon {
  font-size: 18px;
  color: #666;
  margin-bottom: 2px;
}

.send-file-preview__info {
  flex: 1;
  overflow: hidden;
}

.send-file-preview__name {
  font-size: 10px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.send-file-preview__type {
  font-size: 9px;
  color: #999;
  margin-top: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.send-file-preview__btn {
  position: absolute !important;
  top: 2px;
  right: 2px;
  width: 20px !important;
  height: 20px !important;
  min-width: 20px !important;
  padding: 0 !important;
  background: #ff4d4f !important;
  border: 1px solid #fff !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 10px !important;
  color: #fff !important;
  z-index: 1;
}

.send-file-preview__btn:hover {
  background: #ff7875 !important;
}

/* 错误信息样式 */
.error-message {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 12px 16px;
  margin: 0 16px 12px;
  border-radius: 8px;
  font-size: 14px;
}

.ai-chat-component.dark .error-message {
  background: #2a1215;
  border-color: #58181c;
  color: #ff7875;
}

/* 状态信息样式 */
.status-info {
  padding: 8px 16px;
  font-size: 12px;
  color: #999;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-chat-component.dark .status-info {
  background: #141414;
  border-top-color: #434343;
  color: #666;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .ai-chat-component {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .messages-container {
    padding: 12px;
  }
  
  .sender-container {
    padding: 12px;
  }
  
  .send-image-preview__container {
    max-height: 80px;
  }
  
  .send-image-preview__item {
    width: 48px;
    height: 48px;
  }
}

/* 自定义 Ant Design X 组件样式 */
.ai-chat-component .ant-x-sender {
  box-shadow: none;
}

.ai-chat-component .ant-x-bubble {
  margin-bottom: 8px;
}

.ai-chat-component .ant-x-prompts {
  padding: 8px 0;
}

.ai-chat-component .ant-x-prompts .ant-x-prompts-item {
  font-size: 13px;
  padding: 6px 12px;
  border-radius: 16px;
  margin: 2px 4px 2px 0;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  transition: all 0.2s;
}

.ai-chat-component .ant-x-prompts .ant-x-prompts-item:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.ai-chat-component.dark .ant-x-prompts .ant-x-prompts-item {
  background: #262626;
  border-color: #434343;
  color: #ffffff;
}

.ai-chat-component.dark .ant-x-prompts .ant-x-prompts-item:hover {
  background: #003a8c;
  border-color: #1890ff;
  color: #91d5ff;
}

/* 消息文件样式 */
.message-files {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.message-file-item {
  position: relative;
  max-width: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.message-image {
  width: 100%;
  height: auto;
  display: block;
  cursor: pointer;
  transition: transform 0.2s;
  border-radius: 8px;
}

.message-image:hover {
  transform: scale(1.05);
}

.message-file {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 150px;
}

.message-file:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  transform: translateY(-1px);
}

.message-file-icon {
  font-size: 20px;
  color: #666;
  margin-right: 8px;
  flex-shrink: 0;
}

.message-file-info {
  flex: 1;
  overflow: hidden;
}

.message-file-name {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.message-file-type {
  font-size: 11px;
  color: #999;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 自定义插槽样式 */
.chat-header-slot {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.dark .chat-header-slot {
  border-bottom-color: #434343;
}

.empty-state-slot {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 16px;
}

.default-empty-state {
  text-align: center;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
}

.message-actions-slot {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.dark .message-actions-slot {
  border-top-color: #434343;
}

.input-tools-slot {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-footer-slot {
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.dark .chat-footer-slot {
  border-top-color: #434343;
  background: #1a1a1a;
}

/* 动画效果 */
.ai-chat-component .ant-x-bubble {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading 状态样式 */
.ai-chat-component .ant-x-sender.ant-x-sender-loading {
  opacity: 0.8;
}

.ai-chat-component .ant-x-sender.ant-x-sender-loading .ant-x-sender-input {
  background-color: #f5f5f5;
}

/* 基础容器 */
.ai-chat-component-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 悬浮触发器样式 */
.floating-trigger {
  position: fixed;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  cursor: pointer;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  user-select: none;
}
.floating-trigger.dragging {
  cursor: grabbing;
  transform: scale(1.1);
  transition: none;
}
.floating-default-icon { font-size: 24px; color: white; }
.floating-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4d4f;
  color: white;
  border-radius: 12px;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
  border: 2px solid white;
}
.floating-pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(66, 184, 131, 0.5);
  animation: pulse 2s infinite;
  z-index: -1;
}

/* 对话框样式 */
.chat-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 9998;
}
.chat-dialog {
  position: fixed;
  background: white;
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.chat-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}
.chat-dialog-title { font-weight: 600; }
.chat-dialog-close {
  border: none;
  background: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
}
.chat-dialog-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

@keyframes pulse {
  0% { transform: scale(0.95); opacity: 0.7; }
  70% { transform: scale(1.4); opacity: 0; }
  100% { transform: scale(0.95); opacity: 0; }
} 