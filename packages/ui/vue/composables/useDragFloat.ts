import { ref, onMounted, onUnmounted, reactive, watch, computed } from 'vue';
import type { FloatingConfig } from '../types';

interface DragFloatState {
  isDragging: boolean;
  isExpanded: boolean;
  position: { x: number; y: number; side: 'left' | 'right' };
  dragOffset: { x: number; y: number };
}

export function useDragFloat(config: FloatingConfig) {
  // 计算默认右下角位置
  const getDefaultPosition = () => {
    const viewport = { width: window.innerWidth, height: window.innerHeight };
    const elementWidth = 60;
    const elementHeight = 60;
    const margin = 20;
    
    return {
      x: viewport.width - elementWidth - margin,
      y: viewport.height - elementHeight - margin - 100, // 留出一些空间避免被底部工具栏遮挡
      side: 'right' as 'left' | 'right'
    };
  };

  const state = reactive<DragFloatState>({
    isDragging: false,
    isExpanded: false,
    position: {
      x: config.position?.x ?? getDefaultPosition().x,
      y: config.position?.y ?? getDefaultPosition().y,
      side: config.position?.side ?? 'right',
    },
    dragOffset: { x: 0, y: 0 },
  });

  const dragRef = ref<HTMLDivElement | null>(null);

  const snapToEdge = (x: number, y: number) => {
    const viewport = { width: window.innerWidth, height: window.innerHeight };
    const elementWidth = 60;
    const elementHeight = 60;
    const margin = 20;

    const clampedY = Math.max(margin, Math.min(y, viewport.height - elementHeight - margin));
    const side: 'left' | 'right' = x < viewport.width / 2 ? 'left' : 'right';
    const finalX = side === 'left' ? margin : viewport.width - elementWidth - margin;

    return { x: finalX, y: clampedY, side };
  };

  const dragStartPos = ref({ x: 0, y: 0 });
  const dragged = ref(false);
  const lastClickTime = ref(0);
  const clickDebounceDelay = 100; // 防抖延迟
  const recentlyDragged = ref(false); // 跟踪最近是否发生了拖拽操作
  
  const handleDragStart = (e: MouseEvent) => {
    console.log('=== handleDragStart triggered ===');
    console.log('config.dragEnabled:', config.dragEnabled);
    console.log('dragRef.value:', dragRef.value);
    console.log('event:', e);
    
    if (!config.dragEnabled) {
      console.warn('Drag is disabled in config');
      return;
    }
    
    if (!dragRef.value) {
      console.error('dragRef.value is null! Cannot start drag.');
      return;
    }
    
    console.log('Starting drag operation...');
    dragged.value = false;
    recentlyDragged.value = false; // 重置最近拖拽标记
    dragStartPos.value = { x: e.clientX, y: e.clientY };
    
    const rect = dragRef.value.getBoundingClientRect();
    console.log('Element rect:', rect);
    
    state.dragOffset = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
    
    console.log('Drag offset:', state.dragOffset);
    
    // Register mouse move and mouse up listeners immediately
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
    
    // 添加全局click事件监听，拦截意外的点击事件
    document.addEventListener('click', handleGlobalClick, true);
    
    console.log('Event listeners registered');
    
    e.preventDefault();
    console.log('=== handleDragStart completed ===');
  };

  const handleDragMove = (e: MouseEvent) => {
    const dx = e.clientX - dragStartPos.value.x;
    const dy = e.clientY - dragStartPos.value.y;

    // If moved more than a threshold, it's a drag
    if (!dragged.value && (Math.abs(dx) > 5 || Math.abs(dy) > 5)) {
      console.log('Drag threshold exceeded, starting drag animation');
      dragged.value = true;
      state.isDragging = true;
    }
    
    if (dragged.value) {
      state.position.x = e.clientX - state.dragOffset.x;
      state.position.y = e.clientY - state.dragOffset.y;
      // console.log('Dragging to:', state.position.x, state.position.y);
    }
  };

  const handleDragEnd = () => {
    console.log('=== handleDragEnd triggered ===');
    console.log('dragged.value:', dragged.value);
    
    // Remove event listeners immediately
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
    console.log('Event listeners removed');
    
    const wasDragging = dragged.value;
    
    if (wasDragging) {
      console.log('Completing drag operation...');
      state.isDragging = false;
      recentlyDragged.value = true; // 标记最近发生了拖拽
      
      const snappedPosition = snapToEdge(state.position.x, state.position.y);
      console.log('Snapped to position:', snappedPosition);
      state.position = snappedPosition;

      if (config.rememberPosition) {
        localStorage.setItem('chat-floating-position-vue', JSON.stringify(snappedPosition));
        console.log('Position saved to localStorage');
      }
      
      // 延迟清除拖拽标记和移除全局点击监听
      setTimeout(() => {
        recentlyDragged.value = false;
        document.removeEventListener('click', handleGlobalClick, true);
        console.log('recentlyDragged cleared and global click listener removed');
      }, 250); // 250ms延迟，确保click事件被正确处理
    } else {
      // 如果没有拖拽，立即移除全局点击监听
      document.removeEventListener('click', handleGlobalClick, true);
    }
    
    dragged.value = false;
    console.log('=== handleDragEnd completed ===');
  };

  const handleClick = (e: MouseEvent) => {
    console.log('=== handleClick triggered ===');
    console.log('dragged.value:', dragged.value);
    console.log('recentlyDragged.value:', recentlyDragged.value);
    
    // 防抖处理，防止双重触发
    const now = Date.now();
    if (now - lastClickTime.value < clickDebounceDelay) {
      console.log('Click ignored: too soon after last click');
      return;
    }
    lastClickTime.value = now;
    
    // 防止事件冒泡
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    
    // 关键修复：检查是否最近发生了拖拽操作
    if (recentlyDragged.value) {
      console.log('Click ignored: recently dragged');
      return;
    }
    
    // 只有在非拖拽状态下才处理点击
    if (!dragged.value) {
      console.log('Processing click: toggling expanded state');
      state.isExpanded = !state.isExpanded;
    } else {
      console.log('Click ignored: currently dragging');
    }
    
    console.log('=== handleClick completed ===');
  };
  
  const toggleExpanded = () => {
    state.isExpanded = !state.isExpanded;
  };
  
  const setExpanded = (value: boolean) => {
    state.isExpanded = value;
  }
  
  // 全局点击事件处理，用于阻止拖拽后的意外点击
  const handleGlobalClick = (e: MouseEvent) => {
    if (recentlyDragged.value) {
      console.log('Global click intercepted: recently dragged');
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
    }
  };

  onMounted(() => {
    // 修复：当悬浮模式启用时，应该加载保存的位置
    if (config.rememberPosition && config.enabled) {
      const saved = localStorage.getItem('chat-floating-position-vue');
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          state.position = snapToEdge(parsed.x, parsed.y);
        } catch (e) {
          console.warn('Failed to parse saved position for Vue:', e);
          // 如果解析失败，使用默认位置
          state.position = getDefaultPosition();
        }
      } else {
        // 如果没有保存的位置，使用默认右下角
        state.position = getDefaultPosition();
      }
    }

    // Event listeners are now managed directly in handleDragStart and handleDragEnd
    // to avoid circular dependency issues
  });

  onUnmounted(() => {
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);
    document.removeEventListener('click', handleGlobalClick, true);
  });

  return {
    isDragging: computed(() => state.isDragging),
    isExpanded: computed(() => state.isExpanded),
    position: computed(() => state.position),
    dragOffset: computed(() => state.dragOffset),
    dragRef,
    handleDragStart,
    handleClick,
    toggleExpanded,
    setExpanded,
  };
} 