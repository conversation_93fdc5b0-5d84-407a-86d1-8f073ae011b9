import { ref, watch } from 'vue'

// 临时类型定义，避免循环依赖
interface ChatEngineConfig {
  apiEndpoint?: string
  headers?: Record<string, string>
  timeout?: number
}

interface ChatCallbacks {
  onMessage?: (message: string) => void
  onUpdate?: (message: string) => void
  onError?: (error: string) => void
  onComplete?: (finalMessage: string) => void
  onInit?: () => void
}

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp?: number
}

export interface UseVueChatOptions extends ChatEngineConfig {
  storage?: any // 暂时简化
  initialMessages?: ChatMessage[]
  maxMessages?: number
  autoSave?: boolean
  showHistory?: boolean
  sessionId?: string
}

// 消息类型
interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp?: number
}

// 组件配置类型
interface ComponentConfig {
  showAvatar?: boolean
  placeholder?: string
  maxInputLength?: number
  enableAutoScroll?: boolean
  timeout?: number
  style?: Record<string, any>
}

/**
 * Vue 聊天组合式函数 - 适配Vue的响应式系统
 */
export function useVueChat(config: ComponentConfig & { showHistory?: boolean; sessionId?: string }) {
  const SESSION_ID = config.sessionId || 'default-session'
  
  const initialMessages = () => {
    if (config.showHistory) {
      try {
        const saved = localStorage.getItem(`chat-history-${SESSION_ID}`)
        return saved ? JSON.parse(saved) : []
      } catch (e) {
        return []
      }
    }
    return []
  }

  const messages = ref<Message[]>(initialMessages())
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const currentChatId = ref<string>('')

  const sendMessage = async (content: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      // 添加用户消息
      const userMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content,
        timestamp: Date.now()
      }
      messages.value.push(userMessage)
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 添加AI回复
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `AI回复: ${content}`,
        timestamp: Date.now()
      }
      messages.value.push(aiMessage)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '发送失败'
    } finally {
      isLoading.value = false
    }
  }

  const clearMessages = () => {
    messages.value = []
    error.value = null
    if (config.showHistory) {
      localStorage.removeItem(`chat-history-${SESSION_ID}`)
    }
  }

  const abortRequest = () => {
    isLoading.value = false
  }

  const loadMessages = () => {
    // 加载历史消息的逻辑
    // 在实际应用中，这里会从本地存储或API加载消息
  }

  // 监听消息变化，自动保存历史记录
  watch(() => messages.value, (newMessages: Message[]) => {
    if (config.showHistory) {
      localStorage.setItem(`chat-history-${SESSION_ID}`, JSON.stringify(newMessages))
    }
  }, { deep: true })

  // 监听配置变化，重新加载历史记录
  watch(() => [config.sessionId, config.showHistory], () => {
    if (config.showHistory) {
      try {
        const saved = localStorage.getItem(`chat-history-${SESSION_ID}`)
        messages.value = saved ? JSON.parse(saved) : []
      } catch (e) {
        messages.value = []
      }
    } else {
      messages.value = []
    }
  })

  return {
    messages,
    isLoading,
    error,
    currentChatId,
    sendMessage,
    clearMessages,
    abortRequest,
    loadMessages,
  }
} 