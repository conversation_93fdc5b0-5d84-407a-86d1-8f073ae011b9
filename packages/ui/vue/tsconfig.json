{"extends": "../tsconfig.json", "compilerOptions": {"jsx": "preserve", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "types": ["node"], "paths": {"vue": ["./vue.d.ts", "node_modules/vue"], "@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.vue", "**/*.d.ts", "./vue.d.ts", "./shims-vue.d.ts"], "exclude": ["node_modules", "dist"], "vueCompilerOptions": {"target": 3.3}}