import type { UploadConfig, UploadResponse } from '../types';

/**
 * 默认的响应解析函数
 */
const defaultResponseParser = (response: any): UploadResponse => {
  // 尝试常见的响应格式
  if (response.data?.url) {
    return { url: response.data.url, ...response.data };
  }
  if (response.url) {
    return { url: response.url, ...response };
  }
  if (response.data?.data?.url) {
    return { url: response.data.data.url, ...response.data.data };
  }
  if (typeof response === 'string') {
    return { url: response };
  }
  
  throw new Error('无法解析上传响应，请提供 responseParser 函数');
};

/**
 * 默认的状态验证函数
 */
const defaultValidateStatus = (status: number): boolean => {
  return status >= 200 && status < 300;
};

/**
 * 使用配置执行文件上传
 */
export const uploadFileWithConfig = async (
  file: File,
  config: UploadConfig,
  onError?: (error: string) => void
): Promise<string | UploadResponse> => {
  const {
    url,
    method = 'POST',
    headers = {},
    withCredentials = false,
    data = {},
    fieldName = 'file',
    timeout = 60000,
    responseParser = defaultResponseParser,
    onProgress,
    validateStatus = defaultValidateStatus,
    requestType = 'form-data'
  } = config;

  try {
    return new Promise(async(resolve, reject) => {
      const xhr = new XMLHttpRequest();
      
      // 配置超时
      xhr.timeout = timeout;
      
      // 配置跨域凭证
      xhr.withCredentials = withCredentials;
      
      // 上传进度
      if (onProgress) {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const percent = Math.round((event.loaded / event.total) * 100);
            onProgress(percent, file);
          }
        });
      }
      
      // 处理响应
      xhr.addEventListener('load',  () => {
        if (validateStatus(xhr.status)) {
          try {
            let response = xhr.responseText;
            try {
              response = JSON.parse(xhr.responseText);
            } catch (e) {
              // 如果不是JSON，保持原始文本
            }
            
            const parsed = responseParser(response);
            resolve(parsed);
          } catch (error) {
            reject(new Error(`解析响应失败: ${(error as unknown as Error).message}`));
          }
        } else {
          reject(new Error(`上传失败: ${xhr.status} ${xhr.statusText}`));
        }
      });
      
      // 处理错误
      xhr.addEventListener('error', () => {
        reject(new Error('网络错误'));
      });
      
      xhr.addEventListener('timeout', () => {
        reject(new Error('上传超时'));
      });
      
      // 打开连接
      xhr.open(method, url, true);
      
      // 设置请求头
      Object.entries(headers).forEach(([key, value]) => {
        xhr.setRequestHeader(key, value);
      });
      
      // 准备请求体
      let body: FormData | string;
      
      if (requestType === 'json') {
        // JSON格式上传
        const jsonData = {
          ...data,
          [fieldName]: {
            name: file.name,
            type: file.type,
            size: file.size,
            // 将文件转为base64
            content: await fileToBase64(file)
          }
        };
        body = JSON.stringify(jsonData);
        xhr.setRequestHeader('Content-Type', 'application/json');
      } else {
        // FormData格式上传（默认）
        const formData = new FormData();
        formData.append(fieldName, file);
        
        // 添加额外的表单数据
        Object.entries(data).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            formData.append(key, String(value));
          }
        });
        
        body = formData;
        // 不设置Content-Type，让浏览器自动设置
      }
      
      // 发送请求
      xhr.send(body);
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '上传失败';
    onError?.(errorMessage);
    throw error;
  }
};

/**
 * 将文件转换为base64
 */
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  });
};

/**
 * 创建默认的上传函数
 */
export const createDefaultUploadFunction = (
  config: UploadConfig,
  onError?: (error: string) => void
) => {
  return (file: File) => uploadFileWithConfig(file, config, onError);
};