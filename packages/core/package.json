{"name": "@leyaoyao/ai-component-core", "version": "0.0.6", "description": "AI组件核心逻辑包 - 平台无关的业务逻辑和状态管理", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "unpkg": "dist/index.umd.js", "scripts": {"build": "rollup -c", "build:watch": "rollup -c --watch", "dev": "rollup -c --watch", "test": "echo \"Warning: no tests specified\" && exit 0", "clean": "<PERSON><PERSON><PERSON> dist", "tsc:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/esm"}, "keywords": ["ai", "chat", "component", "core", "typescript"], "author": "", "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "@ant-design/x": ">=1.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "typescript": "^5.0.0", "jest": "^29.0.0", "@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "rollup": "^4.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "@rollup/plugin-terser": "^0.4.0", "rimraf": "^5.0.0"}, "files": ["logic/", "state/", "hooks/", "utils/", "types/", "index.ts"], "exports": {".": "./index.ts", "./types": "./types/index.ts", "./hooks": "./hooks/index.ts", "./logic": "./logic/index.ts", "./utils": "./utils/index.ts"}}