import { useState, useRef, useCallback } from 'react'
import { ChatEngine, ChatEngineConfig, ChatCallbacks } from '../logic/ai/chat-engine'
import { ChatMessage, UseChatReturn } from '../types'
import { StorageManager } from '@leyaoyao/ai-component-adapters'

export interface UseChatOptions {
  apiEndpoint: string
  headers?: Record<string, string>
  timeout?: number
  storage?: StorageManager
  initialMessages?: ChatMessage[]
  maxMessages?: number
  autoSave?: boolean
}

/**
 * 聊天Hook - 平台无关的聊天状态管理
 */
export function useChat(options: UseChatOptions): UseChatReturn {
  const {
    storage,
    initialMessages = [],
    maxMessages = 100,
    autoSave = true,
    apiEndpoint,
    headers,
    timeout,
  } = options

  // 状态管理
  const [messages, setMessages] = useState<ChatMessage[]>(initialMessages)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 引用管理
  const chatEngineRef = useRef<ChatEngine | null>(null)
  const chatIdRef = useRef<string>('')

  // 初始化聊天引擎
  const getChatEngine = useCallback(() => {
    if (!chatEngineRef.current) {
      const engineConfig: ChatEngineConfig = {
        apiEndpoint,
        headers,
        timeout,
      }
      chatEngineRef.current = new ChatEngine(engineConfig)
    }
    return chatEngineRef.current
  }, [apiEndpoint, headers, timeout])

  // 发送消息
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isLoading) {
      return
    }

    const engine = getChatEngine()
    setIsLoading(true)
    setError(null)

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: content.trim(),
      role: 'user',
      timestamp: Date.now(),
      chatId: chatIdRef.current,
    }

    setMessages(prev => [...prev, userMessage])

    // 创建助手消息
    const assistantMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      content: '',
      role: 'assistant',
      timestamp: Date.now(),
      chatId: chatIdRef.current,
    }

    setMessages(prev => [...prev, assistantMessage])

    // 配置回调
    const callbacks: ChatCallbacks = {
      onInit: () => {
        console.log('开始发送消息')
      },
      
      onUpdate: (message: string) => {
        setMessages(prev => {
          const newMessages = [...prev]
          const lastMessage = newMessages[newMessages.length - 1]
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.content = message
          }
          return newMessages
        })
      },

      onComplete: (finalMessage: string) => {
        setMessages(prev => {
          const newMessages = [...prev]
          const lastMessage = newMessages[newMessages.length - 1]
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.content = finalMessage
          }
          return newMessages
        })
        
        setIsLoading(false)
        
        // 更新chatId
        chatIdRef.current = engine.getCurrentChatId()
        
        // 自动保存
        if (autoSave && storage) {
          saveMessages([...messages, userMessage, { ...assistantMessage, content: finalMessage }])
        }
      },

      onError: (errorMessage: string) => {
        setError(errorMessage)
        setIsLoading(false)
        
        // 移除失败的助手消息
        setMessages(prev => prev.slice(0, -1))
      },
    }

    try {
      // 发送消息给聊天引擎
      await engine.sendMessage({ content }, callbacks)
    } catch (err) {
      console.error('发送消息失败:', err)
      setError('发送消息失败')
      setIsLoading(false)
      setMessages(prev => prev.slice(0, -1))
    }
  }, [isLoading, getChatEngine, autoSave, storage, messages])

  // 清空消息
  const clearMessages = useCallback(() => {
    setMessages([])
    setError(null)
    chatIdRef.current = ''
    
    if (storage) {
      storage.remove('chat_messages').catch(console.warn)
    }
  }, [storage])

  // 取消请求
  const abortRequest = useCallback(() => {
    const engine = chatEngineRef.current
    if (engine) {
      engine.abort()
    }
    setIsLoading(false)
    setError(null)
  }, [])

  // 保存消息到存储
  const saveMessages = useCallback(async (messagesToSave: ChatMessage[]) => {
    if (!storage) return
    
    try {
      // 限制消息数量
      const limitedMessages = messagesToSave.slice(-maxMessages)
      await storage.setObject('chat_messages', limitedMessages)
      await storage.setString('current_chat_id', chatIdRef.current)
    } catch (err) {
      console.warn('保存消息失败:', err)
    }
  }, [storage, maxMessages])

  // 从存储加载消息
  const loadMessages = useCallback(async () => {
    if (!storage) return
    
    try {
      const savedMessages = await storage.getObject<ChatMessage[]>('chat_messages', [])
      const savedChatId = await storage.getString('current_chat_id', '')
      
      if (savedMessages) {
        setMessages(savedMessages)
      }
      
      if (savedChatId) {
        chatIdRef.current = savedChatId
        const engine = getChatEngine()
        engine.setChatId(savedChatId)
      }
    } catch (err) {
      console.warn('加载消息失败:', err)
    }
  }, [storage, getChatEngine])

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    abortRequest,
    currentChatId: chatIdRef.current,
    // 扩展方法
    loadMessages,
    saveMessages: () => saveMessages(messages),
  }
} 