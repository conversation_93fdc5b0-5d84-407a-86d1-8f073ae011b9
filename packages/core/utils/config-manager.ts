import { ComponentConfig } from '../types'
import { StorageManager } from '@leyaoyao/ai-component-adapters'

/**
 * 配置管理器 - 负责管理组件配置的加载、合并和保存
 */
export class ConfigManager {
  private config: ComponentConfig
  private storage?: StorageManager
  private readonly configKey = 'ai_component_config'

  constructor(defaultConfig: ComponentConfig, storage?: StorageManager) {
    this.config = { ...defaultConfig }
    this.storage = storage
  }

  /**
   * 获取当前配置
   */
  getConfig(): ComponentConfig {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<ComponentConfig>): void {
    this.config = {
      ...this.config,
      ...updates,
      // 确保headers正确合并
      headers: {
        ...this.config.headers,
        ...updates.headers,
      },
    }

    // 自动保存到存储
    if (this.storage) {
      this.saveConfig().catch(console.warn)
    }
  }

  /**
   * 重置配置为默认值
   */
  resetConfig(defaultConfig: ComponentConfig): void {
    this.config = { ...defaultConfig }
    
    if (this.storage) {
      this.storage.remove(this.configKey).catch(console.warn)
    }
  }

  /**
   * 从存储加载配置
   */
  async loadConfig(): Promise<void> {
    if (!this.storage) return

    try {
      const savedConfig = await this.storage.getObject<Partial<ComponentConfig>>(this.configKey)
      if (savedConfig) {
        this.updateConfig(savedConfig)
      }
    } catch (error) {
      console.warn('加载配置失败:', error)
    }
  }

  /**
   * 保存配置到存储
   */
  async saveConfig(): Promise<void> {
    if (!this.storage) return

    try {
      await this.storage.setObject(this.configKey, this.config)
    } catch (error) {
      console.warn('保存配置失败:', error)
    }
  }

  /**
   * 合并多个配置
   */
  static mergeConfigs(...configs: Array<Partial<ComponentConfig>>): ComponentConfig {
    let mergedConfig = {} as ComponentConfig

    for (const config of configs) {
      if (config) {
        mergedConfig = {
          ...mergedConfig,
          ...config,
          // 特殊处理headers合并
          headers: {
            ...mergedConfig.headers,
            ...config.headers,
          },
          // 特殊处理style合并
          style: {
            ...mergedConfig.style,
            ...config.style,
          },
        }
      }
    }

    return mergedConfig
  }

  /**
   * 验证配置有效性
   */
  static validateConfig(config: Partial<ComponentConfig>): string[] {
    const errors: string[] = []

    if (config.apiEndpoint && !config.apiEndpoint.trim()) {
      errors.push('apiEndpoint不能为空')
    }

    if (config.timeout && config.timeout < 1000) {
      errors.push('timeout不能小于1000ms')
    }

    if (config.maxInputLength && config.maxInputLength < 1) {
      errors.push('maxInputLength不能小于1')
    }

    return errors
  }

  /**
   * 获取环境特定配置
   */
  static getEnvironmentConfig(): Partial<ComponentConfig> {
    const env = process.env.NODE_ENV || 'development'
    
    switch (env) {
      case 'development':
        return {
          timeout: 30000,
          headers: {
            'x-debug': 'true',
          },
        }
      
      case 'production':
        return {
          timeout: 180000,
        }
      
      case 'test':
        return {
          timeout: 10000,
          storage: 'memory',
        }
      
      default:
        return {}
    }
  }

  /**
   * 获取平台特定配置
   */
  static getPlatformConfig(): Partial<ComponentConfig> {
    // 检测平台
    if (typeof window !== 'undefined') {
      // Web环境
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      )
      
      return {
        platform: isMobile ? 'mobile' : 'web',
        storage: 'localStorage',
        navigation: 'web',
      }
    } else if (typeof global !== 'undefined') {
      // Node.js或React Native环境
      return {
        platform: 'native',
        storage: 'asyncStorage',
        navigation: 'native',
      }
    }
    
    return {}
  }
} 