{"name": "@leyaoyao/ai-component-adapters", "version": "0.0.6", "description": "AI组件适配器包 - 平台适配和抽象接口实现", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "unpkg": "dist/index.umd.js", "scripts": {"build": "rollup -c", "build:watch": "rollup -c --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "echo \"Warning: no tests specified\" && exit 0", "dev": "tsc --watch", "tsc:types": "tsc --emitDeclarationOnly --declaration --declarationMap --outDir dist/esm"}, "keywords": ["ai", "adapters", "component", "typescript", "storage", "navigation"], "author": "", "license": "MIT", "peerDependencies": {"react": ">=16.8.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-typescript": "^11.0.0", "rollup": "^4.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-terser": "^7.0.2", "rimraf": "^5.0.0", "@types/react": "^18.0.0", "typescript": "^5.0.0", "jest": "^29.0.0"}, "files": ["storage/", "navigation/", "index.ts"], "exports": {".": "./index.ts", "./storage": "./storage/index.ts", "./navigation": "./navigation/index.ts"}}