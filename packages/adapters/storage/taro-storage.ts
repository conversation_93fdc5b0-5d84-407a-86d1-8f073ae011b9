import { StorageAdapter } from './storage-adapter'

/**
 * TaroJS存储适配器 - 支持多端小程序、H5、React Native
 */
export class TaroStorageAdapter implements StorageAdapter {
  async getItem(key: string): Promise<string | null> {
    try {
      // 动态导入Taro，避免在非Taro环境中报错
      const Taro = await import('@tarojs/taro')
      const result = await Taro.default.getStorage({ key })
      return result.data || null
    } catch (error) {
      console.warn(`Taro存储读取失败: ${key}`, error)
      return null
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      const Taro = await import('@tarojs/taro')
      await Taro.default.setStorage({ key, data: value })
    } catch (error) {
      console.warn(`Taro存储写入失败: ${key}`, error)
      throw error
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      const Taro = await import('@tarojs/taro')
      await Ta<PERSON>.default.removeStorage({ key })
    } catch (error) {
      console.warn(`Taro存储删除失败: ${key}`, error)
      throw error
    }
  }

  async clear(): Promise<void> {
    try {
      const Taro = await import('@tarojs/taro')
      await Taro.default.clearStorage()
    } catch (error) {
      console.warn('Taro存储清空失败', error)
      throw error
    }
  }

  async getAllKeys(): Promise<string[]> {
    try {
      const Taro = await import('@tarojs/taro')
      const result = await Taro.default.getStorageInfo()
      return (result as any).keys || []
    } catch (error) {
      console.warn('Taro存储获取键列表失败', error)
      return []
    }
  }

  async multiGet(keys: string[]): Promise<Array<[string, string | null]>> {
    const results: Array<[string, string | null]> = []
    
    for (const key of keys) {
      const value = await this.getItem(key)
      results.push([key, value])
    }
    
    return results
  }

  async multiSet(keyValuePairs: Array<[string, string]>): Promise<void> {
    for (const [key, value] of keyValuePairs) {
      await this.setItem(key, value)
    }
  }

  async multiRemove(keys: string[]): Promise<void> {
    for (const key of keys) {
      await this.removeItem(key)
    }
  }
}

/**
 * React Native存储适配器 - 使用AsyncStorage
 */
export class ReactNativeStorageAdapter implements StorageAdapter {
  async getItem(key: string): Promise<string | null> {
    try {
      // @ts-ignore
      const { default: AsyncStorage } = await import('@react-native-async-storage/async-storage')
      return await AsyncStorage.getItem(key)
    } catch (error) {
      console.warn(`RN存储读取失败: ${key}`, error)
      return null
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      // @ts-ignore
      const { default: AsyncStorage } = await import('@react-native-async-storage/async-storage')
      await AsyncStorage.setItem(key, value)
    } catch (error) {
      console.warn(`RN存储写入失败: ${key}`, error)
      throw error
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      // @ts-ignore
      const { default: AsyncStorage } = await import('@react-native-async-storage/async-storage')
      await AsyncStorage.removeItem(key)
    } catch (error) {
      console.warn(`RN存储删除失败: ${key}`, error)
      throw error
    }
  }

  async clear(): Promise<void> {
    try {
      // @ts-ignore
      const { default: AsyncStorage } = await import('@react-native-async-storage/async-storage')
      await AsyncStorage.clear()
    } catch (error) {
      console.warn('RN存储清空失败', error)
      throw error
    }
  }

  async getAllKeys(): Promise<string[]> {
    try {
      // @ts-ignore
      const { default: AsyncStorage } = await import('@react-native-async-storage/async-storage')
      return await AsyncStorage.getAllKeys()
    } catch (error) {
      console.warn('RN存储获取键列表失败', error)
      return []
    }
  }

  async multiGet(keys: string[]): Promise<Array<[string, string | null]>> {
    try {
      // @ts-ignore
      const { default: AsyncStorage } = await import('@react-native-async-storage/async-storage')
      return await AsyncStorage.multiGet(keys)
    } catch (error) {
      console.warn('RN存储批量获取失败', error)
      return keys.map(key => [key, null])
    }
  }

  async multiSet(keyValuePairs: Array<[string, string]>): Promise<void> {
    try {
      // @ts-ignore
      const { default: AsyncStorage } = await import('@react-native-async-storage/async-storage')
      await AsyncStorage.multiSet(keyValuePairs)
    } catch (error) {
      console.warn('RN存储批量设置失败', error)
      throw error
    }
  }

  async multiRemove(keys: string[]): Promise<void> {
    try {
      // @ts-ignore
      const { default: AsyncStorage } = await import('@react-native-async-storage/async-storage')
      await AsyncStorage.multiRemove(keys)
    } catch (error) {
      console.warn('RN存储批量删除失败', error)
      throw error
    }
  }
} 