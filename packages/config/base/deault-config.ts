import { ComponentConfig } from '@leyaoyao/ai-component-core'

/**
 * 默认AI组件配置
 */
export const defaultConfig: ComponentConfig = {
  // API配置
  apiEndpoint: '/aigw/starmind/rest/biz/chat',
  headers: {
    'Content-Type': 'application/json',
    'bizPlatform': '1',
    'bizScene': '1',
    'bizCategory': '1',
  },
  timeout: 120000, // 2分钟超时
  bizScene: '1',
  bizPlatform: '1',
  bizCategory: '1',

  // 平台配置
  storage: 'localStorage',
  navigation: 'web',
  theme: 'auto',
  platform: 'web',

  // UI配置
  placeholder: '请输入您的问题...',
  showHistory: true,
  showAvatar: true,
  maxInputLength: 2000,

  // 功能配置
  enableAutoScroll: true,
  enableTyping: true,
  enableRetry: true,

  // 样式配置
  className: '',
  style: {},
}

/**
 * 开发环境配置
 */
export const developmentConfig: Partial<ComponentConfig> = {
  timeout: 30000, // 开发环境短超时
  headers: {
    ...defaultConfig.headers,
    'x-debug': 'true',
  },
}

/**
 * 生产环境配置
 */
export const productionConfig: Partial<ComponentConfig> = {
  timeout: 180000, // 生产环境长超时
  enableRetry: true,
}

/**
 * 测试环境配置
 */
export const testConfig: Partial<ComponentConfig> = {
  apiEndpoint: '/mock/ai/chat',
  timeout: 10000,
  storage: 'memory', // 测试环境使用内存存储
}