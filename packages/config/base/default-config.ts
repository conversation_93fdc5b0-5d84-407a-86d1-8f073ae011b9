import { ComponentConfig } from '@leyaoyao/ai-component-core'

/**
 * AI组件默认配置
 */
export const defaultConfig: ComponentConfig = {
  // API 配置
  apiEndpoint: '/api/chat',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
  
  // UI 配置
  placeholder: '请输入您的问题...',
  showHistory: true,
  showAvatar: true,
  maxInputLength: 2000,
  enableAutoScroll: true,
  
  // 功能配置
  enableTyping: false,
  enableRetry: true,
  
  // 主题配置
  theme: 'auto', // 'light' | 'dark' | 'auto'
  
  // 样式配置
  style: {
    width: '100%',
    height: '500px',
    border: '1px solid #d9d9d9',
    borderRadius: '8px',
  },
  
  // 平台配置
  platform: 'web', // 'web' | 'native' | 'mobile'
  
  // 存储配置
  storage: 'localStorage', // 'localStorage' | 'asyncStorage' | 'custom' | 'memory'
  
  // 导航配置
  navigation: 'web', // 'web' | 'native'
} 