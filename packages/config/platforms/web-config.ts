import { ComponentConfig } from '@leyaoyao/ai-component-core'
import { defaultConfig } from '../base/default-config'

/**
 * Web平台配置
 */
export const webConfig: ComponentConfig = {
  ...defaultConfig,
  platform: 'web',
  storage: 'localStorage',
  navigation: 'web',
  
  // Web特定的headers
  headers: {
    ...defaultConfig.headers,
    'User-Agent': typeof navigator !== 'undefined' ? navigator.userAgent : 'WebClient/1.0',
  },

  // Web特定的样式
  style: {
    width: '100%',
    height: '500px',
    border: '1px solid #d9d9d9',
    borderRadius: '8px',
  },
}

/**
 * 移动端Web配置
 */
export const mobileWebConfig: ComponentConfig = {
  ...webConfig,
  platform: 'mobile',
  
  // 移动端适配
  style: {
    width: '100%',
    height: '100vh',
    border: 'none',
    borderRadius: '0',
  },

  // 移动端优化
  maxInputLength: 1000, // 移动端限制输入长度
  enableAutoScroll: true,
  showHistory: false, // 移动端默认隐藏历史记录
}

/**
 * 桌面端Web配置
 */
export const desktopWebConfig: ComponentConfig = {
  ...webConfig,
  
  // 桌面端优化
  style: {
    width: '800px',
    height: '600px',
    border: '1px solid #d9d9d9',
    borderRadius: '12px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  },

  showHistory: true,
  showAvatar: true,
}

/**
 * 嵌入式Web配置 (iframe等)
 */
export const embeddedWebConfig: ComponentConfig = {
  ...webConfig,
  
  // 嵌入式样式
  style: {
    width: '100%',
    height: '100%',
    border: 'none',
    borderRadius: '0',
  },

  // 嵌入式功能限制
  showHistory: false,
  enableRetry: false,
}