import { ComponentConfig } from '@leyaoyao/ai-component-core'

/**
 * Native平台配置
 */
export const nativeConfig: ComponentConfig = {
  // API配置
  apiEndpoint: '/aigw/starmind/rest/biz/chat',
  headers: {
    'Content-Type': 'application/json',
    'bizPlatform': '1',
    'bizScene': '1',
    'bizCategory': '1',
    'User-Agent': 'NativeClient/1.0',
    'X-Platform': 'native',
  },
  timeout: 60000,
  bizScene: '1',
  bizPlatform: '1',
  bizCategory: '1',

  // 平台配置
  platform: 'native',
  storage: 'asyncStorage',
  navigation: 'native',
  theme: 'auto',

  // UI配置
  placeholder: '请输入您的问题...',
  showHistory: true,
  showAvatar: true,
  maxInputLength: 2000,

  // 功能配置
  enableAutoScroll: true,
  enableTyping: true,
  enableRetry: true,

  // 样式配置
  className: '',
  style: {},
}

/**
 * iOS配置
 */
export const iosConfig: ComponentConfig = {
  ...nativeConfig,
  
  headers: {
    ...nativeConfig.headers,
    'X-Platform': 'ios',
  },

  // iOS特定优化
  enableAutoScroll: true,
  showAvatar: true,
}

/**
 * Android配置
 */
export const androidConfig: ComponentConfig = {
  ...nativeConfig,
  
  headers: {
    ...nativeConfig.headers,
    'X-Platform': 'android',
  },

  // Android特定优化
  enableAutoScroll: true,
  showAvatar: true,
}

/**
 * React Native配置
 */
export const reactNativeConfig: ComponentConfig = {
  ...nativeConfig,
  
  headers: {
    ...nativeConfig.headers,
    'X-Platform': 'react-native',
  },

  // React Native特定配置
  storage: 'asyncStorage',
  timeout: 90000,
} 