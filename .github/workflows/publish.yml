name: Build and Publish

on:
  # 手动触发
  workflow_dispatch:
    inputs:
      version:
        description: '发布版本 (留空自动递增patch)'
        required: false
        type: string
      release_type:
        description: '发布类型'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
          - prerelease
      dry_run:
        description: '测试发布 (不实际发布)'
        required: false
        default: false
        type: boolean
      skip_tests:
        description: '跳过测试'
        required: false
        default: false
        type: boolean

  # 推送到master分支时自动发布
  push:
    branches:
      - master
    paths:
      - 'packages/**'
      - 'package.json'
      - 'pnpm-lock.yaml'

  # PR到master分支时运行测试
  pull_request:
    branches:
      - master
    paths:
      - 'packages/**'
      - 'package.json'
      - 'pnpm-lock.yaml'

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'
  COMPANY_REGISTRY: 'https://npm.leyaoyao.com'

jobs:
  # 检查和测试
  check-and-test:
    name: 检查和测试
    runs-on: ubuntu-latest
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # 获取完整历史以便生成changelog

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 设置pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 获取pnpm缓存目录
        id: pnpm-cache
        shell: bash
        run: echo "store_path=$(pnpm store path)" >> $GITHUB_OUTPUT

      - name: 缓存pnpm依赖
        uses: actions/cache@v3
        with:
          path: ${{ steps.pnpm-cache.outputs.store_path }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 代码格式检查
        run: |
          if pnpm run lint --if-present; then
            echo "✅ 代码格式检查通过"
          else
            echo "⚠️ 没有配置lint脚本或检查失败"
          fi

      - name: TypeScript类型检查
        run: |
          if pnpm run type-check --if-present; then
            echo "✅ TypeScript类型检查通过"
          else
            echo "⚠️ 没有配置type-check脚本或检查失败"
          fi

      - name: 运行单元测试
        run: |
          # 运行各个包的测试
          for package in adapters config core ui; do
            if [ -f "packages/$package/package.json" ] && grep -q '"test":' "packages/$package/package.json"; then
              echo "运行 $package 测试..."
              cd "packages/$package"
              pnpm run test || exit 1
              cd - > /dev/null
            fi
          done

      - name: 构建packages
        run: pnpm run build:packages:clean

      - name: 预发布检查
        run: node scripts/pre-publish-check.js

      - name: 缓存构建产物
        uses: actions/cache@v3
        with:
          path: |
            packages/*/dist
          key: ${{ runner.os }}-build-${{ github.sha }}

  # 发布到生产环境
  publish-production:
    name: 发布到生产环境
    runs-on: ubuntu-latest
    needs: check-and-test
    if: |
      github.event_name == 'workflow_dispatch' ||
      (github.event_name == 'push' && github.ref == 'refs/heads/master')
    environment: production
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 设置pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 恢复构建产物缓存
        uses: actions/cache@v3
        with:
          path: |
            packages/*/dist
          key: ${{ runner.os }}-build-${{ github.sha }}

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 配置Git用户
        run: |
          git config --global user.name "github-actions[bot]"
          git config --global user.email "github-actions[bot]@users.noreply.github.com"

      - name: 设置npm认证
        run: |
          echo "//${COMPANY_REGISTRY#*://}/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc
          npm config set registry ${{ env.COMPANY_REGISTRY }}

      - name: 计算发布版本
        id: version
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            if [ -n "${{ github.event.inputs.version }}" ]; then
              echo "version=${{ github.event.inputs.version }}" >> $GITHUB_OUTPUT
            else
              NEW_VERSION=$(node scripts/version-manager.js generate ${{ github.event.inputs.release_type }})
              echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
            fi
            echo "dry_run=${{ github.event.inputs.dry_run }}" >> $GITHUB_OUTPUT
            echo "skip_tests=${{ github.event.inputs.skip_tests }}" >> $GITHUB_OUTPUT
          else
            # 自动发布使用patch版本
            NEW_VERSION=$(node scripts/version-manager.js generate patch)
            echo "version=$NEW_VERSION" >> $GITHUB_OUTPUT
            echo "dry_run=false" >> $GITHUB_OUTPUT
            echo "skip_tests=false" >> $GITHUB_OUTPUT
          fi

      - name: 发布packages
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
        run: |
          # 构建发布命令
          PUBLISH_CMD="bash scripts/build-and-publish.sh"
          
          if [ -n "${{ steps.version.outputs.version }}" ]; then
            PUBLISH_CMD="$PUBLISH_CMD ${{ steps.version.outputs.version }}"
          fi
          
          if [ "${{ steps.version.outputs.dry_run }}" == "true" ]; then
            PUBLISH_CMD="$PUBLISH_CMD --dry-run"
          fi
          
          if [ "${{ steps.version.outputs.skip_tests }}" == "true" ]; then
            PUBLISH_CMD="$PUBLISH_CMD --skip-tests"
          fi
          
          echo "执行命令: $PUBLISH_CMD"
          $PUBLISH_CMD

      - name: 生成发布说明
        if: steps.version.outputs.dry_run != 'true'
        run: |
          node scripts/publish-helper.js notes ${{ steps.version.outputs.version }}

      - name: 创建GitHub Release
        if: steps.version.outputs.dry_run != 'true'
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.version.outputs.version }}
          release_name: Release v${{ steps.version.outputs.version }}
          body_path: RELEASE_NOTES_${{ steps.version.outputs.version }}.md
          draft: false
          prerelease: ${{ contains(steps.version.outputs.version, '-') }}

      - name: 通知结果
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ 发布成功！"
            echo "版本: ${{ steps.version.outputs.version }}"
            echo "仓库: ${{ env.COMPANY_REGISTRY }}"
          else
            echo "❌ 发布失败！"
          fi

  # 发布到测试环境
  publish-staging:
    name: 发布到测试环境
    runs-on: ubuntu-latest
    needs: check-and-test
    if: github.event_name == 'pull_request'
    environment: staging
    steps:
      - name: Checkout代码
        uses: actions/checkout@v4

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 设置pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 测试发布
        run: node scripts/publish-helper.js test

      - name: 评论PR结果
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '🧪 测试发布通过！代码可以安全合并。'
            });

  # 通知任务
  notify:
    name: 发送通知
    runs-on: ubuntu-latest
    needs: [check-and-test, publish-production]
    if: always() && (github.event_name == 'workflow_dispatch' || github.event_name == 'push')
    steps:
      - name: 发送成功通知
        if: needs.publish-production.result == 'success'
        run: |
          echo "🎉 AI Component发布成功！"
          # 这里可以添加钉钉、企业微信等通知

      - name: 发送失败通知
        if: needs.publish-production.result == 'failure'
        run: |
          echo "❌ AI Component发布失败！"
          # 这里可以添加钉钉、企业微信等通知