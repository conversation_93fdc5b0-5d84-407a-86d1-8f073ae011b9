/**
 * AI Component 构建配置
 * 前端开发专家级构建系统
 */

const path = require('path')
const fs = require('fs')

// 构建配置常量
const BUILD_CONFIG = {
  // 包构建顺序（基于依赖关系）
  BUILD_ORDER: ['adapters', 'core', 'config', 'ui'],
  
  // 输出格式配置
  FORMATS: {
    esm: { ext: 'esm.js', format: 'es' },
    cjs: { ext: 'cjs.js', format: 'cjs' },
    umd: { ext: 'umd.js', format: 'umd' },
    iife: { ext: 'iife.js', format: 'iife' }
  },
  
  // 平台特定配置
  PLATFORMS: {
    web: {
      external: ['react', 'react-dom', 'antd', '@ant-design/icons', '@ant-design/x'],
      globals: {
        'react': 'React',
        'react-dom': 'ReactDOM',
        'antd': 'antd',
        '@ant-design/icons': 'AntdIcons',
        '@ant-design/x': 'AntdX'
      }
    },
    vue: {
      external: ['vue', 'ant-design-vue', '@ant-design/icons-vue'],
      globals: {
        'vue': 'Vue',
        'ant-design-vue': 'AntDesignVue',
        '@ant-design/icons-vue': 'AntdIconsVue'
      }
    },
    taro: {
      external: ['@tarojs/taro', '@tarojs/components', '@nutui/nutui-react-taro'],
      globals: {
        '@tarojs/taro': 'Taro',
        '@tarojs/components': 'TaroComponents',
        '@nutui/nutui-react-taro': 'NutUI'
      }
    }
  },
  
  // 优化配置
  OPTIMIZATION: {
    // 代码分割阈值
    splitChunks: {
      minSize: 10000,
      maxSize: 50000
    },
    // 压缩配置
    terser: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.warn']
      },
      mangle: {
        reserved: ['React', 'Vue', 'Taro']
      }
    },
    // Bundle 分析
    analyzer: {
      openAnalyzer: false,
      analyzerMode: 'static',
      reportFilename: 'bundle-analysis.html'
    }
  }
}

// 获取包信息
function getPackageInfo(packageName) {
  const packagePath = path.resolve(__dirname, 'packages', packageName)
  const packageJsonPath = path.join(packagePath, 'package.json')
  
  if (!fs.existsSync(packageJsonPath)) {
    throw new Error(`Package ${packageName} not found`)
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
  
  return {
    name: packageName,
    fullName: packageJson.name,
    version: packageJson.version,
    path: packagePath,
    main: packageJson.main,
    module: packageJson.module,
    types: packageJson.types,
    exports: packageJson.exports || {},
    dependencies: packageJson.dependencies || {},
    peerDependencies: packageJson.peerDependencies || {}
  }
}

// 获取所有包信息
function getAllPackages() {
  return BUILD_CONFIG.BUILD_ORDER.map(getPackageInfo)
}

// 生成外部依赖列表
function generateExternals(packageInfo, platform = 'web') {
  const externals = new Set()

  // 添加 peer dependencies
  Object.keys(packageInfo.peerDependencies).forEach(dep => externals.add(dep))

  // 添加平台特定外部依赖
  if (BUILD_CONFIG.PLATFORMS[platform]) {
    BUILD_CONFIG.PLATFORMS[platform].external.forEach(dep => externals.add(dep))
  }

  // 添加内部包依赖
  Object.keys(packageInfo.dependencies).forEach(dep => {
    if (dep.startsWith('@leyaoyao/ai-component-')) {
      externals.add(dep)
    }
  })

  // 过滤掉 CSS 文件，让 Rollup 处理它们
  return Array.from(externals).filter(dep => {
    // 如果是 CSS 文件路径，不作为外部依赖
    return !dep.includes('.css') && !dep.includes('/styles/') && !dep.includes('/dist/katex')
  })
}

// 生成 globals 映射
function generateGlobals(externals, platform = 'web') {
  const globals = {}
  
  // 平台特定 globals
  if (BUILD_CONFIG.PLATFORMS[platform]) {
    Object.assign(globals, BUILD_CONFIG.PLATFORMS[platform].globals)
  }
  
  // 内部包 globals
  externals.forEach(dep => {
    if (dep.startsWith('@leyaoyao/ai-component-')) {
      const packageName = dep.replace('@leyaoyao/ai-component-', '')
      globals[dep] = `AIComponent${packageName.charAt(0).toUpperCase() + packageName.slice(1)}`
    }
  })
  
  return globals
}

// 检查构建环境
function checkBuildEnvironment() {
  const requiredBins = ['node', 'npm']
  const missingBins = []
  
  requiredBins.forEach(bin => {
    try {
      require('child_process').execSync(`${bin} --version`, { stdio: 'ignore' })
    } catch (error) {
      missingBins.push(bin)
    }
  })
  
  if (missingBins.length > 0) {
    throw new Error(`Missing required binaries: ${missingBins.join(', ')}`)
  }
  
  console.log('✅ Build environment check passed')
}

// 获取构建统计信息
function getBuildStats(packageName, outputPath) {
  const stats = {
    package: packageName,
    files: {},
    totalSize: 0
  }
  
  if (fs.existsSync(outputPath)) {
    const files = fs.readdirSync(outputPath)
    files.forEach(file => {
      const filePath = path.join(outputPath, file)
      const stat = fs.statSync(filePath)
      if (stat.isFile()) {
        const size = stat.size
        stats.files[file] = {
          size,
          sizeFormatted: formatBytes(size)
        }
        stats.totalSize += size
      }
    })
  }
  
  stats.totalSizeFormatted = formatBytes(stats.totalSize)
  return stats
}

// 格式化字节数
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

module.exports = {
  BUILD_CONFIG,
  getPackageInfo,
  getAllPackages,
  generateExternals,
  generateGlobals,
  checkBuildEnvironment,
  getBuildStats,
  formatBytes
}